# This file tracks properties of this Flutter project.
# Used by Flutter tool to assess capabilities and perform upgrades etc.
#
# This file should be version controlled and should not be manually edited.

version:
  revision: "78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9"
  channel: "stable"

project_type: app

# Tracks metadata for the flutter migrate command
migration:
  platforms:
    - platform: root
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
    - platform: android
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
    - platform: ios
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
    - platform: linux
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
    - platform: macos
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
    - platform: web
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
    - platform: windows
      create_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9
      base_revision: 78666c8dc57e9f7548ca9f8dd0740fbf0c658dc9

  # User provided section

  # List of Local paths (relative to this file) that should be
  # ignored by the migrate tool.
  #
  # Files that are not part of the templates will be ignored by default.
  unmanaged_files:
    - 'lib/main.dart'
    - 'ios/Runner.xcodeproj/project.pbxproj'
