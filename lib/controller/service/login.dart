import 'dart:convert';

import 'package:mla_connect/controller/home_controller.dart';
import 'package:mla_connect/controller/partyname_controller.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/get_instance.dart';

import 'package:http/http.dart' as http;

import 'package:shared_preferences/shared_preferences.dart';

class LoginService {
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  HomeController homecontroller = HomeController();
  String deviceId = "Unknown Device ID";
  String deviceversioncode = "Unknown Device ID";
  String deviceversion = '';

   PartyName partyName = Get.put(PartyName()) ;

  Future<void> getDeviceId(BuildContext context) async {
    try {
       //print("get device details");
      if (Theme.of(context).platform == TargetPlatform.android) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

        deviceId = androidInfo.id.toString(); // Use androidId as the device ID
        deviceversioncode = androidInfo.version.codename.toString();
        deviceversion = androidInfo.device.toString();
      } else if (Theme.of(context).platform == TargetPlatform.iOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

        deviceId = iosInfo
            .identifierForVendor!; // Use identifierForVendor as the device ID

      }
    } catch (e) {
       //print("Error getting device ID: $e");
    }
  }

  Future<bool> makeLoginRequest(
      String token, String userID, BuildContext context) async {
    await getDeviceId(context);
    print('Token: ${token.substring(0, 10)}...');
    print('User ID: $userID');
    print('Device ID: $deviceId');
    print('Device Version: $deviceversion');
    print('Platform: ${kIsWeb ? "WEB" : "ANDROID"}');
    
    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/user/login?token=$token');

    Map<String, String> headers = {
      "app-user-id": userID,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": kIsWeb ? "WEB" : "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": "116275886745437886835",
    };

    try {
      print('Making API request to: $url');
      print('Headers: $headers');
      final response = await http.post(url, headers: headers);
      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');
      
      if (response.statusCode == 200) {
        Map<String, dynamic> res = jsonDecode(response.body);
        print('API response message: ${res["message"]}');
        
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString('message', res['message'].toString());
        prefs.setString('userId', res['data']['userId']);
        return true;
      } else {
        print('Login failed with status: ${response.statusCode}');
        print('Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      print('Exception in makeLoginRequest: $e');
      return false;
    }
  }

   Future<bool> testLoginRequest(
      String Id, String Password, BuildContext context) async {
    await getDeviceId(context);
     //print(Id) ;
     //print(Password);
     //print(deviceId);
     //print(deviceversion);
     //print(deviceversioncode);
    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/user/login/password?username=$Id&password=$Password');

    Map<String, String> headers = {
      "app-user-id": "google_test",
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": "116275886745437886835",
    };

    try {
      final response = await http.post(url , headers: headers );
       //print(response.body);
      if (response.statusCode == 200) {
      //print("Login successful: ${response.body}");
        Map<String, dynamic> res = jsonDecode(response.body);
         //print(res["message"].toString());

        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString('message', res['message'].toString());
        prefs.setString('userId', res['data']['userId']);

        //  //print(response.body);

        return true;
      } else {
         //print("Login failed with status: ${response.statusCode}");
         //print("Response body: ${response.body}");
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  String extractImageFormat(String filePath) {
    // Split the path by the last dot (.) to separate filename and extension
    int dotIndex = filePath.lastIndexOf('.');

    // Check if there's a dot (valid extension)
    if (dotIndex == -1) {
      return ""; // Return empty string if no extension found
    }

    // Return the substring starting from the dot + 1 (to skip the dot)
    return filePath.substring(dotIndex + 1).toLowerCase();
  }

  Future<Map<String, dynamic>> makeRegistrationRequest(BuildContext context,
      String name, String number, String profileUrl , String dob ,String gender) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;


    String imageFormat = extractImageFormat(profileUrl);
     //print(imageFormat);
    String image = '';
     //print(profileUrl);
    var res = await uploadImage(jwttoken, userId, profileUrl, imageFormat);
    image = res['res'];
     //print("image $image");
    if (image == "Error") {
      return {"status": res["status"]};
    } else {
      // String trimmedValue = name.replaceAll(RegExp(r"\s"), "");
      //  //print(trimmedValue);

  String trimmedText = trimFirstAndLastSpace(name);
   //print(trimmedText);
      final Map<String, dynamic> data = {
        'number': number,
        'name': trimmedText,
        'profileImgUrl': image,
        "dob": dob,
        "gender": gender,
      };

      final Uri url =
          Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/user');
      final Map<String, String> headers = {
        'token': jwttoken,
        // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
        'app-user-id': userId,
        'device-id': deviceId,
        'CLIENT-VERSION': "12",
        "NETA-ID":  "116275886745437886835",
        'CLIENT-TYPE': "ANDROID",
        'CLIENT-VERSION-CODE': deviceversioncode,
        "content-type": "application/json"
      };

      final String body = jsonEncode(data);
      final response = await http.put(url, headers: headers, body: body);
      var result = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // homecontroller.userDetails = result ;
         //print(response.statusCode.runtimeType);
         //print('User updated successfully!');
         //print(result['data']["role"]);
         //print(result['data']["profileImgUrl"]);
         //print(result['data']["name"]);
         //print(result['data']["number"]);
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        // await prefs.setString('role', result['data']["role"]);
        await prefs.setString('profileUrl', result['data']["profileImgUrl"]);
        await prefs.setString('Name', result['data']["name"]);
        await prefs.setString('number', result['data']["number"]);
        await prefs.setString('ROLEUSERAPI', result['data']["role"]);
        await prefs.setString('gender', result['data']["gender"]);
        await prefs.setString('dob', result['data']["dob"]);
         //print(result['data']["gender"]);

         //print(response.body);

        return {"status": response.statusCode};
      } else if (response.statusCode == 403) {
        return {"status": response.statusCode};
      } else {
         //print('Error updating user: ${response.statusCode}');
         //print(response.body);
        return {"status": response.statusCode};
      }
    }
  }
  String trimFirstAndLastSpace(String input) {
     //print(input);
    if (input.isEmpty) {
    return input;
  }

  // Check and remove the first space if it exists
  if (input[0] == ' ') {
    input = input.substring(1);
  }

  // Check and remove the last space if it exists
  if (input.isNotEmpty && input[input.length - 1] == ' ') {
    input = input.substring(0, input.length - 1);
  }
 //print(input);
  return input;
}

  Future<Map<String, dynamic>> EditUserRequest(
      BuildContext context,
      String name,
      String number,
      String dob,
      String gender,
      String profileUrl) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;

    String image = '';
    var res;
    String imageFormat = extractImageFormat(profileUrl);
    res = await uploadImage(jwttoken, userId, profileUrl, imageFormat);
    image = res['res'];
     //print("image $image");
    if (image == "Error") {
      return {"status": res['status']};
    } else {
       String trimmedText = trimFirstAndLastSpace(name);
    //print(trimmedText);
      // String trimmedName = name.trim();
      // String trimmedValue = name.replaceAll(RegExp(r"\s"), "");

      final Map<String, dynamic> data = {
        'number': number,
        'name': trimmedText,
        "profileImgUrl": image,
        "dob": dob,
        "gender": gender,
      };

      final Uri url =
          Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/user');
      final Map<String, String> headers = {
        'token': jwttoken,
        // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
        'app-user-id': userId,
        'device-id': deviceId,
        'CLIENT-VERSION': "12",
        'CLIENT-TYPE': "ANDROID",
        'CLIENT-VERSION-CODE': deviceversioncode,
        "content-type": "application/json"
      };
       //print(data);
      final String body = jsonEncode(data);
      final response = await http.put(url, headers: headers, body: body);
      var result = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // homecontroller.userDetails = result ;
         //print('User updated successfully!');
         //print(result['data']["role"]);
         //print(result['data']["profileImgUrl"]);
         //print(result['data']["name"]);
         //print(result['data']["number"]);
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        // await prefs.setString('role', result['data']["role"]);
        prefs.setString('profileUrl', result['data']["profileImgUrl"]);
        prefs.setString('Name', result['data']["name"]);
        prefs.setString('number', result['data']["number"]);
        prefs.setString('ROLEUSERAPI', result['data']["role"]);
        prefs.setString('gender', result['data']["gender"]);
        prefs.setString('dob', result['data']["dob"]);
         //print('here  ${response.body}');
        return {"status": response.statusCode}; //  //print response body if needed
      } else if (response.statusCode == 403) {
        return {"status": response.statusCode};
      } else {
         //print('Error updating user: ${response.statusCode}');
         //print(response.body);
        return {"status": response.statusCode};
      }
    }
  }

  Future<Map<String, dynamic>> uploadImage(
      jwttoken, userId, profilefile, String imageformat) async {

    var url =
        'https://neta-backend.netaapp.in/neta/files/v1/new?mediaType=IMAGE';
    var token = jwttoken;
    var request = http.MultipartRequest('POST', Uri.parse(url));
    request.headers['media-type'] =
        "image/$imageformat"; // Set media type header
    request.headers['token'] = token;
    request.headers['app-user-id'] = userId;

    var file = await http.MultipartFile.fromPath('file', profilefile);
    request.files.add(file);

    var streamedResponse = await request.send();
    var response = await http.Response.fromStream(streamedResponse);

    if (streamedResponse.statusCode == 200) {
      return {'status': streamedResponse.statusCode, "res": response.body};
    } else {

      return {'status': streamedResponse.statusCode, "res": "Error"};
    }
  }

  Future<Map<String, dynamic>> fetchHomeData(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;

    final url = Uri.parse('https://neta-backend.netaapp.in/neta/feed/v1/home');

     //print(jwttoken);
    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": "116275886745437886835"
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(utf8.decode(response.body.codeUnits));
      if (response.statusCode == 200) {
         //print("Login successful: ${response.body}");
        SharedPreferences prefs = await SharedPreferences.getInstance();
        var userimage = res['userInfo']["profileImgUrl"];
        await prefs.setString('netaName', res['netaInfo']["name"]);
        await prefs.setString('netaImage', res['netaInfo']["profileImgUrl"]);
var naem =await prefs.getString('netaName' );
         //print(res['netaInfo']["name"]);

        await prefs.setString('netaID', res['netaInfo']["userId"]);
        res['userInfo']["boothId"] == null
            ? ""
            : await prefs.setString('boothId', res['userInfo']["boothId"]);

         //print("res  $res");
        return {"status": response.statusCode, "response": res};
      } else {
         //print("home status : ${response.statusCode}");
         //print("Response body: ${response.body}");

        return {"status": response.statusCode, "response": res};
      }
    } catch (e) {
      return {"error": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> karyakartaApproveRequest(
      BuildContext context,
      bool apporove,
      String? number,
      String? booth,
      String? block,
      String kkUserID,
      String levelKK) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
 //print(block  );
 //print(booth) ;
    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/user/karyakarta');

    Map<String, String> headers = {
      'Content-Type': 'application/json',
      'token': jwttoken,
      // 'eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTE2NDcwMzAsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTc0MzE4MzAzMH0.H5aFtnMJv9_OopYYbNuvcpz9q5NhhKvfrDfQg2Hm4Tk',
      'app-user-id': userId,
      'device-id': '12233',
      'CLIENT-VERSION': '12',
      'NETA-ID': netaID,
      'CLIENT-TYPE': 'ANDROID',
      'CLIENT-VERSION-CODE': '122333',
    };

    Map<String, dynamic> body = {
      "userId": kkUserID,
      "approved": true,
      "karyakartaLevel": levelKK,
      "blockId": block,
      "boothId": booth
    };

    final response = await http.put(
      url,
      headers: headers,
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
       //print("User updated successfully");
       //print(response.body);
      return {"status": response.statusCode};
    } else {
       //print("Failed to update user. Status code: ${response.statusCode}");
       //print("Response body: ${response.body}");
      return {"status": response.statusCode};
    }
  }

  Future<Map<String, dynamic>> AssignkaryakartaToManager(
      BuildContext context,
      String? number,
      String? booth,
      String? block,
      String kkUserID,
      String levelKK) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;

    final url =
        Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/karyakarta');

    Map<String, String> headers = {
      'Content-Type': 'application/json',
      'token': jwttoken,
      // 'eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTE2NDcwMzAsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTc0MzE4MzAzMH0.H5aFtnMJv9_OopYYbNuvcpz9q5NhhKvfrDfQg2Hm4Tk',
      'app-user-id': userId,
      'device-id': '12233',
      'CLIENT-VERSION': '12',
      'NETA-ID': netaID,
      'CLIENT-TYPE': 'ANDROID',
      'CLIENT-VERSION-CODE': '122333',
    };

    Map<String, dynamic> body = {
      "phoneNumber": number,
      "karyakartaLevel": levelKK,
      "blockId": booth,
      "bothId": block
    };

    final response = await http.put(
      url,
      headers: headers,
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
       //print("User updated successfully");
       //print(response.body);
      return {"status": response.statusCode};
    } else {
       //print("Failed to update user. Status code: ${response.statusCode}");
       //print("Response body: ${response.body}");
      return {"status": response.statusCode};
    }
  }

  Future<Map<String, dynamic>> karyakartarejectRequest(
      BuildContext context, bool apporove ,String id) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
    Map<String, dynamic> data = {
      "userId": id,
      "approved": false,
    };

    final Uri url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/user/karyakarta');
    final Map<String, String> headers = {
      'token': jwttoken,
      // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
      'app-user-id': userId,
      'device-id': deviceId,
      'CLIENT-VERSION': "12",
      'NETA-ID': netaID,
      'CLIENT-TYPE': "ANDROID",
      'CLIENT-VERSION-CODE': deviceversioncode,
      "content-type": "application/json"
    };
    var datare = jsonEncode(data);
     //print(datare);
    final response = await http.put(url, headers: headers, body: datare);
    var result = jsonDecode(response.body);

    if (response.statusCode == 200) {
      // homecontroller.userDetails = result ;
       //print('karyakarta rejected successfully!');

       //print('here  ${response.body}');
      return {"status": response.statusCode};
    } else {
       //print('Error updating user: ${response.statusCode}');
       //print(response.body);
      return {"status": response.statusCode};
    }
  }

  Future<Map<String, dynamic>> karyakartaLevelAssignRequest(
      BuildContext context,
      String Number,
      String BoothNumber,
      String block) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String netaID = prefs.getString('netaID')!;

    String userId = prefs.getString('userId')!;
    final Uri url =
        Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/karyakarta');
    final Map<String, String> headers = {
      'token': jwttoken,
      // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
      'app-user-id': userId,
      'device-id': deviceId,
      'CLIENT-VERSION': "12",
      'NETA-ID': netaID,
      'CLIENT-TYPE': "ANDROID",
      'CLIENT-VERSION-CODE': deviceversioncode,
      "content-type": "application/json"
    };

    Map<String, dynamic> data = {
      "phoneNumber": Number,
      "karyakartaLevel": "BOOTH",
      "bothId": BoothNumber,
      "blockId": block,
    };
    var datare = jsonEncode(data);
    final response = await http.put(url, headers: headers, body: datare);
    var result = jsonDecode(response.body);

    if (response.statusCode == 200) {
      // homecontroller.userDetails = result ;
       //print('karyakarta assigned!');

       //print('here  ${response.body}');
      return {"status": response.statusCode}; //  //print response body if needed
    } else {
       //print('Error updating user: ${response.statusCode}');
       //print(response.body);
      return {"status": response.statusCode};
    }
  }
 Future<Map<String, dynamic>> postPartyList(BuildContext context , List<dynamic> partyList) async {
   await getDeviceId(context);

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
  String netaID = prefs.getString('netaID')!;
    String userId = prefs.getString('userId')!;

     //print(partyList);
  String baseUrl = 'https://neta-backend.netaapp.in/neta/user/v1/user/parties';

  String finalUrl = buildUrl(baseUrl, partyList);
 //print(finalUrl);
  final response = await http.post(
    Uri.parse(finalUrl),
    headers:  {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    },
  );
if(response.statusCode ==200){
   //print(response.body);
return {"status": response.statusCode} ;
}else {
return {"status": response.statusCode} ;
}

 }

String buildUrl(String baseUrl, List<dynamic> partyList) {
  if (partyList.isEmpty) {
    return baseUrl;
  }

  String finalUrl = baseUrl + '?parties=${partyList[0]}';

  for (int i = 1; i < partyList.length; i++) {
    finalUrl += '&parties=${partyList[i]}';
  }

  return finalUrl;
}
  Future<Map<String, dynamic>> fetchConfig(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;

    final url =
        Uri.parse('https://neta-backend.netaapp.in/neta/config/v1/config');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": "116275886745437886835",
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
        //  //print("Login successful: ${response.body}");

         //print("res  $res");
        partyName.paryname = res['partyList'];
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('role', res["role"]);

        // ignore: unnecessary_statements
        res['role'] == "KARYAKARTA"
            ? await prefs.setString('kkrequest', "false")
            : 'null';
        return res;
      } else {
         //print("config status : ${response.statusCode}");
         //print("Response body: ${response.body}");

        return res;
      }
    } catch (e) {
      return {"error": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> fetchVoterDetails(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;

    final url =
        Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/voter/booth');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
        //  //print("Login successful: ${response.body}");

         //print("voterDetails  $res");

        return {'status': response.statusCode, "res": res};
      } else {
         //print("config status : ${response.statusCode}");
         //print("Response body: ${response.body}");

        return {'status': response.statusCode, "res": res};
      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> fetchNetaVoterDetails(
      BuildContext context, String boothNo, String blockNo) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;

    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/voter/booth?boothNumber=$boothNo&blockId=$blockNo');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };
 print(headers);
    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
     //print("Login successful: ${response.body}");

        return {'status': response.statusCode, "res": res};

      } else {
        return {'status': response.statusCode, "res": res};

      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> makeCreateRequest(
      BuildContext context, String profileUrl, String caption) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String netaID = prefs.getString('netaID')!;
    String userId = prefs.getString('userId')!;
    String imageFormat = extractImageFormat(profileUrl);
    String image = '';
    var res;
    res = await uploadImage(jwttoken, userId, profileUrl, imageFormat);
    image = res['res'];
     //print("image $image");
    if (image == "Error") {
      return {"status": res['status']};
    } else {
      final Map<String, dynamic> data = {"url": image, "caption": caption};

      final Uri url =
          Uri.parse('https://neta-backend.netaapp.in/neta/post/v1/poster');
      final Map<String, String> headers = {
        'token': jwttoken,
        'app-user-id': userId,
        'device-id': deviceId,
        'CLIENT-VERSION': "12",
        'NETA-ID': netaID,
        'CLIENT-TYPE': "ANDROID",
        'CLIENT-VERSION-CODE': deviceversioncode,
        "content-type": "application/json"
      };
       //print(headers);
       //print(data);
      final String body = jsonEncode(data);
      final response = await http.post(url, headers: headers, body: body);

      if (response.statusCode == 200) {
         //print("create image");
         //print(response.body);
        return {"status": response.statusCode}; //  //print response body if needed
      } else {
         //print('Error updating user: ${response.statusCode}');
         //print(response.body);
        return {"status": response.statusCode};
      }
    }
  }

  Future<Map<String, dynamic>> getKaryaKartaRequest(
      BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
     //print(jwttoken);

    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/user/karyakarta?cursor=');

    // final url = Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/user/karyakarta');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
          //print("Login successful: ${response.body}");

        return {'status': response.statusCode, "res": res};
        ;
      } else {
        return {'status': response.statusCode, "res": res};
        ;
      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> karyakartaRequest(BuildContext context,
      String name, String number, String epicNo, String refrenceName) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
    var url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/user/karyakarta');

    Map<String, String> headers = {
      'token': jwttoken,
      // 'eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDg1NjQ1NzUzNzY5MjAzNDczMTkiLCJpYXQiOjE3MTA4NDIyNTcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA4NTY0NTc1Mzc2OTIwMzQ3MzE5In0sImV4cCI6MTcxMDkyODY1N30.9YzejemyTJT_JN5MJOA5Cej-BOw3-vOnljuasXXQ9CA',
      'app-user-id': userId,
      'device-id': '12233',
      'CLIENT-VERSION': '12',
      'NETA-ID': netaID,
      'CLIENT-TYPE': 'ANDROID',
      'CLIENT-VERSION-CODE': '122333',
      'Content-Type': 'application/json',
    };

    String trimmedValue = name.replaceAll(RegExp(r"\s"), "");
    String epicNot = epicNo.replaceAll(RegExp(r"\s"), "");
    String refrenceNamet = refrenceName.replaceAll(RegExp(r"\s"), "");

    Map<String, String> body = {
      "name": trimmedValue,
      "number": number,
      "epicNumber": epicNot,
      "referenceName": refrenceNamet
    };

    // Encode the body to JSON
    String jsonBody = json.encode(body);

    // Make POST request
    var response = await http.post(
      url,
      headers: headers,
      body: jsonBody,
    );
    if (response.statusCode == 200) {
       //print(response.body);
        //print(response.statusCode);
      return {"status": response.statusCode};
    } else {
       //print(response.body);
      return {"status": response.statusCode};
    }
  }

  Future<Map<String, dynamic>> deleteposter(
      BuildContext context, String posterId) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
     //print(posterId);

    final Uri url = Uri.parse(
        "https://neta-backend.netaapp.in/neta/post/v1/poster/$posterId");
    final Map<String, String> headers = {
      'token': jwttoken,
      // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
      'app-user-id': userId,
      'device-id': deviceId,
      'CLIENT-VERSION': "12",
      'NETA-ID': netaID,
      'CLIENT-TYPE': "ANDROID",
      'CLIENT-VERSION-CODE': deviceversioncode,
      "content-type": "application/json"
    };
 //print(headers);
    final response = await http.delete(url, headers: headers);
//  var  result = jsonDecode(response.body) ;

    if (response.statusCode == 200) {

       //print(response.body);
      return {"status": response.statusCode}; //  //print response body if needed
    } else {
       //print('Error updating user: ${response.statusCode}');
       //print(response.body);
      return {"status": response.statusCode};
    }
  }

  Future<Map<String, dynamic>> deleteuser(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String netaID = prefs.getString('netaID')!;
    String userId = prefs.getString('userId')!;
     //print(userId);
     //print(jwttoken);
     //print(netaID);
    final url = Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/user');

  final headers = {
    'token': jwttoken ,
    // 'eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDIwNDI1NTQ4Mzc1MjE3Mjg5NDIiLCJpYXQiOjE3MTU3OTkyNzQsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTAyMDQyNTU0ODM3NTIxNzI4OTQyIn0sImV4cCI6MTc0NzMzNTI3NH0.DfHvOVNOQxl57_LpYsuXarrLNNX66QexnfNrsy21tkE',
    'app-user-id': userId,
    'device-id': '12233',
    'CLIENT-VERSION': '12',
    'NETA-ID': netaID,
    'CLIENT-TYPE': 'ANDROID',
    'CLIENT-VERSION-CODE': '122345',
  };
   //print(headers);
  final response = await http.delete(url, headers: headers);

  if (response.statusCode == 200) {
     //print('User deleted successfully');
     //print(response.body);
      return {"status": response.statusCode};

  } else {
     //print('Failed to delete user. Status code: ${response.statusCode}');
     //print('Response body: ${response.body}');
    return {"status": response.statusCode};
  }
    // final url = Uri.parse('https://neta-backend.netaapp.in/neta/user/v1/user');
    // final headers = {
    //    "token": jwttoken,
    //   "app-user-id": userId,
    //   "device-id": deviceId,
    //   "CLIENT-VERSION": deviceversion,
    //   "CLIENT-TYPE": "ANDROID",
    //   "CLIENT-VERSION-CODE": "12",
    //   "NETA-ID": netaID,
    //   };

    // final response = await http.delete(
    //   url,
    //   headers: headers,
    // );
    //  //print(response.body);
    // if (response.statusCode == 200) {
    //    //print('Request successful');
    //   return {"status": response.statusCode};
    // } else {
    //    //print('Request failed with status: ${response.statusCode}');
    //    //print('Response body: ${response.body}');
    //   return {"status": response.statusCode};
    // }
  }

  Future<Map<String, dynamic>> karyakartaList(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
     //print(jwttoken);
    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/karyakarta?level=BOOTH');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
         //print("res  $res");
        return {'status': response.statusCode, "res": res};
      } else {
         //print("home status : ${response.statusCode}");
         //print("Response body: ${response.body}");

        return {'status': response.statusCode, "res": res};
      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }


  }

  Future<Map<String, dynamic>> deleteKaryaKarta(
      BuildContext context, String userIDs) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;

    String userId = prefs.getString('userId')!;

    String netaID = prefs.getString('netaID')!;
     //print(userIDs);
    final Uri url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/karyakarta?userId=$userIDs');
    final Map<String, String> headers = {
      'token': jwttoken,
      // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
      'app-user-id': userId,
      'device-id': deviceId,
      'CLIENT-VERSION': "12",
      'NETA-ID': netaID,
      'CLIENT-TYPE': "ANDROID",
      'CLIENT-VERSION-CODE': deviceversioncode,
      "content-type": "application/json"
    };

    final response = await http.delete(url, headers: headers);

    if (response.statusCode == 200) {
       //print("delete karyakarta");
       //print(response.body);
      return {"status": response.statusCode}; //  //print response body if needed
    } else {
       //print('Error updating user: ${response.statusCode}');
       //print(response.body);
      return {"status": response.statusCode};
    }
  }

  Future<Map<String, dynamic>> updatePoster(
      BuildContext context, String posterID) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String netaID = prefs.getString('netaID')!;

    String userId = prefs.getString('userId')!;
    String url =
        'https://neta-backend.netaapp.in/neta/post/v1/post/share?postId=$posterID';
    Map<String, String> headers = {
      'token': jwttoken,
      // 'eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMTExNjQ0Nzc4MTIxMTIyOTY5MjkiLCJpYXQiOjE3MTE2OTYxNzksIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTExMTY0NDc3ODEyMTEyMjk2OTI5In0sImV4cCI6MTc0MzIzMjE3OX0.cxOgQ2cmR-rwI4JlWqFGZwKETi8LrCN51Vq44J7sxpo',
      'device-id': deviceId,
      'neta-id': netaID,
      'app-user-id': userId,
    };

    try {
      http.Response response = await http.put(
        Uri.parse(url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return {"status": response.statusCode};
      } else {
        return {"status": response.statusCode};
      }
    } catch (e) {
      return {"status": e};
    }
  }

  Future<Map<String, dynamic>> voterEditionalDataPost(
      BuildContext context,
      String votingFor,
      int familyCount,
      bool influencer,
      bool beneficiary,
      String beneficiaryType,
      String mobileNumber,
      bool dead,
      bool lastVoted,
      bool currentlyPresent,
      String voterEpicID,
      bool responsibility,
      String lastVotedSelected) async {
         //print("yH");
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
 //print("11");
    Map<String, dynamic> data = {
      "votingFor": votingFor,
      "familyCount": familyCount,
      "influencer": influencer,
      "beneficiary": beneficiary,
      "responsible": responsibility,
      "mobileNumber": mobileNumber,
      "dead": dead,
      "lastVoted": lastVoted,
      "lastVotedFor": lastVotedSelected=="Select Option"?"":lastVotedSelected,
      "currentlyPresent": currentlyPresent
    };
 //print(data);
    final Uri url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/user/v1/voter/$voterEpicID');
     //print(url);
    final Map<String, String> headers = {
      'token': jwttoken,
      // "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMDU0MjkwMzUyOTkwNTI0MTQ2ODEiLCJpYXQiOjE3MTAxNjc1MjcsIlVTRVJfQ0xBSU0iOnsidXNlcklkIjoiMTA1NDI5MDM1Mjk5MDUyNDE0NjgxIn0sImV4cCI6MTcxMDI1MzkyN30.nvNNwY6XEbYI8NXhLM5FUy7KchG3VtwjExnPEeDpsR8",
      'app-user-id': userId,
      'device-id': deviceId,
      'CLIENT-VERSION': "12",
      'NETA-ID': netaID,
      'CLIENT-TYPE': "ANDROID",
      'CLIENT-VERSION-CODE': deviceversioncode,
      "content-type": "application/json"
    };
    var datare = jsonEncode(data);
     //print(datare);
    final response = await http.put(url, headers: headers, body: datare);
    var result = response.body;

    if (response.statusCode == 200) {
      // homecontroller.userDetails = result ;
       //print('voter updated successfully!');

       //print('voter  ${response.body}');

      return {"status": response.statusCode}; //  //print response body if needed
    } else {
       //print('Error updating user: ${response.statusCode}');
       //print(response.body);
      return {"status": response.statusCode};
    }

  }

  Future<Map<String, dynamic>> karyakartaAnalytics(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
    final url =
        Uri.parse('https://neta-backend.netaapp.in/neta/analytics/v1/kk');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
         //print("kk Analytics  $res");
        return {'status': response.statusCode, "res": res};
      } else {
         //print("home status : ${response.statusCode}");
         //print("Response body: ${response.body}");

        return {'status': response.statusCode, "res": res};
      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> netaAnalytics(BuildContext context) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
    final url =
        Uri.parse('https://neta-backend.netaapp.in/neta/analytics/v1/neta');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {
         //print("kk Analytics  $res");
        return {'status': response.statusCode, "res": res};
      } else {
         //print("home status : ${response.statusCode}");
         //print("Response body: ${response.body}");

        return {'status': response.statusCode, "res": res};
      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }
  }

  Future<Map<String, dynamic>> netaboothAnalytics(
      BuildContext context, String boothnumber) async {
    await getDeviceId(context);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String jwttoken = prefs.getString('message')!;
    String userId = prefs.getString('userId')!;
    String netaID = prefs.getString('netaID')!;
    final url = Uri.parse(
        'https://neta-backend.netaapp.in/neta/analytics/v1/neta/booth?boothId=$boothnumber');

    Map<String, String> headers = {
      "token": jwttoken,
      "app-user-id": userId,
      "device-id": deviceId,
      "CLIENT-VERSION": deviceversion,
      "CLIENT-TYPE": "ANDROID",
      "CLIENT-VERSION-CODE": "12",
      "NETA-ID": netaID,
    };

    try {
      final response = await http.get(url, headers: headers);
      Map<String, dynamic> res = jsonDecode(response.body);
      if (response.statusCode == 200) {

        return {'status': response.statusCode, "res": res};

      } else {

        return {'status': response.statusCode, "res": res};

      }
    } catch (e) {
      return {'status': 400, "res": "Something went wrong"};
    }
  }
}
