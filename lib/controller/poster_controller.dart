import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/PosterV2.dart';
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';

class PosterController extends GetxController {
  RxList<PosterNetworkModelV2> posters = <PosterNetworkModelV2>[].obs;
  // RxList<PosterNetworkModel> posters = <PosterNetworkModel>[].obs;

  final refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  getData() async {
    try {
      final result = await FirebaseCloud.instance.fetchPoster(null);
      posters.addAll(result.posters);
    } catch (e) {
       //print(e);
    } finally {}
  }

  PosterController() {
    getData();
  }

  deletePoster(id) {
    posters.removeWhere((element) => element.id == id);
  }

  addPoster(PosterNetworkModelV2 poster) {
    posters.add(poster);
  }
}
