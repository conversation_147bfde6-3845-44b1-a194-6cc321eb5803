import 'dart:async';

import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/RoomList.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/database_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
 
import 'package:get/state_manager.dart';
import 'package:mla_connect/models/ChatMessage.dart';
import 'package:get/get.dart';

class ChatRoomController extends GetxController {
  List<RoomList> roomListsData = <RoomList>[];
  RxList<RoomList> oldRoomListsData = <RoomList>[].obs;

  RxBool _noRooms = false.obs;

  ChatRoomController() {
    getData();
  }

  fetchRefreshedData() async {
    List<RoomList> openDB = await DatabaseHelper.instance.queryAllRows();

    oldRoomListsData.clear();
    oldRoomListsData.addAll(openDB);
    getSortedRoomList();
  }

  getData() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return;
    }
    oldRoomListsData.clear();
    List<RoomList> openDB = await DatabaseHelper.instance.queryAllRows();
    oldRoomListsData.addAll(openDB);
    getSortedRoomList();
    DashboardResponse d = await FirebaseCloud.instance.getDashboardData();

    final roomsQuery = FirebaseFirestore.instance
        .collection('rooms')
        .where('access', arrayContains: d.profile.id)
        .orderBy("lastMessageTimestamp", descending: true);

    final unreadMessageMapQuery = FirebaseFirestore.instance
        .collection('unreadMessages')
        .doc(d.profile.id);

    final futures1 = [
      roomsQuery.get(),
      unreadMessageMapQuery.get(),
    ];
    final results = await Future.wait(futures1);
    QuerySnapshot<Map<String, dynamic>> rooms =
        results[0] as QuerySnapshot<Map<String, dynamic>>;
    DocumentSnapshot<Map<String, dynamic>> unreadMessageMap =
        results[1] as DocumentSnapshot<Map<String, dynamic>>;

    if (rooms.docs.isEmpty) {
      _noRooms.value = true;
      return;
    }
    for (int i = 0; i < rooms.docs.length; i++) {
      int? lastTimestamp;
      if (unreadMessageMap.data() != null &&
          unreadMessageMap.data()![rooms.docs[i].id] != null) {
        lastTimestamp = unreadMessageMap.data()![rooms.docs[i].id];
      }
      final unseenMsgCountQuery = FirebaseFirestore.instance
          .collection('rooms')
          .doc(rooms.docs[i].id)
          .collection('messages')
          .orderBy('timeStamp')
          .where('timeStamp', isGreaterThan: lastTimestamp)
          .count();

      final lastMessageQuery = FirebaseFirestore.instance
          .collection('rooms')
          .doc(rooms.docs[i].id)
          .collection('messages')
          .orderBy('timeStamp', descending: true)
          .limit(1);

      final futures1 = [
        unseenMsgCountQuery.get(),
        lastMessageQuery.get(),
      ];
      final results1 = await Future.wait(futures1);
      AggregateQuerySnapshot unseenMsgCount =
          results1[0] as AggregateQuerySnapshot;
      QuerySnapshot<Map<String, dynamic>> lastMessage =
          results1[1] as QuerySnapshot<Map<String, dynamic>>;
      roomListsData.add(  RoomList(
          name: rooms.docs[i].data()['name'],
          roomId: rooms.docs[i].id,
          unseenCount: unseenMsgCount.count,
          lastMessage: ChatMessage.fromJson(lastMessage.docs[0].data()),
          lastMessageTimestamp: rooms.docs[i].data()['lastMessageTimestamp']));
    }
    oldRoomListsData.clear();
    oldRoomListsData.addAll(roomListsData);
    getSortedRoomList();
    await DatabaseHelper.instance.clearTable();
    await DatabaseHelper.instance.updateAllRows(roomListsData);
    List<String> roomIds = [];
    if (rooms.docs.isNotEmpty) {
      rooms.docs.forEach((element) {
        roomIds.add(element.id);
      });
    }
    for (String roomId in roomIds) {
      await FirebaseMessaging.instance.subscribeToTopic('room_$roomId');
    }
  }

  updateRoomFromNotification(ChatMessage msg) async {
    final storedData = await DatabaseHelper.instance.queryById(msg.recipientId);
    if (storedData != null && storedData.unseenCount != null) {
       //print("time stamps");
       //print(storedData.lastMessageTimestamp);
       //print(msg.timeStamp);
      if (storedData.lastMessageTimestamp >= msg.timeStamp) {
        return;
      }

      RoomList myData =   RoomList(
          name: msg.recipientName,
          roomId: msg.recipientId,
          lastMessage: msg,
          lastMessageTimestamp: msg.timeStamp,
          unseenCount: storedData.unseenCount! + 1);

      for (int i = 0; i < oldRoomListsData.length; i++) {
        if (oldRoomListsData[i].roomId == msg.recipientId) {
          oldRoomListsData[i] = myData;
          break;
        }
      }
      await DatabaseHelper.instance.update(myData);
       //print("HERE IT IS");
       //print(myData.toJson());
       //print(oldRoomListsData[0].toJson());
    } else {
      RoomList myData =   RoomList(
          name: msg.recipientName,
          roomId: msg.recipientId,
          lastMessage: msg,
          unseenCount: 1,
          lastMessageTimestamp: msg.timeStamp);
      await DatabaseHelper.instance.update(myData);
      for (int i = 0; i < oldRoomListsData.length; i++) {
        if (oldRoomListsData[i].roomId == msg.recipientId) {
          oldRoomListsData[i] = myData;
          break;
        }
      }
    }
    getSortedRoomList();

// oldRoomListsData.

//     ValueBuilder<bool>(
//       initialValue: false,
//       builder: (value, updateFn) => Switch(
//         value: value,
//         onChanged:
//             updateFn, // same signature! you could use ( newValue ) => updateFn( newValue )
//       ),
//       // if you need to call something outside the builder method.
//       onUpdate: (value) =>  //print("Value updated: $value"),
//       onDispose: () =>  //print("Widget unmounted"),
//     );
  }

  updateLastSentMessage(ChatMessage msg) async {
    var temp;
    var pos;
    for (int i = 0; i < oldRoomListsData.length; i++) {
      if (oldRoomListsData[i].roomId == msg.recipientId) {
        pos = i;
        // oldRoomListsData[i]
        temp = RoomList(
            name: msg.recipientName,
            roomId: msg.recipientId,
            lastMessage: msg,
            unseenCount: 0,
            lastMessageTimestamp: msg.timeStamp);
        //  //print(pos);
        //  //print(temp);
        //  //print("---");
        //  //print(temp);

        //  //print("UPDATINGss");
        // if (pos != null && temp != null) {
        //   oldRoomListsData[pos] = temp;
        // }
      }
    }
    await DatabaseHelper.instance.update(new RoomList(
        name: msg.recipientName,
        roomId: msg.recipientId,
        lastMessage: msg,
        unseenCount: 0,
        lastMessageTimestamp: msg.timeStamp));
    if (pos != null && temp != null) {
      oldRoomListsData[pos] = temp;
    }
     //print("UPDATED LOCAL DB NOW");
    getSortedRoomList();
  }

  getSortedRoomList() {
    List<RoomList> sortedList = List.from(oldRoomListsData);
    sortedList.sort(
        (b, a) => a.lastMessageTimestamp.compareTo(b.lastMessageTimestamp));
    oldRoomListsData.value = sortedList;
  }
}
