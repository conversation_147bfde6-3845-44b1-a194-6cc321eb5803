import 'package:mla_connect/new_ui/cadre/cadreOptions.dart';
import 'package:mla_connect/screens/voters/voter_search.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/state_manager.dart';
 
import 'package:shared_preferences/shared_preferences.dart';

class DownloadingDataController extends GetxController {
  RxDouble _currentDownloaded = 0.0.obs;
  RxBool downloading = false.obs;
  RxBool dataDownloaded = false.obs;

  DownloadingDataController() {
    checkCandidatesDownloaded();
  }

  setCurtrrentProgress(double count) {
    _currentDownloaded.value = (count / 1500) * 100;
     //print("CURRENT % is ${_currentDownloaded.toString()}");
  }

  get getCurrentProgress => _currentDownloaded.value;
  setCandidatesDownloadDataCompleted() async {
    final pref = await SharedPreferences.getInstance();
    await pref.setBool("candidates_downloaded", true);

    dataDownloaded.value = true;
  }

  Future<bool> checkCandidatesDownloaded() async {
    final pref = await SharedPreferences.getInstance();
    var value = pref.getBool('candidates_downloaded');
     //print("Vdsfds");
     //print(value);
    if (value != null && value) {
      dataDownloaded.value = true;
      return true;
    }
    return false;
  }

  navigatePage(context) {
    dataDownloaded.value
        ? context.pushAndRemoveWidget(
            (context) => VoterSearchPage(), (route) => false)
        : context.pushAndRemoveWidget(
            (context) => CadreOptions(), (route) => false);
  }
}
