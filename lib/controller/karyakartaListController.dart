import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/karyakarta.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/state_manager.dart';

class KaryakartaListController extends GetxController {
  Rx<KaryakartaList>? karyakartaList;
  KaryakartaListController() {
    getKaryakartaList();
  }
  getKaryakartaList() async {
    karyakartaList = (await FirebaseCloud.instance.getAllkaryakartas()).obs;
  }

  addPerson() async {
    var d = (await FirebaseCloud.instance.getAllkaryakartas());
    karyakartaList!.value = d;
    karyakartaList!.refresh();
    update();
  }
}
