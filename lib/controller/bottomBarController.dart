import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/state_manager.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
 

class BottomBarController extends GetxController {
  // RxInt _currentPage = 0.obs;

  Rx<PersistentTabController> controller =
      PersistentTabController(initialIndex: 0).obs;

  setCurrentPage(int pageNumber) {
    // _currentPage = pageNumber.obs;
    controller.value.jumpToTab(pageNumber);
  }

  List<dynamic> userLeaderBoard=[];
}
