import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/state_manager.dart';

class RewardController extends GetxController {
  RxInt reward_points = 0.obs;

  setRewardPoint(int point) {
    reward_points.value = point;
    reward_points.refresh();
     //print("Updating");
     //print(reward_points);
  }

  getRewardPoint() {
    return reward_points;
  }

  increment_rewardPoint(int points) {
    reward_points.value = (points + reward_points.value);
  }
}
