import 'package:mla_connect/controller/chatRoomController.dart';
import 'package:mla_connect/new_ui/home/<USER>';

import 'package:mla_connect/new_ui/onboarding/phoneInput.dart';
import 'package:mla_connect/screens/onBoarding_page.dart';

import 'package:mla_connect/utils/constants.dart';

import 'package:facebook_app_events/facebook_app_events.dart';

import 'package:firebase_auth/firebase_auth.dart' as _fbAuth;
import 'package:firebase_core/firebase_core.dart';
import 'package:mla_connect/firebase_options.dart';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/route_manager.dart';

import 'package:get/instance_manager.dart';
import 'package:mla_connect/models/ChatMessage.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.dark,
    ),
  );
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]).then(
    (_) {
      GestureBinding.instance.resamplingEnabled = true; // Set this flag.

      runApp(GetMaterialApp(home: MyApp()));
    },
  );
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

// Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   if (message.data['notif_type'] != 'chat') {
//     return;
//   }
//   await Firebase.initializeApp();
//    await FirebaseAppCheck.instance
//       // Your personal reCaptcha public key goes here:
//       .activate(
//     androidProvider: AndroidProvider.debug,
//     appleProvider: AppleProvider.debug,

//   );
//   // ChatRoomController _chatController = Get.isRegistered<ChatRoomController>()
//   //     ? Get.find<ChatRoomController>()
//   //     : Get.put(ChatRoomController());

//   // final test = Get.isRegistered<ChatRoomController>;

//   ChatRoomController _chatController = ChatRoomController();

//   message.data['timeStamp'] = int.parse(message.data['timeStamp']);
//   try {
//     _fbAuth.User? user = _fbAuth.FirebaseAuth.instance.currentUser;
//     if (user?.uid != ChatMessage.fromJson(message.data).senderId) {
//       _chatController
//           .updateRoomFromNotification(ChatMessage.fromJson(message.data));
//     }
//   } catch (e) {}
// }

class _MyAppState extends State<MyApp> {
  String? _error;

  bool goggleLoggeIn = false;
  bool isRegistered = false;

  // Define an async function to initialize FlutterFire
  void initializeFlutterFire() async {
    try {
      // Firebase is already initialized in main()
      await FirebaseMessaging.instance.getToken();

      _fbAuth.User? user = _fbAuth.FirebaseAuth.instance.currentUser;

      //retrieve Dynamic Link

      // final PendingDynamicLinkData? initialLink =
      //     await FirebaseDynamicLinks.instance.getInitialLink();
      // if (initialLink != null) {
      //   final Uri deepLink = initialLink.link;
      //   // Example of using the dynamic link to push the user to a different screen
      //   // Navigator.pushNamed(context, deepLink.path);
      //   String referedId = deepLink.queryParametersAll['id']?[0] ?? "";
      //   if (referedId != "") setReferedId(referedId);
      // }

      // FirebaseDynamicLinks.instance.onLink.listen(
      //   (pendingDynamicLinkData) {
      //     // Set up the `onLink` event listener next as it may be received here
      //     if (pendingDynamicLinkData != null) {
      //       final Uri deepLink = pendingDynamicLinkData.link;
      //       String referedId = deepLink.queryParametersAll['id']?[0] ?? "";
      //       if (referedId != "") setReferedId(referedId);
      //     }
      //   },
      // );

//-----
//  RewardController? _rewardController = Get.isRegistered<RewardController>()
//         ? Get.find<RewardController>()
//         : Get.put(RewardController());
      ChatRoomController _chatController =
          Get.isRegistered<ChatRoomController>()
              ? Get.find<ChatRoomController>()
              : Get.put(ChatRoomController());
      try {
        // FirebaseMessaging.onBackgroundMessage(
        //     firebaseMessagingBackgroundHandler);
        // // FirebaseMessaging.instance.

        FirebaseMessaging.onMessage.listen((RemoteMessage message) {
          if (message.data['notif_type'] != 'chat') {
            return;
          }
          message.data['timeStamp'] = int.parse(message.data['timeStamp']);
          if (user?.uid != ChatMessage.fromJson(message.data).senderId)
            _chatController
                .updateRoomFromNotification(ChatMessage.fromJson(message.data));
          // Handle the FCM message data, update the local room list data,
          // and refresh the UI to display the updated information.
        });
      } catch (e) {}
//----
      // if (await checkUserBoarded()) {
      //   setState(() {
      //     _onBoarded = true;
      //   });
      // }
      // setState(() {
      //   if (user != null) _authorized = true;
      //   _initialized = true;
      // });
    } catch (e) {
      // Set `_error` state to true if Firebase initialization fails
      setState(() {
        _error = e.toString();
      });
    }
  }

  final facebookEvents = FacebookAppEvents();
  @override
  void initState() {
    super.initState();
    initializeFlutterFire();
    // checkOnboarding() ;

    _initializeScreen();
//  Future.delayed(Duration.zero, () async {
//       await facebookEvents.logCompletedRegistration();
//     });
  }

  Widget _screenWidget = CircularProgressIndicator();
  void _initializeScreen() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? userId = prefs.getString('userId');
    String? role = prefs.getString('role');

    // if (_error != null) {
    //   _screenWidget = Scaffold(
    //     body: Center(
    //       child: Padding(
    //         padding: const EdgeInsets.all(16.0),
    //         child: Text(_error!),
    //       ),
    //     ),
    //   );
    // }
    if (userId != null) {
      if (role != null) {
        _screenWidget = MyBottomBar();
      } else {
        _screenWidget = OnBoarding();
      }
    } else {
      _screenWidget = PhoneInput();
    }

    if (mounted) {
      setState(() {
        _screenWidget = _screenWidget;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        title: APP_NAME,
        // theme: AppTheme.lightTheme(),
        home: _screenWidget
 );
  }
}
