import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/state_manager.dart';

class UnseenMsgController extends GetxController {
  RxMap<String, dynamic> _unseenMsgMap = <String, dynamic>{}.obs;
  RxBool loaded = false.obs;
  setUnseenMsgMap(Map<String, dynamic> map) {
    _unseenMsgMap.value = map;
    loaded.value = true;
    refresh();
  }

  updateUnseenMsgMap(String key, value) {
    // var temp = _unseenMsgMap;
    // temp[]
    //  //print("UPDATING VALUE IS");
    //  //print(_unseenMsgMap);
    _unseenMsgMap[key] = value;
  }

  RxMap<String, dynamic> get getUnseenMsgMap => _unseenMsgMap;
}
