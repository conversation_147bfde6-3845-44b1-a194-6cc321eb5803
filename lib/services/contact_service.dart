import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:sembast/sembast.dart';

import 'package:mla_connect/models/contacts.dart';
import 'package:mla_connect/services/db.dart';
import 'package:mla_connect/services/firebase_cloud.dart';

class ContactService extends ChangeNotifier {
  final dbUtil = DbUtil();

  bool? contatcPermissionGiven;
  int _serverErrorCount = 0;
  final max_server_error = 5;

  static const SEND_LIMIT = 1000;

  Future<List<PhoneContact>> _getContactsFromPhone() async {
    final contacts = await FlutterContacts.getContacts(withProperties: true);
    final start = DateTime.now().millisecondsSinceEpoch;
    final phoneContacts = contacts
        .where((c) => c.phones.isNotEmpty)
        .map((p) => PhoneContact(
            id: p.id,
            synced: false,
            name: p.displayName,
            phones: p.phones
                .map((e) => e.normalizedNumber.isNotEmpty
                    ? e.normalizedNumber
                    : e.number)
                .toList()))
        .toList();
    final end = DateTime.now().millisecondsSinceEpoch;
     //print("Get Contact Diff : ${end - start}");
    return phoneContacts;
  }

  Future _updateContactsToLocal(
      StoreRef storeRef, Database db, List<PhoneContact> contacts) async {
    final previous = (await storeRef.record("all").get(db)) ?? Map();
    // final mutable = Map<String, dynamic>.from(previous);
     //print(previous);
    final mutable = previous as Map<String, dynamic>;
    int count = 0;
    contacts.forEach((c) {
      if (mutable[c.id] == null) {
        mutable[c.id] = c.toJson();
        count++;
      }
    });
     //print("Updateding $count contacts");
    await storeRef.record("all").put(db, mutable);
  }

  Future<List<PhoneContact>> _fetchUnSyncedContacts(
      StoreRef storeRef, Database db) async {
    final cMap = (await storeRef.record("all").get(db)) ?? Map();
    final mutable = (cMap) as Map<String, dynamic>;
    final List<PhoneContact> list = mutable.values
        .map((e) => PhoneContact.fromJson(e as Map<String, dynamic>))
        .where((e) => !e.synced)
        .take(SEND_LIMIT)
        .toList();
    return list;
  }

  Future _setSynced(
      StoreRef storeRef, Database db, List<PhoneContact> contacts) async {
    final previous = (await storeRef.record("all").get(db)) ?? Map();
    final mutable = (previous) as Map<String, dynamic>;
    final synced = contacts.map((e) => e.copyWith(synced: true)).toList();
    synced.forEach((c) {
      mutable[c.id] = c.toJson();
    });
    await storeRef.record("all").put(db, mutable);
  }

  void _serverSync() async {
    try {
      final unSynced =
          await _fetchUnSyncedContacts(dbUtil.contactStore, await dbUtil.db);
      if (unSynced.isEmpty) return;

      final result = await FirebaseCloud.instance.syncContacts(unSynced);
      final syncedSet = result.synced.toSet();
      final syncedContacts =
          unSynced.where((c) => syncedSet.contains(c.id)).toList();
      await _setSynced(dbUtil.contactStore, await dbUtil.db, syncedContacts);
      _serverErrorCount = 0;
      schduleServerSync();
    } catch (e) {
      _serverErrorCount++;
      if (_serverErrorCount <= max_server_error) {
        schduleServerSync();
      }
    }
  }

  void schduleServerSync() {
    Future.microtask(() => _serverSync());
  }

  void startContactSync() async {
    try {
      if (await FlutterContacts.requestPermission(readonly: true)) {
        contatcPermissionGiven = true;
        notifyListeners();

        final allContacts = await _getContactsFromPhone();
        await _updateContactsToLocal(
            dbUtil.contactStore, await dbUtil.db, allContacts);
        schduleServerSync();
      } else {
        contatcPermissionGiven = false;
        notifyListeners();
      }
    } catch (e) {
       //print(e.toError());
    }
  }
}
