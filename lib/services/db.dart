import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast.dart';
import 'package:sembast/sembast_io.dart';

class DbUtil {
  final String _dbPath = 'app.db';
  DatabaseFactory _dbFactory = databaseFactoryIo;
  Database? _database;
  Future<Database> get db async {
    if (_database == null) {
      final appDocDir = await getApplicationDocumentsDirectory();
      _database = await _dbFactory.openDatabase(appDocDir.path + "/" + _dbPath);
    }
    return _database!;
  }

  final contactStore = stringMapStoreFactory.store("local_contacts");
}
