import 'dart:async';

import 'package:mla_connect/controller/chatRoomController.dart';
import 'package:mla_connect/services/unseenMsgController.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:mla_connect/models/ChatMessage.dart';
import 'package:get/instance_manager.dart';

Map<String, dynamic> _body([Map<String, dynamic>? data]) {
  if (data != null) {
    data['API_VERSION'] = 2;
    return data;
  } else {
    final Map<String, dynamic> newD = {};
    newD['API_VERSION'] = 2;
    return newD;
  }
}

class FirebaseChat {
  static final FirebaseChat _singleton = FirebaseChat._();

  static FirebaseChat get instance => _singleton;

  FirebaseChat._();

  final _functions = FirebaseFunctions.instanceFor(region: 'asia-south1');

  final _roomUnseenMsg = StreamController.broadcast();

  Stream _roomUnseenMsgStream() => _roomUnseenMsg.stream;

  Future<bool> createRoom(name) async {
    try {
       //print("HERE");
      var response = await _functions
          .httpsCallable('createChatRoom')
          .call(_body({"name": name}));

      return true;
    } catch (e) {
       //print("Error");
       //print(e);
      return false;
    }
  }

  addVote(String uid, String roomId, String docId, ChatMessagePoll data) async {
    FirebaseFirestore.instance
        .collection('rooms')
        .doc(roomId)
        .collection('messages')
        .doc(docId)
        .update({'pollData': data.toJson()});
  }

  updateLastSeenMsgRoom(String uid, String roomId, String lastseen) async {
    FirebaseFirestore.instance
        .collection('rooms')
        .doc(roomId)
        .update({'lastMessageTimestamp': int.parse(lastseen)});
  }

  updateLastSeenMsgUser(
      String uid, String roomId, String lastseen, ChatMessage msg) async {
    final _controller = Get.find<ChatRoomController>();
    // Timer(Duration(seconds: 1), () => {
    _controller.updateLastSentMessage(msg);
    // });
    await FirebaseFirestore.instance
        .collection('unreadMessages')
        .doc(uid)
        .set({roomId: int.parse(lastseen)}, SetOptions(merge: true));
  }

  getLastSeenMsgCount(String uid) async {
    DocumentSnapshot<Map<String, dynamic>> unreadMsgData =
        await FirebaseFirestore.instance
            .collection('unreadMessages')
            .doc(uid)
            .get();
    UnseenMsgController unseenMsgController = Get.find();
    if (unreadMsgData.data() != null)
      unseenMsgController.setUnseenMsgMap(unreadMsgData.data()!);
  }

  Future<bool> sendMessageToFirebase(
      {required ChatMessage msg, required String chatId}) async {
    // UnseenMsgController unseenMsgController = Get.find();
    // unseenMsgController.updateUnseenMsgMap(chatId, int.parse(msg.messageId));
    try {
      try {
        var documentReference = FirebaseFirestore.instance
            .collection('rooms')
            .doc(chatId)
            .collection("messages")
            .doc(msg.messageId);
        await FirebaseFirestore.instance.runTransaction((transaction) async {
          transaction.set(documentReference, msg.toJson());
        });
        // final _controller = Get.find<ChatRoomController>();
        // _controller.updateLastSentMessage(msg);
        updateLastSeenMsgUser(
            msg.senderId, msg.recipientId, msg.messageId, msg);
        updateLastSeenMsgRoom(msg.senderId, msg.recipientId, msg.messageId);
      } catch (e) {
         //print(e);
      }
      return true;
    } catch (e) {
      return false;
    }
  }
}
