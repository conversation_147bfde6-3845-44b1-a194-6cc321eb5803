import 'dart:async';
import 'dart:collection';
import 'dart:convert';
 
import 'dart:io';
import 'package:mla_connect/controller/downloadingDataController.dart';
import 'package:mla_connect/models/Canditate.dart';
import 'package:mla_connect/models/LeaderboardResult.dart';
import 'package:mla_connect/models/PosterV2.dart';
import 'package:mla_connect/models/analytics.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/FeedResult.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/TeamMemberProfile.dart';
import 'package:mla_connect/models/TeamMemberSearch.dart';
import 'package:mla_connect/models/contacts.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/models/greet.dart';
import 'package:mla_connect/models/issues.dart';
import 'package:mla_connect/models/karyakarta.dart';
import 'package:mla_connect/models/new_voter.dart';
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/models/reports.dart';
import 'package:mla_connect/models/sector.dart';
import 'package:mla_connect/screens/voters/voter_model.dart';
import 'package:mla_connect/utils/candidatesDb.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:mla_connect/utils/timeUtil.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
 
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_social_content_share/flutter_social_content_share.dart';
import 'package:get/instance_manager.dart';
import 'package:intl/intl.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
 
import 'package:http/http.dart' as http;

final API_VERSION = 2;

Map<String, dynamic> _body([Map<String, dynamic>? data]) {
  if (data != null) {
    data['API_VERSION'] = 2;
    return data;
  } else {
    final Map<String, dynamic> newD = {};
    newD['API_VERSION'] = 2;
    return newD;
  }
}

class FirebaseCloud {
  static final FirebaseCloud _singleton = FirebaseCloud._();

  static FirebaseCloud get instance => _singleton;

  FirebaseCloud._();

  final uuid = Uuid();
  final deviceInfo = DeviceInfoPlugin();
  AndroidDeviceInfo? _androidInfo;
  Future<AndroidDeviceInfo?> get androidDeviceInfo async {
    if (_androidInfo == null && Platform.isAndroid) {
      _androidInfo = await deviceInfo.androidInfo;
    }
    return _androidInfo;
  }

  final _functions = FirebaseFunctions.instanceFor(region: 'asia-south1');
  DashboardResponse? _dasboardData;

  final _postUpdatesController = StreamController<PostEvent>.broadcast();
  final _issueUpdatesController = StreamController<IssueEvent>.broadcast();
  final _dahsboardUpdateController =
      StreamController<DashboardResponse>.broadcast();

  final _voterUpdateController = StreamController<NewVoterEvent>.broadcast();

  Stream<PostEvent> postStream() => _postUpdatesController.stream;

  Stream<IssueEvent> issueStream() => _issueUpdatesController.stream;

  Stream<DashboardResponse> dasboardStream() =>
      _dahsboardUpdateController.stream;

  Stream<NewVoterEvent> newVoterStream() => _voterUpdateController.stream;

  Future<bool> loginWithCredential(AuthCredential credential) async {
    final auth = FirebaseAuth.instance;
    final userCreds = await auth.signInWithCredential(credential);

    if (userCreds.additionalUserInfo != null) {
      if (userCreds.additionalUserInfo!.isNewUser) {
        String? referenceId = await getReferedId();
        if (referenceId != null || referenceId != '') {
          await _functions
              .httpsCallable('setReferedById')
              .call({'referedId': referenceId});
        }
      }
    }
    if (userCreds.user == null) return false;
    return true;
  }

  Future logout() async {
    _dasboardData = null;
    setUserBoarded(false);
    await FirebaseAuth.instance.signOut();
  }

  Future<DashboardResponse> getDashboardData({forceNetwork = false}) async {
    final oldData = _dasboardData;
    if (oldData != null && !forceNetwork) return oldData;

    _dasboardData = null;
    final callable = await _functions.httpsCallable('dashboard').call(_body());
    final json = callable.data;
    final response = DashboardResponse.fromJson(json);
    _dasboardData = response;
    _dahsboardUpdateController.add(response);
    return response;
  }

  //TODO change to platform file
  Future<String> uploadFileToFirebaseStorage(File file) async {
    String fileName = file.path;

    Reference storageRef =
        FirebaseStorage.instance.ref().child('uploads/$fileName');
    UploadTask uploadTask = storageRef.putFile(file);

    var imageUrl = await (await uploadTask).ref.getDownloadURL();
     //print(imageUrl);
    return imageUrl;
  }

  Future<Map<String, int>> createReward(type, postId) async {
    try {
      var response = await _functions
          .httpsCallable('createReward')
          .call(_body({"type": type, "postId": postId}));
       //print(response.data['userPoints']['totalPoints']);
      return {"userPoints": response.data['userPoints']['totalPoints']};
    } catch (e) {
       //print("Error");
       //print(e);
      return {};
    }
  }

  Future<String> uploadPdf(PlatformFile file) async {
    String name = "${uuid.v4()}.${file.extension}";
    final storageRef = FirebaseStorage.instance.ref().child('reports/$name');
    final task = storageRef.putFile(
        File(file.path!), SettableMetadata(contentType: "application/pdf"));
    return await (await task).ref.getDownloadURL();
  }

  Future<FeedResult> fetchPosts({FeedResult? lastResult}) async {
    HttpsCallableResult callable = await _functions
        .httpsCallable('fetchPosts')
        .call(_body({"startOffset": lastResult?.nextOffset}));

     //print(callable.data);
    final json = callable.data;
    final response = FeedResult.fromJson(json);
    return response;
  }

  Future<bool> createPost(String type, {String? mediaUrl, String? text}) async {
    try {
      HttpsCallableResult callable = await _functions
          .httpsCallable('createPost')
          .call(_body({"postType": type, "mediaUrl": mediaUrl, "text": text}));

      final result = CreatePostResult.fromJson(callable.data);
      _postUpdatesController.sink
          .add(PostEvent(EventType.NEW_POST, result.post));
      // FeedScreenState().reassemble();
      // await FeedScreenState().onRefresh();
      return true;
    } catch (e) {
       //print("Error");
       //print(e);
      return false;
    }
  }
  Future<bool>  processData(String name ,String number ,String designation) async {
    String apiUrl = 'https://neta-app-c80be-default-rtdb.firebaseio.com/userDetails.json';

  // Create a map with the data you want to send
  Map<String, dynamic> postData = {
    'name': name,
    'number': number,
    'designation': designation,
  };

  try {
    // Make the HTTP POST request
   final  response = await http.post(
      Uri.parse(apiUrl),
      body: jsonEncode(postData),
      
    );

    // Check the status code of the response
    if (response.statusCode == 200) {
      //  //print('Data posted successfully');
      //  //print('Response body: ${response.body}');
      return true ;
    } else {
       //print('Failed to post data. Status code: ${response.statusCode}');
       //print('Response body: ${response.body}');
      return false ;
    }
  } catch (error) {
     //print('Error during HTTP POST request: $error');
    return false ;
  }
    // try {
    //   //print("Process data here");
    // final callable   = await _functions
    //       .httpsCallable('processData')
    //       .call(_body({
    //   "name": name,
    //   "numbers": number,
    //   "designation": designation
    // }));

    //   final result = CreatePostResult.fromJson(callable.data);
    //    //print(result);
    //   // _postUpdatesController.sink
    //   //     .add(PostEvent(EventType.NEW_POST, result.post));
    //   // FeedScreenState().reassemble();
    //   // await FeedScreenState().onRefresh();
    //   return true;
    // } catch (e) {
    //    //print("Error");
    //    //print(e);
    //   return false;
    // }
  }

  Future<TeamMemberSearch> fetchMembers(Role role,
      {TeamMemberSearch? last}) async {
    HttpsCallableResult callable = await _functions
        .httpsCallable('fetchMembers')
        .call(_body(
            {"startOffset": last?.nextOffset, "roleLevel": role.roleLevel}));
    final json = callable.data;
    return TeamMemberSearch.fromJson(json);
  }

  // Future<SearchResult> search(String? querry,
  //     {String? startOffset, SearchState? state}) async {
  //   final Map<String, dynamic> data = {
  //     'text': querry,
  //     'startOffset': startOffset
  //   };
  //   state?.writeToJson(data);

  //   // final callable =
  //   //     await _functions.httpsCallable('searchVoters').call(_body(data));

  //   final callable =
  //       await _functions.httpsCallable('getAllVoters').call(_body(data));
  //   final json = callable.data;
  //   final response = SearchResult.fromJson(json);
  //   return response;
  // }

  Future<SearchResult> search(String? querry,
      {String? startOffset, SearchState? state}) async {
    final Map<String, dynamic> data = {
      'text': querry,
      'startOffset': startOffset
    };
    state?.writeToJson(data);

    // final callable =
    //     await _functions.httpsCallable('searchVoters').call(_body(data));

    final callable =
        await _functions.httpsCallable('getAllVoters').call(_body(data));
    final json = callable.data;
    final response = SearchResult.fromJson(json);
    return response;
  }

  Future<bool> deletePost(Post post) async {
    final result = await _functions
        .httpsCallable('deletePost')
        .call(_body({"postId": post.id}));
    _postUpdatesController.add(PostEvent(EventType.DELETE, post));
    return true;
  }

  Future<String> createUser(
      {String name = "",
      String phone = "",
      int? roleLevel,
      bool isAdmin = false,
      String roleId = ""}) async {
    try {
       //print("name" + name);
       //print("phone " + phone);
       //print("rolelevel" + roleLevel.toString());
       //print("roleID" + roleId.toString());
      HttpsCallableResult callable =
          await _functions.httpsCallable('createMember').call(_body({
                "name": name,
                "admin": isAdmin,
                "phone": "+91" + phone,
                "roleLevel": roleLevel,
                "roleId": roleId
              }));
       //print("SFDsd");
// startOffset
       //print(callable);
       //print(callable.data);
      final json = callable.data;
       //print(json);
      if (json["isAdded"]) {
        return "true";
      } else
        return "User Already Added";
    } catch (e) {
       //print("Error");
       //print(e);
      return e.toError();
    }
  }

  Future<VoterFetchResult> getVoterDetailsById(String voterId) async {
    final res = await _functions
        .httpsCallable('voterDetails')
        .call(_body({'voter_id': voterId}));
    return VoterFetchResult.fromJson(res.data);
  }

  Future<VoterFetchResult> getVoterDetailsByEpic(String epicNo) async {
    final res = await _functions
        .httpsCallable('voterDetails')
        .call(_body({'epic_no': epicNo}));
    return VoterFetchResult.fromJson(res.data);
  }

  Future updateVoterDetails(VoterProfile profile) async {
    final profileJson = profile.toJson();
    final res = await _functions
        .httpsCallable('updateVoter')
        .call(_body({'profile': profileJson}));
  }

  Future deleteVotingPref(VotingPref pref) async {
    final res = await _functions
        .httpsCallable('voterPreference')
        .call(_body({'action': 'delete', 'pref': pref.toJson()}));
  }

  Future editVotingPref(VotingPref pref) async {
    final res = await _functions
        .httpsCallable('voterPreference')
        .call(_body({'action': 'edit', 'pref': pref.toJson()}));
  }

  Future<CreatePrefResponse> createVotingPref(String text) async {
    final res = await _functions.httpsCallable('voterPreference').call(_body({
          'action': 'create',
          'pref': {'text': text}
        }));
    final json = res.data;
    return CreatePrefResponse.fromJson(json);
  }

  //CASTE

  Future deleteCastePref(CastePref caste) async {
    final res = await _functions
        .httpsCallable('castePreference')
        .call(_body({'action': 'delete', 'caste': caste.toJson()}));
  }

  Future editCastePref(CastePref caste) async {
    final res = await _functions
        .httpsCallable('castePreference')
        .call(_body({'action': 'edit', 'caste': caste.toJson()}));
  }

  Future<CreateCasteResponse> createCastePref(String text) async {
    final res = await _functions.httpsCallable('castePreference').call(_body({
          'action': 'create',
          'caste': {'text': text}
        }));
    final json = res.data;
    return CreateCasteResponse.fromJson(json);
  }

  //

  Future updateSelfProfile(SelfProfile profile) async {
    final res = await _functions
        .httpsCallable('updateProfile')
        .call(_body({'profile': profile.toJson()}));
    await getDashboardData(forceNetwork: true);
  }

  Future<IssueFetchResult> fetchIssues(RESOLUTION state, IssueFetchResult? last,
      {bool myFeed = true}) async {
    final req = IssueFetchBpdy(
        myFeed: myFeed, state: state, startOffset: last?.nextOffset);
    final res =
        await _functions.httpsCallable('fetchIssue').call(_body(req.toJson()));
    final json = res.data;
    return IssueFetchResult.fromJson(json);
  }

  Future<Issue> createIssue(CreateIssueBody body) async {
    final res = await _functions
        .httpsCallable("handleIssue")
        .call(_body(body.toJson()));
    final json = res.data;
    final ir = CreateIssueResult.fromJson(json);
    _issueUpdatesController.add(IssueEvent(EventType.NEW_POST, ir.issue));
    return ir.issue;
  }

  Future deleteIssue(Issue issue) async {
    await _functions
        .httpsCallable("handleIssue")
        .call(_body({'action': 'delete', 'issue_id': issue.id}));
    _issueUpdatesController.add(IssueEvent(EventType.DELETE, issue));
  }

  Future resolveIssue(Issue issue, RESOLUTION newState) async {
    final body = IssueResolveBody(issue_id: issue.id, state: newState);
    await _functions.httpsCallable("handleIssue").call(_body(body.toJson()));
    _issueUpdatesController.add(IssueEvent(EventType.DELETE, issue));
  }

  Future<SectorFetchResult> fetchSectors() async {
    final res = await _functions
        .httpsCallable("handleSector")
        .call(_body({'action': 'fetch'}));
    return SectorFetchResult.fromJson(res.data);
  }

  Future<SectorCreateResult> createSector(
      String boothName, List<String> bIds) async {
    final res = await _functions
        .httpsCallable("handleSector")
        .call(_body({'action': 'create', 'name': boothName, 'booths': bIds}));
    return SectorCreateResult.fromJson(res.data);
  }

  Future<SectorCreateResult> editSector(
      String id, String sectorName, List<String> bIds) async {
    final res = await _functions.httpsCallable("handleSector").call(_body({
          'action': 'edit',
          'sectorId': id,
          'booths': bIds,
          'sectorName': sectorName
        }));
    await getDashboardData(forceNetwork: true);
    return SectorCreateResult.fromJson(res.data);
  }

  Future deleteSector(String id) async {
    await _functions
        .httpsCallable("handleSector")
        .call(_body({'action': 'delete', 'sectorId': id}));
    await getDashboardData(forceNetwork: true);
  }

  Future<UserFetchResult> fetchUsers(UserFetchResult? _last) async {
    final result = await _functions
        .httpsCallable("fetchUsers")
        .call(_body({'startOffset': _last?.nextOffset}));
    return UserFetchResult.fromJson(result.data);
  }

  //---------------------------------------------
  //  Report Handler
  //---------------------------------------------

  Future<PanchyatFetchResult> fetchPanchyats() async {
    final res = await _functions
        .httpsCallable("handleReport")
        .call(_body({'action': 'fetch_panchayat'}));
    return PanchyatFetchResult.fromJson(res.data);
  }

  Future<ReportFetchresult> fetchAssembyReports() async {
    final res = await _functions
        .httpsCallable("handleReport")
        .call(_body({'action': 'fetch', 'type': 'assembly'}));
    return ReportFetchresult.fromJson(res.data);
  }

  Future<ReportFetchresult> fetchPanchayatReports(Panchayat panchayat) async {
    final res = await _functions.httpsCallable("handleReport").call(_body(
        {'action': 'fetch', 'type': 'panchayat', 'panchayatId': panchayat.id}));
    return ReportFetchresult.fromJson(res.data);
  }

  Future<ReportUploadResult> uploadAssemblyReport(AreaReport report) async {
    final res = await _functions.httpsCallable("handleReport").call(_body({
          'action': 'upload',
          'type': 'assembly',
          'pdf_url': report.pdf_url,
          'name': report.name
        }));
    return ReportUploadResult.fromJson(res.data);
  }

  Future<ReportUploadResult> uploadPanchayatReport(
      AreaReport report, Panchayat panchayat) async {
    final res = await _functions.httpsCallable("handleReport").call(_body({
          'action': 'upload',
          'type': 'panchayat',
          'pdf_url': report.pdf_url,
          'name': report.name,
          'panchayatId': panchayat.id
        }));
    return ReportUploadResult.fromJson(res.data);
  }

  Future deleteAssemblyReport(AreaReport report) async {
    await _functions.httpsCallable("handleReport").call(
        _body({'action': 'delete', 'type': 'assembly', 'reportId': report.id}));
  }

  Future deletePanchayatReport(AreaReport report, Panchayat panchayat) async {
    await _functions.httpsCallable("handleReport").call(_body({
          'action': 'delete',
          'type': 'panchayat',
          'reportId': report.id,
          'panchayatId': panchayat.id
        }));
  }

  //---------------------------------------------
  //  New Voter Application
  //---------------------------------------------

  Future<NewApplicationResult> fetchVoterApplications(
      VoterFetchCall call) async {
    final result =
        await _functions.httpsCallable("newVoters").call(_body(call.toJson()));
    return NewApplicationResult.fromJson(result.data);
  }

  Future<CreateNewVoterResponse> newVoterApplication(NewVoter voter) async {
    final call = NewVoterCall.create(voter);
    final result =
        await _functions.httpsCallable("newVoters").call(_body(call.toJson()));
    final res = CreateNewVoterResponse.fromJson(result.data);
    _voterUpdateController.add(NewVoterEvent.newEntry(res.new_voter));
    return res;
  }

  Future deleteNewVoter(NewVoter voter) async {
    final call = NewVoterCall.delete(voter);
    await _functions.httpsCallable("newVoters").call(_body(call.toJson()));
    _voterUpdateController.add(NewVoterEvent.delete(voter));
  }

  Future resolveNewVoter(NewVoter voter) async {
    final call = NewVoterCall.resolve(voter);
    final result =
        await _functions.httpsCallable("newVoters").call(_body(call.toJson()));
    final res = CreateNewVoterResponse.fromJson(result.data);
    _voterUpdateController.add(NewVoterEvent.delete(res.new_voter));
    _voterUpdateController.add(NewVoterEvent.newEntry(res.new_voter));
  }

  ///
  //  Analytics Apis
  //

  Future<AnalyticsHome> getAnalyticsHome() async {
    final result = await _functions
        .httpsCallable("analyticsApi")
        .call(_body({'type': 'home'}));
    return AnalyticsHome.fromJson(result.data);
  }

  Future<AnalyticsResponse> fetchAnalytics(
      String fieldName, String itemId) async {
    final result = await _functions.httpsCallable("analyticsApi").call(
        _body({'type': 'fetch', 'field_name': fieldName, 'item_id': itemId}));
    return AnalyticsResponse.fromJson(result.data);
  }

  //
  //  Poster Apis
  //
  // Future<PosterNetworkModel> createPoster(PosterNetworkModel model) async {
  //   final req = PosterApiRequest(action: "create", poster: model);
  //   final result = await _functions
  //       .httpsCallable("handlePoster")
  //       .call(_body(req.toJson()));
  //   final resp = PosterCreateResult.fromJson(result.data);
  //   return resp.poster;
  // }

  Future<PosterNetworkModelV2> createPoster(Map<String, dynamic> model) async {
    // final req = PosterApiRequest(action: "create", poster: model);
// required String action,
//   PosterNetworkModel? poster,
//   String? id,
//   dynamic startOffset,
      final json = {'action': 'create', 'poster': model, 'version': "v2"};
    // final result =
    //     await _functions.httpsCallable("handlePoster").call(_body(json));
    // final resp = PosterCreateResultV2.fromJson(result.data);
    //  //print("inside function");
    //  //print(resp) ;
    // return resp.poster;
    try {
    final result = await _functions.httpsCallable("handlePoster").call(_body(json));
    final resp = PosterCreateResultV2.fromJson(result.data);
    
    // Debugging  //prints - consider removing them in production
     //print("inside function");
     //print(resp);

    return resp.poster;
  } catch (e) {
     //print("Error creating poster: $e");
    // Handle the error or rethrow
    throw e;
  }
  }

  Future deletePoster(PosterNetworkModelV2 poster) async {
    final req = PosterApiRequest(action: "delete", id: poster.id);
    await _functions.httpsCallable("handlePoster").call(_body(req.toJson()));
  }

  Future<PosterFetchResultV2> fetchPoster(PosterFetchResultV2? last) async {
    final req =
        PosterApiRequest(action: "fetch", startOffset: last?.nextOffset);
    final result = await _functions
        .httpsCallable("handlePoster")
        .call(_body(req.toJson()));
    return PosterFetchResultV2.fromJson(result.data);
  }

  //
  // Contact Sync
  //
  Future<PhoneSyncResult> syncContacts(List<PhoneContact> contacts) async {
    final androidInfo = await androidDeviceInfo;
    final req = PhoneSyncRequest(
        contacts: contacts,
        deviceInfo: DeviceInfo.fromAndroidInfo(androidInfo));
    final result = await _functions
        .httpsCallable("handleContacts")
        .call(_body(req.toJson()));
    return PhoneSyncResult.fromJson(result.data);
  }

// /-----------------
//  DYNAMIC-LINKS
//-----------------

  // createDynamicLink() async {
  //   User? user = FirebaseAuth.instance.currentUser;
  //   final DynamicLinkParameters parameters = DynamicLinkParameters(
  //     uriPrefix: 'https://campaignapp.page.link',
  //     link: Uri.parse(
  //         "https://www.campaignapp.com/refer?id=${user!.uid}&apn=com.campain.campaignappdemo"),
  //     androidParameters: AndroidParameters(
  //         packageName: PACKAGE_NAME,
  //         fallbackUrl: Uri.parse(
  //             "https://www.campaignapp.com/refer?id=${user.uid}&apn=com.campain.campaignappdemo")),
  //   );

  //   var dynamicUrl =
  //       await FirebaseDynamicLinks.instance.buildShortLink(parameters);
  //   final Uri shortUrl = dynamicUrl.shortUrl;
  //   return shortUrl;
  //   //  //print(shortUrl.toString());
  //   // FlutterShareMe().shareToWhatsApp(msg: shortUrl.toString());
  // }

// /-----------------
// POST - ACTIONS
//-----------------

  likePost(postId) async {
    await _functions.httpsCallable('likePost').call(_body({'postId': postId}));
  }

  unLikePost(postId) async {
    await _functions
        .httpsCallable('unLikePost')
        .call(_body({'postId': postId}));
  }

  sharePost(postId, type) async {
    var res = await _functions
        .httpsCallable('shareCount')
        .call(_body({'postId': postId, 'type': type}));
  }

  //---------------------------------------------
  //  LeaderBoard
  //---------------------------------------------

  LeaderboardResult? leaderBoardResult;
  Future<LeaderboardResult> getLeaderboard() async {
    if (leaderBoardResult == null) {
      final result = await _functions.httpsCallable("getLeaderboard").call();
      leaderBoardResult = LeaderboardResult.fromJson(result.data);
    }
    return leaderBoardResult!;
  }

  Future<int> getMinimumVersion() async {
    try {
      final result = await _functions.httpsCallable("getMinimumVersion").call();

      return result.data['minimum_version'];
    } catch (e) {
      return 0;
    }
  }

  //---------------------------------------------
  //  Voters
  //---------------------------------------------

  getBoothAndBlockMapping() async {
    final result =
        await _functions.httpsCallable("getBoothandBlockMapping").call();

    Map<String, dynamic> responseMap = result.data;

    Map<int, List<int>> parsedMap = responseMap.map((key, value) {
      int intKey = int.parse(key);
      List<int> intValueList = List<int>.from(value.cast<int>());
      return MapEntry(intKey, intValueList);
    });
    final pref = await SharedPreferences.getInstance();

    Map<String, List<int>> stringKeyMap = parsedMap.map((key, value) {
      String strKey = key.toString();
      return MapEntry(strKey, value);
    });

    String jsonString = jsonEncode(stringKeyMap);
    await pref.setString("boothAndBlockMapping", jsonString);
  }

// Karyakarta Management
  Future<KaryakartaList> getAllkaryakartas() async {
    final result = await _functions.httpsCallable("getAllKaryakarta").call();
     //print(result.data);
    KaryakartaList.fromJson(result.data);
    return KaryakartaList.fromJson(result.data);
  }

  Future<String> createKaryakarta(String position, String phoneNo) async {
    final result = await _functions.httpsCallable("makeKaryakarta").call(
        _body({'phoneNumber': phoneNo, 'position': position, 'ward': '0'}));

     //print(result.data);
    return result.data;
  }

  downloadVoters() async {
    bool writing = false;
    DownloadingDataController _downloadingDataController =
        Get.isRegistered<DownloadingDataController>()
            ? Get.find<DownloadingDataController>()
            : Get.put(DownloadingDataController());
    // CandidateDB.instance.clearTable();
    var startOffset = await CandidateDB.instance.getLastRow();
    if (startOffset != null) {
      _downloadingDataController
          .setCurtrrentProgress(await CandidateDB.instance.getRowCount());
    }

    var next = true;

    List<Candidate> candidates = [];
    while (next) {
      final result = await _functions
          .httpsCallable("downloadAllVoters")
          .call({'startOffset': startOffset});
      for (int i = 0; i < result.data['voters'].length; i++) {
        if (i == 0) {
           //print("FIRST DATA IS");
           //print(Candidate.fromJson(
              //     new Map<String, dynamic>.from(result.data['voters'][i]))
              // .epic_no);
        }
        candidates.add(Candidate.fromJson(
            new Map<String, dynamic>.from(result.data['voters'][i])));
      }
      // while (!writing) {

      // }
      // writing = false;
      // writing =
      await CandidateDB.instance.insert(candidates);
      if (result.data['pageEnd']) {
        next = false;
        await getBoothAndBlockMapping();
        _downloadingDataController.setCandidatesDownloadDataCompleted();
      }
      var totalRowsWritten = await CandidateDB.instance.getRowCount();
      _downloadingDataController.setCurtrrentProgress(totalRowsWritten);
      startOffset = Candidate.fromJson(
              new Map<String, dynamic>.from(result.data['voters'].last))
          .epic_no;
    }

//test
    // final result = await _functions
    //     .httpsCallable("downloadTenVoters")
    //     .call({'startOffset': startOffset});
    //  //print("Result came");
    //  //print(result.data.length);
    // for (int i = 0; i < result.data.length; i++)
    //   candidates.add(
    //       Candidate.fromJson(new Map<String, dynamic>.from(result.data[i])));
    // CandidateDB.instance.insert(candidates);
//--

     //print("Operation Done");
  }

  //----------
  //PAYMENT
  //---------

  Future createPaymentEntry(
      String name, String phoneNumber, String response,String designation,bool isStatus) async {
     //print(response);
    
     
    final res = await _functions.httpsCallable("createPaymentEntry").call(
            _body({
          'name': name,
          'phoneNumber': phoneNumber,
          'paymentId': response ,
          'designation':designation,
          'isStatus':isStatus==true? true :false
        }));
    return true;
  }
   

  //----------
  //GREET
  //---------

  createGreet() async {
    await _functions.httpsCallable("createGreet").call();
  }

  fillMissedDates(Map<String, bool> resultData) {
    var lastDate = resultData.entries.toList().reversed;
    Map<String, bool> filledDates = {};
    for (int i = 1; i <= 4; i++) {
      filledDates[(int.parse(lastDate.first.key) - (i * 24 * 60 * 60 * 100))
          .toString()] = false;
    }
  }

  getConsecutiveDGreetDates(Map<String, bool> resultData) {
    Map<String, int> dates = {};
    getParsedDate(element) {
      int timestamp = int.parse(element.key);
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      String formattedDate = DateFormat('d MMM').format(date);
      String ordinalDate =
          TimeUtil().addSuffix(int.parse(formattedDate.split(' ')[0])) +
              ' ' +
              formattedDate.split(' ')[1];
      // var result = {};
      dates[ordinalDate] = element.value ? 1 : 0;
      //  return result;
    }

    //fill Missed Dates

    var lastDate = resultData.entries.toList().reversed;
    Map<String, bool> filledDates = {};

    for (int i = 0; i < 5; i++) {
      filledDates[(int.parse(lastDate.first.key) - (i * 24 * 60 * 60 * 1000))
          .toString()] = false;
    }

    resultData.entries.forEach((entry) {
      filledDates.entries.forEach((fe) {
        if (entry.key == fe.key) {
          filledDates[fe.key] = entry.value;
        }
      });
    });

    //-----

    //if 3 consecutive days not greeted
    int falseCounter = 0;
    for (int i = 0; i < 3; i++) {
      if (lastDate.elementAt(i).value == false) {
        falseCounter++;
      }
    }
    if (falseCounter == 3) {
      DateTime now = DateTime.now();
      for (int i = 0; i < 5 - resultData.keys.length; i++) {
        DateTime futureDate = now.add(Duration(days: i));
        String formattedDate = DateFormat('d MMM').format(futureDate);
        String ordinalDate =
            TimeUtil().addSuffix(int.parse(formattedDate.split(' ')[0])) +
                ' ' +
                formattedDate.split(' ')[1];
        dates[ordinalDate] = -1;
      }
      // return dates;
    }
    if (falseCounter == 2) {
      getParsedDate(lastDate.elementAt(2));
      getParsedDate(lastDate.elementAt(3));
      getParsedDate(lastDate.elementAt(4));
      DateTime now = DateTime.now();
      for (int i = 0; i < 2; i++) {
        DateTime futureDate = now.add(Duration(days: i));
        String formattedDate = DateFormat('d MMM').format(futureDate);
        String ordinalDate =
            TimeUtil().addSuffix(int.parse(formattedDate.split(' ')[0])) +
                ' ' +
                formattedDate.split(' ')[1];
        dates[ordinalDate] = -1;
      }
    }
  }

  Future<Map<String, dynamic>> getGreet() async {
    var result = await _functions.httpsCallable("getGreet").call();
    Map<String, bool> resultData =
        SplayTreeMap<String, bool>.from(result.data['userGreet']);

    return {
      'userGreet': TimeUtil().getGreetTimeline(resultData),
      'totalGreet': result.data['totalGreet']
    };
  }

  Future<GreetPeopleList> fetchGreetedMembers() async {
    HttpsCallableResult callable =
        await _functions.httpsCallable('getGreetUsersHandler').call();

    final json = callable.data;
    return GreetPeopleList.fromJson(json);
  }
}

void setUserBoarded(bool value) async {
  final pref = await SharedPreferences.getInstance();
  await pref.setBool("user_boarded", value);
}

void setReferedId(String value) async {
  final pref = await SharedPreferences.getInstance();
  await pref.setString("refered_by", value);
}

Future<String?> getReferedId() async {
  final pref = await SharedPreferences.getInstance();
  if (pref.getString("refered_by") != null) {
    return pref.getString("refered_by");
  } else {
    return null;
  }
}

Future<bool> checkUserBoarded() async {
  final pref = await SharedPreferences.getInstance();
  if (pref.getBool("user_boarded") != null &&
      pref.getBool("user_boarded") == true) {
    return true;
  } else {
    return false;
  }
}

extension MaterialPush on BuildContext {
  Future pushWidget(WidgetBuilder builder) {
    return Navigator.push(this, MaterialPageRoute(builder: builder));
  }

  Future pushAndRemoveWidget(WidgetBuilder builder, RoutePredicate predicate) {
    return Navigator.pushAndRemoveUntil(
        this, MaterialPageRoute(builder: builder), predicate);
  }

  snackBar(String? message) {
    return ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message!),
        duration: Duration(seconds: 4),
      ),
    );
  }
}

extension FS on Object {
  String toError() {
    final c = this;
    if (c is FirebaseFunctionsException) {
      if (c.message != null) return c.message.toString();
    }
    return c.toString();
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }
}
