// Future razorpayInit() async{
//     RazorpayPayment razorpayPayment = RazorpayPayment(
//         context: context,
//         amount: int.parse(amounts[currentSelection]),
//         paymentSuccess: () {
//           setState(() {
//             getWalletAmount();
//           });
//         });
//     razorpayPayment.initRazorpay();
//     razorpayPayment.openCheckout();
//   }

import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
// import 'package:hexcolor/hexcolor.dart';
// import 'package:lottie/lottie.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

// import '../GlobalWidgets/primary_button.dart';

// key = rzp_test_ThaEWCeyB5INTI
// secret = Zlfu1VUxAeAoBE090TNXwSjO

class RazorpayPayment {
  final BuildContext context;
  final int amount;
  final VoidCallback? paymentSuccess;
  final String name;
  final String phoneNumber;
  final String designation ;

  RazorpayPayment(
      {required this.context,
      required this.amount,
      this.paymentSuccess,
      required this.name,
      required this.designation ,
      required this.phoneNumber});

  late Razorpay _razorpay;

  void initRazorpay() {
     
    _razorpay = Razorpay();
    paymentinit();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  void dispose() {
    _razorpay.clear();
  }
 void paymentinit()async{
await FacebookAppEvents().logEvent(name: "Payment intilize");
 }
  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    var paymentid =response.paymentId.toString() ;
    double am= double.parse(amount.toString());
     await FacebookAppEvents().logPurchase(amount:  am , currency: 'INR');
    // Handle successful payment
    await FirebaseCloud.instance
        .createPaymentEntry(name, phoneNumber, paymentid,designation,true);
    successMessage(context);
    dispose();
  }

  void _handlePaymentError(PaymentFailureResponse response) async {
      Map<dynamic, dynamic>?v=response.error ;
     //print("object  "+ v.toString() );
    // PaymentSuccessResponse? respons   ;
    await FirebaseCloud.instance
        .createPaymentEntry(name, phoneNumber, "" ,designation,false);
    errorMessage(context);
    // Handle payment failure
    dispose();
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Handle external wallet
    dispose();
  }

  void openCheckout() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    int number = int.parse(sharedPreferences.getString('number') ?? '0');
    var options = {
      'key': 'rzp_live_WnBblVuW1S46Y4',
      'amount': amount * 100, // amount is in Paise hence multiply by 100
      'name': 'Neta App',
      'description': 'Token money',
      'prefill': {'contact': number},
      'external': {
      'wallets': ['paytm']
      }
    };
    try {
      _razorpay.open(options);
    } catch (e) {
      // debug //print(e.toString());
    }
  }

  Future<void> successMessage(BuildContext context) async {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => false,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15)),
              backgroundColor: Colors.white,
              content: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        width: 150,
                        height: 150,
                        child: Lottie.asset('assets/animations/success.json',
                            repeat: false, fit: BoxFit.fill),
                      ),
                      const Text('The payment was successful',
                          textAlign: TextAlign.center,
                          style:  TextStyle(fontFamily: 'Gilroy',
                              fontSize: 24,
                              fontWeight: FontWeight.w800,
                               
                              height: 1.5,
                              color: Colors.black87))
                    ],
                  ),
                ),
              ),
              actions: <Widget>[
                ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text("Okay"))
              ],
            ),
          );
        });
  }

  Future<void> errorMessage(BuildContext context) async {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => false,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15)),
              backgroundColor: Colors.white,
              content: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        width: 100,
                        height: 100,
                        child: Lottie.asset('assets/animations/failed.json',
                            fit: BoxFit.fill, repeat: false),
                      ),
                      const Text(
                        'Error processing your payment',
                        textAlign: TextAlign.center,
                        style:  TextStyle(fontFamily: 'Gilroy',
                            fontSize: 24,
                            fontWeight: FontWeight.w800,
                           
                            color: Colors.black87),
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      Text(
                        'Contact support if your amount has been debited',
                        textAlign: TextAlign.center,
                        style:  TextStyle(fontFamily: 'Gilroy',
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87.withAlpha(150),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              actions: <Widget>[
                ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text("Okay"))
              ],
            ),
          );
        });
  }
}
