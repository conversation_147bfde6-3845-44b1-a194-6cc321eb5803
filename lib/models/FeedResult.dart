import 'package:mla_connect/models/Post.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'FeedResult.g.dart';

@CopyWith()
@JsonSerializable()
class FeedResult {
  final List<Post> posts;
  final bool pageEnd;
  final int? nextOffset;

  FeedResult({
    required this.posts,
    required this.pageEnd,
    required this.nextOffset,
  });

  factory FeedResult.fromJson(Map<String, dynamic> json) =>
      _$FeedResultFromJson(json);
  Map<String, dynamic> toJson() => _$FeedResultToJson(this);
}
