import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:json_annotation/json_annotation.dart';

part 'contacts.g.dart';

@JsonSerializable()
@CopyWith()
class PhoneContact {
  final String id;
  final String name;
  final List<String> phones;
  final bool synced;
  PhoneContact(
      {required this.id,
      required this.name,
      required this.phones,
      required this.synced});

  factory PhoneContact.fromJson(Map<String, dynamic> json) =>
      _$PhoneContactFromJson(json);
  Map<String, dynamic> toJson() => _$PhoneContactToJson(this);
}

@JsonSerializable()
class DeviceInfo {
  final String? brand;
  final String? device;
  final String? id;
  final String? manufacturer;
  final String? model;
  final String? product;
  final String? androidId;
  final String? finger ;
  DeviceInfo({
    this.brand,
    this.device,
    this.id,
    this.manufacturer,
    this.model,
    this.product,
    this.androidId,
    this.finger  ,
  });

  factory DeviceInfo.fromAndroidInfo(AndroidDeviceInfo? info) {
    return DeviceInfo(
        brand: info?.brand,
        device: info?.device,
        id: info?.id,
        manufacturer: info?.manufacturer,
        model: info?.model,
        product: info?.product,
        // androidId: info?.androidId,
        // finger : info?.finger 
      );
  }

  factory DeviceInfo.fromJson(Map<String, dynamic> json) =>
      _$DeviceInfoFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceInfoToJson(this);
}

//API REquest
@JsonSerializable()
class PhoneSyncRequest {
  final DeviceInfo deviceInfo;
  final List<PhoneContact> contacts;
  PhoneSyncRequest({required this.contacts, required this.deviceInfo});

  factory PhoneSyncRequest.fromJson(Map<String, dynamic> json) =>
      _$PhoneSyncRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PhoneSyncRequestToJson(this);
}

@JsonSerializable()
class PhoneSyncResult {
  final List<String> synced;
  PhoneSyncResult({
    required this.synced,
  });

  factory PhoneSyncResult.fromJson(Map<String, dynamic> json) =>
      _$PhoneSyncResultFromJson(json);
  Map<String, dynamic> toJson() => _$PhoneSyncResultToJson(this);
}
