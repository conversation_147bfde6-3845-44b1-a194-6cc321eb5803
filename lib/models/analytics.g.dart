// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$AnalyticsItemCWProxy {
  AnalyticsItem name(String name);

  AnalyticsItem lastUpdatedInMs(int lastUpdatedInMs);

  AnalyticsItem roleLevel(int roleLevel);

  AnalyticsItem options(List<ItemSubCat>? options);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AnalyticsItem(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AnalyticsItem(...).copyWith(id: 12, name: "My name")
  /// ````
  AnalyticsItem call({
    String? name,
    int? lastUpdatedInMs,
    int? roleLevel,
    List<ItemSubCat>? options,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfAnalyticsItem.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfAnalyticsItem.copyWith.fieldName(...)`
class _$AnalyticsItemCWProxyImpl implements _$AnalyticsItemCWProxy {
  const _$AnalyticsItemCWProxyImpl(this._value);

  final AnalyticsItem _value;

  @override
  AnalyticsItem name(String name) => this(name: name);

  @override
  AnalyticsItem lastUpdatedInMs(int lastUpdatedInMs) =>
      this(lastUpdatedInMs: lastUpdatedInMs);

  @override
  AnalyticsItem roleLevel(int roleLevel) => this(roleLevel: roleLevel);

  @override
  AnalyticsItem options(List<ItemSubCat>? options) => this(options: options);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AnalyticsItem(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AnalyticsItem(...).copyWith(id: 12, name: "My name")
  /// ````
  AnalyticsItem call({
    Object? name = const $CopyWithPlaceholder(),
    Object? lastUpdatedInMs = const $CopyWithPlaceholder(),
    Object? roleLevel = const $CopyWithPlaceholder(),
    Object? options = const $CopyWithPlaceholder(),
  }) {
    return AnalyticsItem(
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      lastUpdatedInMs: lastUpdatedInMs == const $CopyWithPlaceholder() ||
              lastUpdatedInMs == null
          // ignore: unnecessary_non_null_assertion
          ? _value.lastUpdatedInMs!
          // ignore: cast_nullable_to_non_nullable
          : lastUpdatedInMs as int,
      roleLevel: roleLevel == const $CopyWithPlaceholder() || roleLevel == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roleLevel!
          // ignore: cast_nullable_to_non_nullable
          : roleLevel as int,
      options: options == const $CopyWithPlaceholder()
          ? _value.options
          // ignore: cast_nullable_to_non_nullable
          : options as List<ItemSubCat>?,
    );
  }
}

extension $AnalyticsItemCopyWith on AnalyticsItem {
  /// Returns a callable class that can be used as follows: `instanceOfAnalyticsItem.copyWith(...)` or like so:`instanceOfAnalyticsItem.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$AnalyticsItemCWProxy get copyWith => _$AnalyticsItemCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsItem _$AnalyticsItemFromJson(Map json) => AnalyticsItem(
      name: json['name'] as String,
      lastUpdatedInMs: json['last_updated'] as int,
      roleLevel: json['roleLevel'] as int,
    );

Map<String, dynamic> _$AnalyticsItemToJson(AnalyticsItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'last_updated': instance.lastUpdatedInMs,
      'roleLevel': instance.roleLevel,
    };

AnalyticsHome _$AnalyticsHomeFromJson(Map json) => AnalyticsHome(
      items: (json['items'] as List<dynamic>)
          .map((e) =>
              AnalyticsItem.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$AnalyticsHomeToJson(AnalyticsHome instance) =>
    <String, dynamic>{
      'items': instance.items.map((e) => e.toJson()).toList(),
    };

AnalyticsData _$AnalyticsDataFromJson(Map json) => AnalyticsData(
      caste: _casteFromJson(json['caste'] as Map),
      votes: _votingFromJson(json['voting_pref'] as Map),
    );

AnalyticsResponse _$AnalyticsResponseFromJson(Map json) => AnalyticsResponse(
      data: AnalyticsData.fromJson(
          Map<String, dynamic>.from(json['data'] as Map)),
    );
