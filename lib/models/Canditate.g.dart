// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'Canditate.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$CandidateCWProxy {
  Candidate gender(String? gender);

  Candidate address_2(String? address_2);

  Candidate mobile_no(String? mobile_no);

  Candidate rln_name_v1(String? rln_name_v1);

  Candidate rln_lastname_en(String? rln_lastname_en);

  Candidate name_v1(String? name_v1);

  Candidate epic_no(String epic_no);

  Candidate polling_station_2(String? polling_station_2);

  Candidate id(String id);

  Candidate lastname_v1(String? lastname_v1);

  Candidate address(String? address);

  Candidate rln_type(String? rln_type);

  Candidate part_no(String? part_no);

  Candidate dob(String? dob);

  Candidate house_no(String? house_no);

  Candidate house_no_v1(String? house_no_v1);

  Candidate sl_no_part(String? sl_no_part);

  Candidate rln_lastname_v1(String? rln_lastname_v1);

  Candidate polling_station(String? polling_station);

  Candidate section_no(String? section_no);

  Candidate ac_no(String? ac_no);

  Candidate age(String? age);

  Candidate name_en(String? name_en);

  Candidate lastname_en(String? lastname_en);

  Candidate rln_name_en(String? rln_name_en);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Candidate(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Candidate(...).copyWith(id: 12, name: "My name")
  /// ````
  Candidate call({
    String? gender,
    String? address_2,
    String? mobile_no,
    String? rln_name_v1,
    String? rln_lastname_en,
    String? name_v1,
    String? epic_no,
    String? polling_station_2,
    String? id,
    String? lastname_v1,
    String? address,
    String? rln_type,
    String? part_no,
    String? dob,
    String? house_no,
    String? house_no_v1,
    String? sl_no_part,
    String? rln_lastname_v1,
    String? polling_station,
    String? section_no,
    String? ac_no,
    String? age,
    String? name_en,
    String? lastname_en,
    String? rln_name_en,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfCandidate.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfCandidate.copyWith.fieldName(...)`
class _$CandidateCWProxyImpl implements _$CandidateCWProxy {
  const _$CandidateCWProxyImpl(this._value);

  final Candidate _value;

  @override
  Candidate gender(String? gender) => this(gender: gender);

  @override
  Candidate address_2(String? address_2) => this(address_2: address_2);

  @override
  Candidate mobile_no(String? mobile_no) => this(mobile_no: mobile_no);

  @override
  Candidate rln_name_v1(String? rln_name_v1) => this(rln_name_v1: rln_name_v1);

  @override
  Candidate rln_lastname_en(String? rln_lastname_en) =>
      this(rln_lastname_en: rln_lastname_en);

  @override
  Candidate name_v1(String? name_v1) => this(name_v1: name_v1);

  @override
  Candidate epic_no(String epic_no) => this(epic_no: epic_no);

  @override
  Candidate polling_station_2(String? polling_station_2) =>
      this(polling_station_2: polling_station_2);

  @override
  Candidate id(String id) => this(id: id);

  @override
  Candidate lastname_v1(String? lastname_v1) => this(lastname_v1: lastname_v1);

  @override
  Candidate address(String? address) => this(address: address);

  @override
  Candidate rln_type(String? rln_type) => this(rln_type: rln_type);

  @override
  Candidate part_no(String? part_no) => this(part_no: part_no);

  @override
  Candidate dob(String? dob) => this(dob: dob);

  @override
  Candidate house_no(String? house_no) => this(house_no: house_no);

  @override
  Candidate house_no_v1(String? house_no_v1) => this(house_no_v1: house_no_v1);

  @override
  Candidate sl_no_part(String? sl_no_part) => this(sl_no_part: sl_no_part);

  @override
  Candidate rln_lastname_v1(String? rln_lastname_v1) =>
      this(rln_lastname_v1: rln_lastname_v1);

  @override
  Candidate polling_station(String? polling_station) =>
      this(polling_station: polling_station);

  @override
  Candidate section_no(String? section_no) => this(section_no: section_no);

  @override
  Candidate ac_no(String? ac_no) => this(ac_no: ac_no);

  @override
  Candidate age(String? age) => this(age: age);

  @override
  Candidate name_en(String? name_en) => this(name_en: name_en);

  @override
  Candidate lastname_en(String? lastname_en) => this(lastname_en: lastname_en);

  @override
  Candidate rln_name_en(String? rln_name_en) => this(rln_name_en: rln_name_en);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Candidate(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Candidate(...).copyWith(id: 12, name: "My name")
  /// ````
  Candidate call({
    Object? gender = const $CopyWithPlaceholder(),
    Object? address_2 = const $CopyWithPlaceholder(),
    Object? mobile_no = const $CopyWithPlaceholder(),
    Object? rln_name_v1 = const $CopyWithPlaceholder(),
    Object? rln_lastname_en = const $CopyWithPlaceholder(),
    Object? name_v1 = const $CopyWithPlaceholder(),
    Object? epic_no = const $CopyWithPlaceholder(),
    Object? polling_station_2 = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? lastname_v1 = const $CopyWithPlaceholder(),
    Object? address = const $CopyWithPlaceholder(),
    Object? rln_type = const $CopyWithPlaceholder(),
    Object? part_no = const $CopyWithPlaceholder(),
    Object? dob = const $CopyWithPlaceholder(),
    Object? house_no = const $CopyWithPlaceholder(),
    Object? house_no_v1 = const $CopyWithPlaceholder(),
    Object? sl_no_part = const $CopyWithPlaceholder(),
    Object? rln_lastname_v1 = const $CopyWithPlaceholder(),
    Object? polling_station = const $CopyWithPlaceholder(),
    Object? section_no = const $CopyWithPlaceholder(),
    Object? ac_no = const $CopyWithPlaceholder(),
    Object? age = const $CopyWithPlaceholder(),
    Object? name_en = const $CopyWithPlaceholder(),
    Object? lastname_en = const $CopyWithPlaceholder(),
    Object? rln_name_en = const $CopyWithPlaceholder(),
  }) {
    return Candidate(
      gender: gender == const $CopyWithPlaceholder()
          ? _value.gender
          // ignore: cast_nullable_to_non_nullable
          : gender as String?,
      address_2: address_2 == const $CopyWithPlaceholder()
          ? _value.address_2
          // ignore: cast_nullable_to_non_nullable
          : address_2 as String?,
      mobile_no: mobile_no == const $CopyWithPlaceholder()
          ? _value.mobile_no
          // ignore: cast_nullable_to_non_nullable
          : mobile_no as String?,
      rln_name_v1: rln_name_v1 == const $CopyWithPlaceholder()
          ? _value.rln_name_v1
          // ignore: cast_nullable_to_non_nullable
          : rln_name_v1 as String?,
      rln_lastname_en: rln_lastname_en == const $CopyWithPlaceholder()
          ? _value.rln_lastname_en
          // ignore: cast_nullable_to_non_nullable
          : rln_lastname_en as String?,
      name_v1: name_v1 == const $CopyWithPlaceholder()
          ? _value.name_v1
          // ignore: cast_nullable_to_non_nullable
          : name_v1 as String?,
      epic_no: epic_no == const $CopyWithPlaceholder() || epic_no == null
          // ignore: unnecessary_non_null_assertion
          ? _value.epic_no!
          // ignore: cast_nullable_to_non_nullable
          : epic_no as String,
      polling_station_2: polling_station_2 == const $CopyWithPlaceholder()
          ? _value.polling_station_2
          // ignore: cast_nullable_to_non_nullable
          : polling_station_2 as String?,
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      lastname_v1: lastname_v1 == const $CopyWithPlaceholder()
          ? _value.lastname_v1
          // ignore: cast_nullable_to_non_nullable
          : lastname_v1 as String?,
      address: address == const $CopyWithPlaceholder()
          ? _value.address
          // ignore: cast_nullable_to_non_nullable
          : address as String?,
      rln_type: rln_type == const $CopyWithPlaceholder()
          ? _value.rln_type
          // ignore: cast_nullable_to_non_nullable
          : rln_type as String?,
      part_no: part_no == const $CopyWithPlaceholder()
          ? _value.part_no
          // ignore: cast_nullable_to_non_nullable
          : part_no as String?,
      dob: dob == const $CopyWithPlaceholder()
          ? _value.dob
          // ignore: cast_nullable_to_non_nullable
          : dob as String?,
      house_no: house_no == const $CopyWithPlaceholder()
          ? _value.house_no
          // ignore: cast_nullable_to_non_nullable
          : house_no as String?,
      house_no_v1: house_no_v1 == const $CopyWithPlaceholder()
          ? _value.house_no_v1
          // ignore: cast_nullable_to_non_nullable
          : house_no_v1 as String?,
      sl_no_part: sl_no_part == const $CopyWithPlaceholder()
          ? _value.sl_no_part
          // ignore: cast_nullable_to_non_nullable
          : sl_no_part as String?,
      rln_lastname_v1: rln_lastname_v1 == const $CopyWithPlaceholder()
          ? _value.rln_lastname_v1
          // ignore: cast_nullable_to_non_nullable
          : rln_lastname_v1 as String?,
      polling_station: polling_station == const $CopyWithPlaceholder()
          ? _value.polling_station
          // ignore: cast_nullable_to_non_nullable
          : polling_station as String?,
      section_no: section_no == const $CopyWithPlaceholder()
          ? _value.section_no
          // ignore: cast_nullable_to_non_nullable
          : section_no as String?,
      ac_no: ac_no == const $CopyWithPlaceholder()
          ? _value.ac_no
          // ignore: cast_nullable_to_non_nullable
          : ac_no as String?,
      age: age == const $CopyWithPlaceholder()
          ? _value.age
          // ignore: cast_nullable_to_non_nullable
          : age as String?,
      name_en: name_en == const $CopyWithPlaceholder()
          ? _value.name_en
          // ignore: cast_nullable_to_non_nullable
          : name_en as String?,
      lastname_en: lastname_en == const $CopyWithPlaceholder()
          ? _value.lastname_en
          // ignore: cast_nullable_to_non_nullable
          : lastname_en as String?,
      rln_name_en: rln_name_en == const $CopyWithPlaceholder()
          ? _value.rln_name_en
          // ignore: cast_nullable_to_non_nullable
          : rln_name_en as String?,
    );
  }
}

extension $CandidateCopyWith on Candidate {
  /// Returns a callable class that can be used as follows: `instanceOfCandidate.copyWith(...)` or like so:`instanceOfCandidate.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$CandidateCWProxy get copyWith => _$CandidateCWProxyImpl(this);
}

abstract class _$AllCandidatesCWProxy {
  AllCandidates candidates(List<Candidate> candidates);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AllCandidates(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AllCandidates(...).copyWith(id: 12, name: "My name")
  /// ````
  AllCandidates call({
    List<Candidate>? candidates,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfAllCandidates.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfAllCandidates.copyWith.fieldName(...)`
class _$AllCandidatesCWProxyImpl implements _$AllCandidatesCWProxy {
  const _$AllCandidatesCWProxyImpl(this._value);

  final AllCandidates _value;

  @override
  AllCandidates candidates(List<Candidate> candidates) =>
      this(candidates: candidates);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AllCandidates(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AllCandidates(...).copyWith(id: 12, name: "My name")
  /// ````
  AllCandidates call({
    Object? candidates = const $CopyWithPlaceholder(),
  }) {
    return AllCandidates(
      candidates:
          candidates == const $CopyWithPlaceholder() || candidates == null
              // ignore: unnecessary_non_null_assertion
              ? _value.candidates!
              // ignore: cast_nullable_to_non_nullable
              : candidates as List<Candidate>,
    );
  }
}

extension $AllCandidatesCopyWith on AllCandidates {
  /// Returns a callable class that can be used as follows: `instanceOfAllCandidates.copyWith(...)` or like so:`instanceOfAllCandidates.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$AllCandidatesCWProxy get copyWith => _$AllCandidatesCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Candidate _$CandidateFromJson(Map json) => Candidate(
      gender: json['gender'] as String?,
      address_2: json['address_2'] as String?,
      mobile_no: json['mobile_no'] as String?,
      rln_name_v1: json['rln_name_v1'] as String?,
      rln_lastname_en: json['rln_lastname_en'] as String?,
      name_v1: json['name_v1'] as String?,
      epic_no: json['epic_no'] as String,
      polling_station_2: json['polling_station_2'] as String?,
      id: json['id'] as String,
      lastname_v1: json['lastname_v1'] as String?,
      address: json['address'] as String?,
      rln_type: json['rln_type'] as String?,
      part_no: json['part_no'] as String?,
      dob: json['dob'] as String?,
      house_no: json['house_no'] as String?,
      house_no_v1: json['house_no_v1'] as String?,
      sl_no_part: json['sl_no_part'] as String?,
      rln_lastname_v1: json['rln_lastname_v1'] as String?,
      polling_station: json['polling_station'] as String?,
      section_no: json['section_no'] as String?,
      ac_no: json['ac_no'] as String?,
      age: json['age'] as String?,
      name_en: json['name_en'] as String?,
      lastname_en: json['lastname_en'] as String?,
      rln_name_en: json['rln_name_en'] as String?,
    );

Map<String, dynamic> _$CandidateToJson(Candidate instance) => <String, dynamic>{
      'gender': instance.gender,
      'address_2': instance.address_2,
      'mobile_no': instance.mobile_no,
      'rln_name_v1': instance.rln_name_v1,
      'rln_lastname_en': instance.rln_lastname_en,
      'name_v1': instance.name_v1,
      'epic_no': instance.epic_no,
      'polling_station_2': instance.polling_station_2,
      'id': instance.id,
      'lastname_v1': instance.lastname_v1,
      'address': instance.address,
      'rln_type': instance.rln_type,
      'part_no': instance.part_no,
      'dob': instance.dob,
      'house_no': instance.house_no,
      'house_no_v1': instance.house_no_v1,
      'sl_no_part': instance.sl_no_part,
      'rln_lastname_v1': instance.rln_lastname_v1,
      'polling_station': instance.polling_station,
      'section_no': instance.section_no,
      'ac_no': instance.ac_no,
      'age': instance.age,
      'name_en': instance.name_en,
      'lastname_en': instance.lastname_en,
      'rln_name_en': instance.rln_name_en,
    };

AllCandidates _$AllCandidatesFromJson(Map json) => AllCandidates(
      candidates: (json['candidates'] as List<dynamic>)
          .map((e) => Candidate.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$AllCandidatesToJson(AllCandidates instance) =>
    <String, dynamic>{
      'candidates': instance.candidates.map((e) => e.toJson()).toList(),
    };
