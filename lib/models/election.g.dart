// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'election.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$ElectoralProfileCWProxy {
  ElectoralProfile id(String id);

  ElectoralProfile name(String name);

  ElectoralProfile gender(String gender);

  ElectoralProfile age(int age);

  ElectoralProfile ac_no(int ac_no);

  ElectoralProfile section_no(int section_no);

  ElectoralProfile sl_no_part(int sl_no_part);

  ElectoralProfile part_no(int part_no);

  ElectoralProfile voting_pref(String? voting_pref);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ElectoralProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ElectoralProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  ElectoralProfile call({
    String? id,
    String? name,
    String? gender,
    int? age,
    int? ac_no,
    int? section_no,
    int? sl_no_part,
    int? part_no,
    String? voting_pref,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfElectoralProfile.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfElectoralProfile.copyWith.fieldName(...)`
class _$ElectoralProfileCWProxyImpl implements _$ElectoralProfileCWProxy {
  const _$ElectoralProfileCWProxyImpl(this._value);

  final ElectoralProfile _value;

  @override
  ElectoralProfile id(String id) => this(id: id);

  @override
  ElectoralProfile name(String name) => this(name: name);

  @override
  ElectoralProfile gender(String gender) => this(gender: gender);

  @override
  ElectoralProfile age(int age) => this(age: age);

  @override
  ElectoralProfile ac_no(int ac_no) => this(ac_no: ac_no);

  @override
  ElectoralProfile section_no(int section_no) => this(section_no: section_no);

  @override
  ElectoralProfile sl_no_part(int sl_no_part) => this(sl_no_part: sl_no_part);

  @override
  ElectoralProfile part_no(int part_no) => this(part_no: part_no);

  @override
  ElectoralProfile voting_pref(String? voting_pref) =>
      this(voting_pref: voting_pref);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ElectoralProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ElectoralProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  ElectoralProfile call({
    Object? id = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? gender = const $CopyWithPlaceholder(),
    Object? age = const $CopyWithPlaceholder(),
    Object? ac_no = const $CopyWithPlaceholder(),
    Object? section_no = const $CopyWithPlaceholder(),
    Object? sl_no_part = const $CopyWithPlaceholder(),
    Object? part_no = const $CopyWithPlaceholder(),
    Object? voting_pref = const $CopyWithPlaceholder(),
  }) {
    return ElectoralProfile(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      gender: gender == const $CopyWithPlaceholder() || gender == null
          // ignore: unnecessary_non_null_assertion
          ? _value.gender!
          // ignore: cast_nullable_to_non_nullable
          : gender as String,
      age: age == const $CopyWithPlaceholder() || age == null
          // ignore: unnecessary_non_null_assertion
          ? _value.age!
          // ignore: cast_nullable_to_non_nullable
          : age as int,
      ac_no: ac_no == const $CopyWithPlaceholder() || ac_no == null
          // ignore: unnecessary_non_null_assertion
          ? _value.ac_no!
          // ignore: cast_nullable_to_non_nullable
          : ac_no as int,
      section_no:
          section_no == const $CopyWithPlaceholder() || section_no == null
              // ignore: unnecessary_non_null_assertion
              ? _value.section_no!
              // ignore: cast_nullable_to_non_nullable
              : section_no as int,
      sl_no_part:
          sl_no_part == const $CopyWithPlaceholder() || sl_no_part == null
              // ignore: unnecessary_non_null_assertion
              ? _value.sl_no_part!
              // ignore: cast_nullable_to_non_nullable
              : sl_no_part as int,
      part_no: part_no == const $CopyWithPlaceholder() || part_no == null
          // ignore: unnecessary_non_null_assertion
          ? _value.part_no!
          // ignore: cast_nullable_to_non_nullable
          : part_no as int,
      voting_pref: voting_pref == const $CopyWithPlaceholder()
          ? _value.voting_pref
          // ignore: cast_nullable_to_non_nullable
          : voting_pref as String?,
    );
  }
}

extension $ElectoralProfileCopyWith on ElectoralProfile {
  /// Returns a callable class that can be used as follows: `instanceOfElectoralProfile.copyWith(...)` or like so:`instanceOfElectoralProfile.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$ElectoralProfileCWProxy get copyWith => _$ElectoralProfileCWProxyImpl(this);
}

abstract class _$SearchResultCWProxy {
  SearchResult profiles(List<Candidate> profiles);

  SearchResult pageEnd(bool pageEnd);

  SearchResult nextOffset(String? nextOffset);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchResult(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchResult(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchResult call({
    List<Candidate>? profiles,
    bool? pageEnd,
    String? nextOffset,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSearchResult.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfSearchResult.copyWith.fieldName(...)`
class _$SearchResultCWProxyImpl implements _$SearchResultCWProxy {
  const _$SearchResultCWProxyImpl(this._value);

  final SearchResult _value;

  @override
  SearchResult profiles(List<Candidate> profiles) => this(profiles: profiles);

  @override
  SearchResult pageEnd(bool pageEnd) => this(pageEnd: pageEnd);

  @override
  SearchResult nextOffset(String? nextOffset) => this(nextOffset: nextOffset);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchResult(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchResult(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchResult call({
    Object? profiles = const $CopyWithPlaceholder(),
    Object? pageEnd = const $CopyWithPlaceholder(),
    Object? nextOffset = const $CopyWithPlaceholder(),
  }) {
    return SearchResult(
      profiles: profiles == const $CopyWithPlaceholder() || profiles == null
          // ignore: unnecessary_non_null_assertion
          ? _value.profiles!
          // ignore: cast_nullable_to_non_nullable
          : profiles as List<Candidate>,
      pageEnd: pageEnd == const $CopyWithPlaceholder() || pageEnd == null
          // ignore: unnecessary_non_null_assertion
          ? _value.pageEnd!
          // ignore: cast_nullable_to_non_nullable
          : pageEnd as bool,
      nextOffset: nextOffset == const $CopyWithPlaceholder()
          ? _value.nextOffset
          // ignore: cast_nullable_to_non_nullable
          : nextOffset as String?,
    );
  }
}

extension $SearchResultCopyWith on SearchResult {
  /// Returns a callable class that can be used as follows: `instanceOfSearchResult.copyWith(...)` or like so:`instanceOfSearchResult.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$SearchResultCWProxy get copyWith => _$SearchResultCWProxyImpl(this);
}

abstract class _$VoterProfileCWProxy {
  VoterProfile id(String id);

  VoterProfile epic_no(String epic_no);

  VoterProfile profile_url(String? profile_url);

  VoterProfile age(int age);

  VoterProfile gender(String gender);

  VoterProfile first_name(VString first_name);

  VoterProfile last_name(VString last_name);

  VoterProfile rel_first_name(VString rel_first_name);

  VoterProfile rel_last_name(VString rel_last_name);

  VoterProfile rel_type(String rel_type);

  VoterProfile address(VoterAddress address);

  VoterProfile election_info(ElectionInfo? election_info);

  VoterProfile part_name(VString? part_name);

  VoterProfile part_no(int? part_no);

  VoterProfile sl_no_part(int? sl_no_part);

  VoterProfile mobile_no(String mobile_no);

  VoterProfile family_count(int? family_count);

  VoterProfile influencer(bool? influencer);

  VoterProfile beneficiary(bool? beneficiary);

  VoterProfile beneficiary_type(String beneficiary_type);

  VoterProfile epic_list(List<String>? epic_list);

  VoterProfile dead(bool dead);

  VoterProfile voting_pref(VotingPref? voting_pref);

  VoterProfile resp_member(TeamMemberProfile? resp_member);

  VoterProfile caste(CastePref? caste);

  VoterProfile voted_last(bool voted_last);

  VoterProfile currently_present(bool currently_present);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VoterProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VoterProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  VoterProfile call({
    String? id,
    String? epic_no,
    String? profile_url,
    int? age,
    String? gender,
    VString? first_name,
    VString? last_name,
    VString? rel_first_name,
    VString? rel_last_name,
    String? rel_type,
    VoterAddress? address,
    ElectionInfo? election_info,
    VString? part_name,
    int? part_no,
    int? sl_no_part,
    String? mobile_no,
    int? family_count,
    bool? influencer,
    bool? beneficiary,
    String? beneficiary_type,
    List<String>? epic_list,
    bool? dead,
    VotingPref? voting_pref,
    TeamMemberProfile? resp_member,
    CastePref? caste,
    bool? voted_last,
    bool? currently_present,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfVoterProfile.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfVoterProfile.copyWith.fieldName(...)`
class _$VoterProfileCWProxyImpl implements _$VoterProfileCWProxy {
  const _$VoterProfileCWProxyImpl(this._value);

  final VoterProfile _value;

  @override
  VoterProfile id(String id) => this(id: id);

  @override
  VoterProfile epic_no(String epic_no) => this(epic_no: epic_no);

  @override
  VoterProfile profile_url(String? profile_url) =>
      this(profile_url: profile_url);

  @override
  VoterProfile age(int age) => this(age: age);

  @override
  VoterProfile gender(String gender) => this(gender: gender);

  @override
  VoterProfile first_name(VString first_name) => this(first_name: first_name);

  @override
  VoterProfile last_name(VString last_name) => this(last_name: last_name);

  @override
  VoterProfile rel_first_name(VString rel_first_name) =>
      this(rel_first_name: rel_first_name);

  @override
  VoterProfile rel_last_name(VString rel_last_name) =>
      this(rel_last_name: rel_last_name);

  @override
  VoterProfile rel_type(String rel_type) => this(rel_type: rel_type);

  @override
  VoterProfile address(VoterAddress address) => this(address: address);

  @override
  VoterProfile election_info(ElectionInfo? election_info) =>
      this(election_info: election_info);

  @override
  VoterProfile part_name(VString? part_name) => this(part_name: part_name);

  @override
  VoterProfile part_no(int? part_no) => this(part_no: part_no);

  @override
  VoterProfile sl_no_part(int? sl_no_part) => this(sl_no_part: sl_no_part);

  @override
  VoterProfile mobile_no(String mobile_no) => this(mobile_no: mobile_no);

  @override
  VoterProfile family_count(int? family_count) =>
      this(family_count: family_count);

  @override
  VoterProfile influencer(bool? influencer) => this(influencer: influencer);

  @override
  VoterProfile beneficiary(bool? beneficiary) => this(beneficiary: beneficiary);

  @override
  VoterProfile beneficiary_type(String beneficiary_type) =>
      this(beneficiary_type: beneficiary_type);

  @override
  VoterProfile epic_list(List<String>? epic_list) => this(epic_list: epic_list);

  @override
  VoterProfile dead(bool dead) => this(dead: dead);

  @override
  VoterProfile voting_pref(VotingPref? voting_pref) =>
      this(voting_pref: voting_pref);

  @override
  VoterProfile resp_member(TeamMemberProfile? resp_member) =>
      this(resp_member: resp_member);

  @override
  VoterProfile caste(CastePref? caste) => this(caste: caste);

  @override
  VoterProfile voted_last(bool voted_last) => this(voted_last: voted_last);

  @override
  VoterProfile currently_present(bool currently_present) =>
      this(currently_present: currently_present);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VoterProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VoterProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  VoterProfile call({
    Object? id = const $CopyWithPlaceholder(),
    Object? epic_no = const $CopyWithPlaceholder(),
    Object? profile_url = const $CopyWithPlaceholder(),
    Object? age = const $CopyWithPlaceholder(),
    Object? gender = const $CopyWithPlaceholder(),
    Object? first_name = const $CopyWithPlaceholder(),
    Object? last_name = const $CopyWithPlaceholder(),
    Object? rel_first_name = const $CopyWithPlaceholder(),
    Object? rel_last_name = const $CopyWithPlaceholder(),
    Object? rel_type = const $CopyWithPlaceholder(),
    Object? address = const $CopyWithPlaceholder(),
    Object? election_info = const $CopyWithPlaceholder(),
    Object? part_name = const $CopyWithPlaceholder(),
    Object? part_no = const $CopyWithPlaceholder(),
    Object? sl_no_part = const $CopyWithPlaceholder(),
    Object? mobile_no = const $CopyWithPlaceholder(),
    Object? family_count = const $CopyWithPlaceholder(),
    Object? influencer = const $CopyWithPlaceholder(),
    Object? beneficiary = const $CopyWithPlaceholder(),
    Object? beneficiary_type = const $CopyWithPlaceholder(),
    Object? epic_list = const $CopyWithPlaceholder(),
    Object? dead = const $CopyWithPlaceholder(),
    Object? voting_pref = const $CopyWithPlaceholder(),
    Object? resp_member = const $CopyWithPlaceholder(),
    Object? caste = const $CopyWithPlaceholder(),
    Object? voted_last = const $CopyWithPlaceholder(),
    Object? currently_present = const $CopyWithPlaceholder(),
  }) {
    return VoterProfile(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      epic_no: epic_no == const $CopyWithPlaceholder() || epic_no == null
          // ignore: unnecessary_non_null_assertion
          ? _value.epic_no!
          // ignore: cast_nullable_to_non_nullable
          : epic_no as String,
      profile_url: profile_url == const $CopyWithPlaceholder()
          ? _value.profile_url
          // ignore: cast_nullable_to_non_nullable
          : profile_url as String?,
      age: age == const $CopyWithPlaceholder() || age == null
          // ignore: unnecessary_non_null_assertion
          ? _value.age!
          // ignore: cast_nullable_to_non_nullable
          : age as int,
      gender: gender == const $CopyWithPlaceholder() || gender == null
          // ignore: unnecessary_non_null_assertion
          ? _value.gender!
          // ignore: cast_nullable_to_non_nullable
          : gender as String,
      first_name:
          first_name == const $CopyWithPlaceholder() || first_name == null
              // ignore: unnecessary_non_null_assertion
              ? _value.first_name!
              // ignore: cast_nullable_to_non_nullable
              : first_name as VString,
      last_name: last_name == const $CopyWithPlaceholder() || last_name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.last_name!
          // ignore: cast_nullable_to_non_nullable
          : last_name as VString,
      rel_first_name: rel_first_name == const $CopyWithPlaceholder() ||
              rel_first_name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.rel_first_name!
          // ignore: cast_nullable_to_non_nullable
          : rel_first_name as VString,
      rel_last_name:
          rel_last_name == const $CopyWithPlaceholder() || rel_last_name == null
              // ignore: unnecessary_non_null_assertion
              ? _value.rel_last_name!
              // ignore: cast_nullable_to_non_nullable
              : rel_last_name as VString,
      rel_type: rel_type == const $CopyWithPlaceholder() || rel_type == null
          // ignore: unnecessary_non_null_assertion
          ? _value.rel_type!
          // ignore: cast_nullable_to_non_nullable
          : rel_type as String,
      address: address == const $CopyWithPlaceholder() || address == null
          // ignore: unnecessary_non_null_assertion
          ? _value.address!
          // ignore: cast_nullable_to_non_nullable
          : address as VoterAddress,
      election_info: election_info == const $CopyWithPlaceholder()
          ? _value.election_info
          // ignore: cast_nullable_to_non_nullable
          : election_info as ElectionInfo?,
      part_name: part_name == const $CopyWithPlaceholder()
          ? _value.part_name
          // ignore: cast_nullable_to_non_nullable
          : part_name as VString?,
      part_no: part_no == const $CopyWithPlaceholder()
          ? _value.part_no
          // ignore: cast_nullable_to_non_nullable
          : part_no as int?,
      sl_no_part: sl_no_part == const $CopyWithPlaceholder()
          ? _value.sl_no_part
          // ignore: cast_nullable_to_non_nullable
          : sl_no_part as int?,
      mobile_no: mobile_no == const $CopyWithPlaceholder() || mobile_no == null
          // ignore: unnecessary_non_null_assertion
          ? _value.mobile_no!
          // ignore: cast_nullable_to_non_nullable
          : mobile_no as String,
      family_count: family_count == const $CopyWithPlaceholder()
          ? _value.family_count
          // ignore: cast_nullable_to_non_nullable
          : family_count as int?,
      influencer: influencer == const $CopyWithPlaceholder()
          ? _value.influencer
          // ignore: cast_nullable_to_non_nullable
          : influencer as bool?,
      beneficiary: beneficiary == const $CopyWithPlaceholder()
          ? _value.beneficiary
          // ignore: cast_nullable_to_non_nullable
          : beneficiary as bool?,
      beneficiary_type: beneficiary_type == const $CopyWithPlaceholder() ||
              beneficiary_type == null
          // ignore: unnecessary_non_null_assertion
          ? _value.beneficiary_type!
          // ignore: cast_nullable_to_non_nullable
          : beneficiary_type as String,
      epic_list: epic_list == const $CopyWithPlaceholder()
          ? _value.epic_list
          // ignore: cast_nullable_to_non_nullable
          : epic_list as List<String>?,
      dead: dead == const $CopyWithPlaceholder() || dead == null
          // ignore: unnecessary_non_null_assertion
          ? _value.dead!
          // ignore: cast_nullable_to_non_nullable
          : dead as bool,
      voting_pref: voting_pref == const $CopyWithPlaceholder()
          ? _value.voting_pref
          // ignore: cast_nullable_to_non_nullable
          : voting_pref as VotingPref?,
      resp_member: resp_member == const $CopyWithPlaceholder()
          ? _value.resp_member
          // ignore: cast_nullable_to_non_nullable
          : resp_member as TeamMemberProfile?,
      caste: caste == const $CopyWithPlaceholder()
          ? _value.caste
          // ignore: cast_nullable_to_non_nullable
          : caste as CastePref?,
      voted_last:
          voted_last == const $CopyWithPlaceholder() || voted_last == null
              // ignore: unnecessary_non_null_assertion
              ? _value.voted_last!
              // ignore: cast_nullable_to_non_nullable
              : voted_last as bool,
      currently_present: currently_present == const $CopyWithPlaceholder() ||
              currently_present == null
          // ignore: unnecessary_non_null_assertion
          ? _value.currently_present!
          // ignore: cast_nullable_to_non_nullable
          : currently_present as bool,
    );
  }
}

extension $VoterProfileCopyWith on VoterProfile {
  /// Returns a callable class that can be used as follows: `instanceOfVoterProfile.copyWith(...)` or like so:`instanceOfVoterProfile.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$VoterProfileCWProxy get copyWith => _$VoterProfileCWProxyImpl(this);
}

abstract class _$VoterFetchResultCWProxy {
  VoterFetchResult profile(VoterProfile profile);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VoterFetchResult(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VoterFetchResult(...).copyWith(id: 12, name: "My name")
  /// ````
  VoterFetchResult call({
    VoterProfile? profile,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfVoterFetchResult.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfVoterFetchResult.copyWith.fieldName(...)`
class _$VoterFetchResultCWProxyImpl implements _$VoterFetchResultCWProxy {
  const _$VoterFetchResultCWProxyImpl(this._value);

  final VoterFetchResult _value;

  @override
  VoterFetchResult profile(VoterProfile profile) => this(profile: profile);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VoterFetchResult(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VoterFetchResult(...).copyWith(id: 12, name: "My name")
  /// ````
  VoterFetchResult call({
    Object? profile = const $CopyWithPlaceholder(),
  }) {
    return VoterFetchResult(
      profile: profile == const $CopyWithPlaceholder() || profile == null
          // ignore: unnecessary_non_null_assertion
          ? _value.profile!
          // ignore: cast_nullable_to_non_nullable
          : profile as VoterProfile,
    );
  }
}

extension $VoterFetchResultCopyWith on VoterFetchResult {
  /// Returns a callable class that can be used as follows: `instanceOfVoterFetchResult.copyWith(...)` or like so:`instanceOfVoterFetchResult.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$VoterFetchResultCWProxy get copyWith => _$VoterFetchResultCWProxyImpl(this);
}

abstract class _$VotingPrefCWProxy {
  VotingPref id(String id);

  VotingPref text(String text);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VotingPref(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VotingPref(...).copyWith(id: 12, name: "My name")
  /// ````
  VotingPref call({
    String? id,
    String? text,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfVotingPref.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfVotingPref.copyWith.fieldName(...)`
class _$VotingPrefCWProxyImpl implements _$VotingPrefCWProxy {
  const _$VotingPrefCWProxyImpl(this._value);

  final VotingPref _value;

  @override
  VotingPref id(String id) => this(id: id);

  @override
  VotingPref text(String text) => this(text: text);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VotingPref(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VotingPref(...).copyWith(id: 12, name: "My name")
  /// ````
  VotingPref call({
    Object? id = const $CopyWithPlaceholder(),
    Object? text = const $CopyWithPlaceholder(),
  }) {
    return VotingPref(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      text: text == const $CopyWithPlaceholder() || text == null
          // ignore: unnecessary_non_null_assertion
          ? _value.text!
          // ignore: cast_nullable_to_non_nullable
          : text as String,
    );
  }
}

extension $VotingPrefCopyWith on VotingPref {
  /// Returns a callable class that can be used as follows: `instanceOfVotingPref.copyWith(...)` or like so:`instanceOfVotingPref.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$VotingPrefCWProxy get copyWith => _$VotingPrefCWProxyImpl(this);
}

abstract class _$CastePrefCWProxy {
  CastePref id(String id);

  CastePref text(String text);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `CastePref(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// CastePref(...).copyWith(id: 12, name: "My name")
  /// ````
  CastePref call({
    String? id,
    String? text,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfCastePref.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfCastePref.copyWith.fieldName(...)`
class _$CastePrefCWProxyImpl implements _$CastePrefCWProxy {
  const _$CastePrefCWProxyImpl(this._value);

  final CastePref _value;

  @override
  CastePref id(String id) => this(id: id);

  @override
  CastePref text(String text) => this(text: text);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `CastePref(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// CastePref(...).copyWith(id: 12, name: "My name")
  /// ````
  CastePref call({
    Object? id = const $CopyWithPlaceholder(),
    Object? text = const $CopyWithPlaceholder(),
  }) {
    return CastePref(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      text: text == const $CopyWithPlaceholder() || text == null
          // ignore: unnecessary_non_null_assertion
          ? _value.text!
          // ignore: cast_nullable_to_non_nullable
          : text as String,
    );
  }
}

extension $CastePrefCopyWith on CastePref {
  /// Returns a callable class that can be used as follows: `instanceOfCastePref.copyWith(...)` or like so:`instanceOfCastePref.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$CastePrefCWProxy get copyWith => _$CastePrefCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ElectoralProfile _$ElectoralProfileFromJson(Map json) => ElectoralProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      gender: json['gender'] as String,
      age: json['age'] as int,
      ac_no: json['ac_no'] as int,
      section_no: json['section_no'] as int,
      sl_no_part: json['sl_no_part'] as int,
      part_no: json['part_no'] as int,
      voting_pref: json['voting_pref'] as String? ?? '',
    );

Map<String, dynamic> _$ElectoralProfileToJson(ElectoralProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'gender': instance.gender,
      'age': instance.age,
      'ac_no': instance.ac_no,
      'section_no': instance.section_no,
      'sl_no_part': instance.sl_no_part,
      'part_no': instance.part_no,
      'voting_pref': instance.voting_pref,
    };

SearchResult _$SearchResultFromJson(Map json) => SearchResult(
      profiles: (json['profiles'] as List<dynamic>)
          .map((e) => Candidate.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      pageEnd: json['pageEnd'] as bool,
      nextOffset: json['nextOffset'] as String?,
    );

Map<String, dynamic> _$SearchResultToJson(SearchResult instance) =>
    <String, dynamic>{
      'profiles': instance.profiles.map((e) => e.toJson()).toList(),
      'pageEnd': instance.pageEnd,
      'nextOffset': instance.nextOffset,
    };

VString _$VStringFromJson(Map json) => VString(
      eng: json['eng'] as String,
      lkl: json['lkl'] as String,
    );

Map<String, dynamic> _$VStringToJson(VString instance) => <String, dynamic>{
      'eng': instance.eng,
      'lkl': instance.lkl,
    };

VoterAddress _$VoterAddressFromJson(Map json) => VoterAddress(
      address:
          VString.fromJson(Map<String, dynamic>.from(json['address'] as Map)),
      booth: VString.fromJson(Map<String, dynamic>.from(json['booth'] as Map)),
      house_no:
          VString.fromJson(Map<String, dynamic>.from(json['house_no'] as Map)),
      village: json['village'] == null
          ? null
          : VString.fromJson(Map<String, dynamic>.from(json['village'] as Map)),
      pincode: json['pincode'] as int?,
    );

Map<String, dynamic> _$VoterAddressToJson(VoterAddress instance) =>
    <String, dynamic>{
      'address': instance.address.toJson(),
      'booth': instance.booth.toJson(),
      'house_no': instance.house_no.toJson(),
      'village': instance.village?.toJson(),
      'pincode': instance.pincode,
    };

ElectionInfo _$ElectionInfoFromJson(Map json) => ElectionInfo(
      state: json['state'] as String,
      assembly_name: json['assembly_name'] as String,
      voting_date: json['voting_date'] as String?,
    );

Map<String, dynamic> _$ElectionInfoToJson(ElectionInfo instance) =>
    <String, dynamic>{
      'state': instance.state,
      'assembly_name': instance.assembly_name,
      'voting_date': instance.voting_date,
    };

VoterProfile _$VoterProfileFromJson(Map json) => VoterProfile(
      id: json['id'] as String,
      epic_no: json['epic_no'] as String,
      profile_url: json['profile_url'] as String?,
      age: json['age'] as int,
      gender: json['gender'] as String,
      first_name: VString.fromJson(
          Map<String, dynamic>.from(json['first_name'] as Map)),
      last_name:
          VString.fromJson(Map<String, dynamic>.from(json['last_name'] as Map)),
      rel_first_name: VString.fromJson(
          Map<String, dynamic>.from(json['rel_first_name'] as Map)),
      rel_last_name: VString.fromJson(
          Map<String, dynamic>.from(json['rel_last_name'] as Map)),
      rel_type: json['rel_type'] as String,
      address: VoterAddress.fromJson(
          Map<String, dynamic>.from(json['address'] as Map)),
      election_info: json['election_info'] == null
          ? null
          : ElectionInfo.fromJson(
              Map<String, dynamic>.from(json['election_info'] as Map)),
      part_name: json['part_name'] == null
          ? null
          : VString.fromJson(
              Map<String, dynamic>.from(json['part_name'] as Map)),
      part_no: json['part_no'] as int?,
      sl_no_part: json['sl_no_part'] as int?,
      mobile_no: json['mobile_no'] as String,
      family_count: json['family_count'] as int?,
      influencer: json['influencer'] as bool?,
      beneficiary: json['beneficiary'] as bool?,
      beneficiary_type: json['beneficiary_type'] as String? ?? '',
      epic_list: (json['epic_list'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      dead: json['dead'] as bool? ?? false,
      voting_pref: json['voting_pref'] == null
          ? null
          : VotingPref.fromJson(
              Map<String, dynamic>.from(json['voting_pref'] as Map)),
      resp_member: json['resp_member'] == null
          ? null
          : TeamMemberProfile.fromJson(
              Map<String, dynamic>.from(json['resp_member'] as Map)),
      caste: json['caste'] == null
          ? null
          : CastePref.fromJson(Map<String, dynamic>.from(json['caste'] as Map)),
      voted_last: json['voted_last'] as bool,
      currently_present: json['currently_present'] as bool,
    );

Map<String, dynamic> _$VoterProfileToJson(VoterProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'epic_no': instance.epic_no,
      'profile_url': instance.profile_url,
      'age': instance.age,
      'gender': instance.gender,
      'first_name': instance.first_name.toJson(),
      'last_name': instance.last_name.toJson(),
      'rel_first_name': instance.rel_first_name.toJson(),
      'rel_last_name': instance.rel_last_name.toJson(),
      'rel_type': instance.rel_type,
      'address': instance.address.toJson(),
      'part_no': instance.part_no,
      'sl_no_part': instance.sl_no_part,
      'part_name': instance.part_name?.toJson(),
      'election_info': instance.election_info?.toJson(),
      'mobile_no': instance.mobile_no,
      'family_count': instance.family_count,
      'influencer': instance.influencer,
      'beneficiary': instance.beneficiary,
      'beneficiary_type': instance.beneficiary_type,
      'epic_list': instance.epic_list,
      'dead': instance.dead,
      'voting_pref': instance.voting_pref?.toJson(),
      'resp_member': instance.resp_member?.toJson(),
      'caste': instance.caste?.toJson(),
      'voted_last': instance.voted_last,
      'currently_present': instance.currently_present,
    };

VoterFetchResult _$VoterFetchResultFromJson(Map json) => VoterFetchResult(
      profile: VoterProfile.fromJson(
          Map<String, dynamic>.from(json['profile'] as Map)),
    );

Map<String, dynamic> _$VoterFetchResultToJson(VoterFetchResult instance) =>
    <String, dynamic>{
      'profile': instance.profile.toJson(),
    };

VotingPref _$VotingPrefFromJson(Map json) => VotingPref(
      id: json['id'] as String,
      text: json['text'] as String,
    );

Map<String, dynamic> _$VotingPrefToJson(VotingPref instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
    };

CreatePrefResponse _$CreatePrefResponseFromJson(Map json) => CreatePrefResponse(
      VotingPref.fromJson(Map<String, dynamic>.from(json['pref'] as Map)),
    );

Map<String, dynamic> _$CreatePrefResponseToJson(CreatePrefResponse instance) =>
    <String, dynamic>{
      'pref': instance.pref.toJson(),
    };

CastePref _$CastePrefFromJson(Map json) => CastePref(
      id: json['id'] as String,
      text: json['text'] as String,
    );

Map<String, dynamic> _$CastePrefToJson(CastePref instance) => <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
    };

CreateCasteResponse _$CreateCasteResponseFromJson(Map json) =>
    CreateCasteResponse(
      caste:
          CastePref.fromJson(Map<String, dynamic>.from(json['caste'] as Map)),
    );

Map<String, dynamic> _$CreateCasteResponseToJson(
        CreateCasteResponse instance) =>
    <String, dynamic>{
      'caste': instance.caste.toJson(),
    };
