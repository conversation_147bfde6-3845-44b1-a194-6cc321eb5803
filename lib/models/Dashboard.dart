import 'package:mla_connect/models/election.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'Dashboard.g.dart';

@JsonSerializable()
class Node {
  final String id; //rollId
  final String value; //show

  Node({required this.id, required this.value});

  factory Node.fromJson(Map<String, dynamic> json) => _$NodeFromJson(json);
  Map<String, dynamic> toJson() => _$NodeToJson(this);
}

@JsonSerializable()
class Role {
  final int roleLevel;
  final String alias; //shows
  final bool canAddUser;
  final List<Node> nodes;

  Role(
      {required this.roleLevel,
      required this.alias,
      required this.canAddUser,
      required this.nodes});

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);
  Map<String, dynamic> toJson() => _$RoleToJson(this);
}

@CopyWith()
@JsonSerializable()
class SelfProfile {
  final String id;
  @JsonKey(defaultValue: "")
  final String name;
  @JsonKey(defaultValue: "")
  final String gender;
  @JsonKey(defaultValue: "")
  final String dob;
  @JsonKey(defaultValue: "")
  final String phoneNumber;
  final String? roleId;
  final int? roleLevel;
  @JsonKey(defaultValue: "")
  final String roleAlias;
  @JsonKey(defaultValue: false)
  final bool adminUser;
  final String? profileUrl;
  final String? epicNo;
  final String? address;
  final String? about;
  final int? totalPoints;
  final String? referedBy;
  final String? position;
  final String? ward;

  // final String? village;

  String get designation {
    if (roleAlias.isNotEmpty) return roleAlias;
    return "Volunteer";
  }

  SelfProfile(
      {required this.id,
      required this.name,
      required this.phoneNumber,
      required this.roleId,
      required this.adminUser,
      required this.roleLevel,
      required this.roleAlias,
      required this.profileUrl,
      required this.epicNo,
      required this.address,
      required this.totalPoints,
      required this.gender,
      required this.dob,
      this.referedBy,
      this.position,
      this.ward,
      required this.about});

  factory SelfProfile.fromJson(Map<String, dynamic> json) =>
      _$SelfProfileFromJson(json);
  Map<String, dynamic> toJson() => _$SelfProfileToJson(this);
}

@CopyWith()
@JsonSerializable()
class DashboardResponse {
  @JsonKey(defaultValue: [])
  final List<Role> roles;
  final SelfProfile profile;
  final bool isMember;
  final bool isAdmin;
  final bool canAddUser;

  @JsonKey(defaultValue: [])
  final List<VotingPref> voting_pref;
  @JsonKey(defaultValue: [])
  final List<CastePref> castes;

  DashboardResponse(
      {required this.roles,
      required this.canAddUser,
      required this.profile,
      required this.isMember,
      required this.isAdmin,
      required this.voting_pref,
      required this.castes});

  factory DashboardResponse.fromJson(Map<String, dynamic> json) =>
      _$DashboardResponseFromJson(json);
  Map<String, dynamic> toJson() => _$DashboardResponseToJson(this);
}
