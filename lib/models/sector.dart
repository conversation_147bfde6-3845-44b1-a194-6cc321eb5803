import 'package:mla_connect/models/Dashboard.dart';
import 'package:json_annotation/json_annotation.dart';
part 'sector.g.dart';

@JsonSerializable()
class SectorData {
  final String id;
  final String name;
  @JsonKey(defaultValue: [])
  final List<String> booths;

  SectorData(this.id, this.name, this.booths);

  factory SectorData.fromJson(Map<String, dynamic> json) =>
      _$SectorDataFromJson(json);
  Map<String, dynamic> toJson() => _$SectorDataToJson(this);
}

@JsonSerializable()
class SectorFetchResult {
  @JsonKey(defaultValue: [])
  final List<SectorData> sectors;
  @JsonKey(defaultValue: [])
  final List<Node> availableBooths;

  SectorFetchResult(this.sectors, this.availableBooths);

  factory SectorFetchResult.fromJson(Map<String, dynamic> json) =>
      _$SectorFetchResultFromJson(json);
  Map<String, dynamic> toJson() => _$SectorFetchResultToJson(this);
}

@JsonSerializable()
class SectorCreateResult {
  final SectorData sector;

  SectorCreateResult(this.sector);

  factory SectorCreateResult.fromJson(Map<String, dynamic> json) =>
      _$SectorCreateResultFromJson(json);
  Map<String, dynamic> toJson() => _$SectorCreateResultToJson(this);
}
