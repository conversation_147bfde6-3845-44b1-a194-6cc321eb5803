import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'greet.g.dart';

@CopyWith()
@JsonSerializable()
class GreetPeopleList {
  final List<GreetPeopleData> data;
  GreetPeopleList({required this.data});
  factory GreetPeopleList.fromJson(Map<String, dynamic> json) =>
      _$GreetPeopleListFromJson(json);
  Map<String, dynamic> toJson() => _$GreetPeopleListToJson(this);
}

@CopyWith()
@JsonSerializable()
class GreetPeopleData {
  final String profileUrl;
  final int totalPoints;
  final String name;

  GreetPeopleData(
      {required this.name,
      required this.totalPoints,
      required this.profileUrl});

  factory GreetPeopleData.fromJson(Map<String, dynamic> json) =>
      _$GreetPeopleDataFromJson(json);
  Map<String, dynamic> toJson() => _$GreetPeopleDataToJson(this);
}
