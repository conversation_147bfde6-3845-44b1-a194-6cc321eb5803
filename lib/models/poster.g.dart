// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OverlapPoint _$OverlapPointFromJson(Map json) => OverlapPoint(
      dx: (json['dx'] as num).toDouble(),
      dy: (json['dy'] as num).toDouble(),
    );

Map<String, dynamic> _$OverlapPointToJson(OverlapPoint instance) =>
    <String, dynamic>{
      'dx': instance.dx,
      'dy': instance.dy,
    };

PosterNetworkModel _$PosterNetworkModelFromJson(Map json) => PosterNetworkModel(
      id: json['id'] as String,
      posterUrl: json['posterUrl'] as String,
      createTimeMs: json['createTimeMs'] as int?,
      overlapImageType: json['overlapImageType'] as String,
      overlapImageSize: (json['overlapImageSize'] as num).toDouble(),
      overlapPos: OverlapPoint.fromJson(
          Map<String, dynamic>.from(json['overlapPos'] as Map)),
      textAlign:
          $enumDecodeNullable(_$PosterTextAlignEnumMap, json['textAlign']) ??
              PosterTextAlign.Center,
      textFont:
          $enumDecodeNullable(_$PosterTextFontEnumMap, json['textFont']) ??
              PosterTextFont.Arial_24,
      textColor: fromColorJson(json['textColor'] as int),
      caption: json['caption'] as String,
      textBg: fromColorJson(json['textBg'] as int),
    );

Map<String, dynamic> _$PosterNetworkModelToJson(PosterNetworkModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'posterUrl': instance.posterUrl,
      'overlapImageType': instance.overlapImageType,
      'overlapImageSize': instance.overlapImageSize,
      'overlapPos': instance.overlapPos.toJson(),
      'createTimeMs': instance.createTimeMs,
      'caption': instance.caption,
      'textAlign': _$PosterTextAlignEnumMap[instance.textAlign]!,
      'textFont': _$PosterTextFontEnumMap[instance.textFont]!,
      'textColor': colorToJson(instance.textColor),
      'textBg': colorToJson(instance.textBg),
    };

const _$PosterTextAlignEnumMap = {
  PosterTextAlign.Left: 'Left',
  PosterTextAlign.Center: 'Center',
  PosterTextAlign.Right: 'Right',
};

const _$PosterTextFontEnumMap = {
  PosterTextFont.Arial_14: 'Arial_14',
  PosterTextFont.Arial_24: 'Arial_24',
  PosterTextFont.Arial_48: 'Arial_48',
};

PosterCreateResult _$PosterCreateResultFromJson(Map json) => PosterCreateResult(
      poster: PosterNetworkModel.fromJson(
          Map<String, dynamic>.from(json['poster'] as Map)),
    );

Map<String, dynamic> _$PosterCreateResultToJson(PosterCreateResult instance) =>
    <String, dynamic>{
      'poster': instance.poster.toJson(),
    };

PosterFetchResult _$PosterFetchResultFromJson(Map json) => PosterFetchResult(
      posters: (json['posters'] as List<dynamic>)
          .map((e) =>
              PosterNetworkModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      nextOffset: json['nextOffset'],
      pageEnd: json['pageEnd'] as bool,
    );

Map<String, dynamic> _$PosterFetchResultToJson(PosterFetchResult instance) =>
    <String, dynamic>{
      'posters': instance.posters.map((e) => e.toJson()).toList(),
      'nextOffset': instance.nextOffset,
      'pageEnd': instance.pageEnd,
    };

PosterApiRequest _$PosterApiRequestFromJson(Map json) => PosterApiRequest(
      action: json['action'] as String,
      poster: json['poster'] == null
          ? null
          : PosterNetworkModel.fromJson(
              Map<String, dynamic>.from(json['poster'] as Map)),
      id: json['id'] as String?,
      startOffset: json['startOffset'],
    );

Map<String, dynamic> _$PosterApiRequestToJson(PosterApiRequest instance) =>
    <String, dynamic>{
      'action': instance.action,
      'poster': instance.poster?.toJson(),
      'id': instance.id,
      'startOffset': instance.startOffset,
    };
