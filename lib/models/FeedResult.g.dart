// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FeedResult.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$FeedResultCWProxy {
  FeedResult posts(List<Post> posts);

  FeedResult pageEnd(bool pageEnd);

  FeedResult nextOffset(int? nextOffset);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `FeedResult(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// FeedResult(...).copyWith(id: 12, name: "My name")
  /// ````
  FeedResult call({
    List<Post>? posts,
    bool? pageEnd,
    int? nextOffset,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfFeedResult.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfFeedResult.copyWith.fieldName(...)`
class _$FeedResultCWProxyImpl implements _$FeedResultCWProxy {
  const _$FeedResultCWProxyImpl(this._value);

  final FeedResult _value;

  @override
  FeedResult posts(List<Post> posts) => this(posts: posts);

  @override
  FeedResult pageEnd(bool pageEnd) => this(pageEnd: pageEnd);

  @override
  FeedResult nextOffset(int? nextOffset) => this(nextOffset: nextOffset);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `FeedResult(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// FeedResult(...).copyWith(id: 12, name: "My name")
  /// ````
  FeedResult call({
    Object? posts = const $CopyWithPlaceholder(),
    Object? pageEnd = const $CopyWithPlaceholder(),
    Object? nextOffset = const $CopyWithPlaceholder(),
  }) {
    return FeedResult(
      posts: posts == const $CopyWithPlaceholder() || posts == null
          // ignore: unnecessary_non_null_assertion
          ? _value.posts!
          // ignore: cast_nullable_to_non_nullable
          : posts as List<Post>,
      pageEnd: pageEnd == const $CopyWithPlaceholder() || pageEnd == null
          // ignore: unnecessary_non_null_assertion
          ? _value.pageEnd!
          // ignore: cast_nullable_to_non_nullable
          : pageEnd as bool,
      nextOffset: nextOffset == const $CopyWithPlaceholder()
          ? _value.nextOffset
          // ignore: cast_nullable_to_non_nullable
          : nextOffset as int?,
    );
  }
}

extension $FeedResultCopyWith on FeedResult {
  /// Returns a callable class that can be used as follows: `instanceOfFeedResult.copyWith(...)` or like so:`instanceOfFeedResult.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$FeedResultCWProxy get copyWith => _$FeedResultCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FeedResult _$FeedResultFromJson(Map json) => FeedResult(
      posts: (json['posts'] as List<dynamic>)
          .map((e) => Post.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      pageEnd: json['pageEnd'] as bool,
      nextOffset: json['nextOffset'] as int?,
    );

Map<String, dynamic> _$FeedResultToJson(FeedResult instance) =>
    <String, dynamic>{
      'posts': instance.posts.map((e) => e.toJson()).toList(),
      'pageEnd': instance.pageEnd,
      'nextOffset': instance.nextOffset,
    };
