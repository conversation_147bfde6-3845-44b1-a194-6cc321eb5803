// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'Dashboard.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$SelfProfileCWProxy {
  SelfProfile id(String id);

  SelfProfile name(String name);

  SelfProfile phoneNumber(String phoneNumber);

  SelfProfile roleId(String? roleId);

  SelfProfile adminUser(bool adminUser);

  SelfProfile roleLevel(int? roleLevel);

  SelfProfile roleAlias(String roleAlias);

  SelfProfile profileUrl(String? profileUrl);

  SelfProfile epicNo(String? epicNo);

  SelfProfile address(String? address);

  SelfProfile totalPoints(int? totalPoints);

  SelfProfile gender(String gender);

  SelfProfile dob(String dob);

  SelfProfile referedBy(String? referedBy);

  SelfProfile position(String? position);

  SelfProfile ward(String? ward);

  SelfProfile about(String? about);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SelfProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SelfProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  SelfProfile call({
    String? id,
    String? name,
    String? phoneNumber,
    String? roleId,
    bool? adminUser,
    int? roleLevel,
    String? roleAlias,
    String? profileUrl,
    String? epicNo,
    String? address,
    int? totalPoints,
    String? gender,
    String? dob,
    String? referedBy,
    String? position,
    String? ward,
    String? about,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSelfProfile.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfSelfProfile.copyWith.fieldName(...)`
class _$SelfProfileCWProxyImpl implements _$SelfProfileCWProxy {
  const _$SelfProfileCWProxyImpl(this._value);

  final SelfProfile _value;

  @override
  SelfProfile id(String id) => this(id: id);

  @override
  SelfProfile name(String name) => this(name: name);

  @override
  SelfProfile phoneNumber(String phoneNumber) => this(phoneNumber: phoneNumber);

  @override
  SelfProfile roleId(String? roleId) => this(roleId: roleId);

  @override
  SelfProfile adminUser(bool adminUser) => this(adminUser: adminUser);

  @override
  SelfProfile roleLevel(int? roleLevel) => this(roleLevel: roleLevel);

  @override
  SelfProfile roleAlias(String roleAlias) => this(roleAlias: roleAlias);

  @override
  SelfProfile profileUrl(String? profileUrl) => this(profileUrl: profileUrl);

  @override
  SelfProfile epicNo(String? epicNo) => this(epicNo: epicNo);

  @override
  SelfProfile address(String? address) => this(address: address);

  @override
  SelfProfile totalPoints(int? totalPoints) => this(totalPoints: totalPoints);

  @override
  SelfProfile gender(String gender) => this(gender: gender);

  @override
  SelfProfile dob(String dob) => this(dob: dob);

  @override
  SelfProfile referedBy(String? referedBy) => this(referedBy: referedBy);

  @override
  SelfProfile position(String? position) => this(position: position);

  @override
  SelfProfile ward(String? ward) => this(ward: ward);

  @override
  SelfProfile about(String? about) => this(about: about);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SelfProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SelfProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  SelfProfile call({
    Object? id = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? phoneNumber = const $CopyWithPlaceholder(),
    Object? roleId = const $CopyWithPlaceholder(),
    Object? adminUser = const $CopyWithPlaceholder(),
    Object? roleLevel = const $CopyWithPlaceholder(),
    Object? roleAlias = const $CopyWithPlaceholder(),
    Object? profileUrl = const $CopyWithPlaceholder(),
    Object? epicNo = const $CopyWithPlaceholder(),
    Object? address = const $CopyWithPlaceholder(),
    Object? totalPoints = const $CopyWithPlaceholder(),
    Object? gender = const $CopyWithPlaceholder(),
    Object? dob = const $CopyWithPlaceholder(),
    Object? referedBy = const $CopyWithPlaceholder(),
    Object? position = const $CopyWithPlaceholder(),
    Object? ward = const $CopyWithPlaceholder(),
    Object? about = const $CopyWithPlaceholder(),
  }) {
    return SelfProfile(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      phoneNumber:
          phoneNumber == const $CopyWithPlaceholder() || phoneNumber == null
              // ignore: unnecessary_non_null_assertion
              ? _value.phoneNumber!
              // ignore: cast_nullable_to_non_nullable
              : phoneNumber as String,
      roleId: roleId == const $CopyWithPlaceholder()
          ? _value.roleId
          // ignore: cast_nullable_to_non_nullable
          : roleId as String?,
      adminUser: adminUser == const $CopyWithPlaceholder() || adminUser == null
          // ignore: unnecessary_non_null_assertion
          ? _value.adminUser!
          // ignore: cast_nullable_to_non_nullable
          : adminUser as bool,
      roleLevel: roleLevel == const $CopyWithPlaceholder()
          ? _value.roleLevel
          // ignore: cast_nullable_to_non_nullable
          : roleLevel as int?,
      roleAlias: roleAlias == const $CopyWithPlaceholder() || roleAlias == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roleAlias!
          // ignore: cast_nullable_to_non_nullable
          : roleAlias as String,
      profileUrl: profileUrl == const $CopyWithPlaceholder()
          ? _value.profileUrl
          // ignore: cast_nullable_to_non_nullable
          : profileUrl as String?,
      epicNo: epicNo == const $CopyWithPlaceholder()
          ? _value.epicNo
          // ignore: cast_nullable_to_non_nullable
          : epicNo as String?,
      address: address == const $CopyWithPlaceholder()
          ? _value.address
          // ignore: cast_nullable_to_non_nullable
          : address as String?,
      totalPoints: totalPoints == const $CopyWithPlaceholder()
          ? _value.totalPoints
          // ignore: cast_nullable_to_non_nullable
          : totalPoints as int?,
      gender: gender == const $CopyWithPlaceholder() || gender == null
          // ignore: unnecessary_non_null_assertion
          ? _value.gender!
          // ignore: cast_nullable_to_non_nullable
          : gender as String,
      dob: dob == const $CopyWithPlaceholder() || dob == null
          // ignore: unnecessary_non_null_assertion
          ? _value.dob!
          // ignore: cast_nullable_to_non_nullable
          : dob as String,
      referedBy: referedBy == const $CopyWithPlaceholder()
          ? _value.referedBy
          // ignore: cast_nullable_to_non_nullable
          : referedBy as String?,
      position: position == const $CopyWithPlaceholder()
          ? _value.position
          // ignore: cast_nullable_to_non_nullable
          : position as String?,
      ward: ward == const $CopyWithPlaceholder()
          ? _value.ward
          // ignore: cast_nullable_to_non_nullable
          : ward as String?,
      about: about == const $CopyWithPlaceholder()
          ? _value.about
          // ignore: cast_nullable_to_non_nullable
          : about as String?,
    );
  }
}

extension $SelfProfileCopyWith on SelfProfile {
  /// Returns a callable class that can be used as follows: `instanceOfSelfProfile.copyWith(...)` or like so:`instanceOfSelfProfile.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$SelfProfileCWProxy get copyWith => _$SelfProfileCWProxyImpl(this);
}

abstract class _$DashboardResponseCWProxy {
  DashboardResponse roles(List<Role> roles);

  DashboardResponse canAddUser(bool canAddUser);

  DashboardResponse profile(SelfProfile profile);

  DashboardResponse isMember(bool isMember);

  DashboardResponse isAdmin(bool isAdmin);

  DashboardResponse voting_pref(List<VotingPref> voting_pref);

  DashboardResponse castes(List<CastePref> castes);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `DashboardResponse(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// DashboardResponse(...).copyWith(id: 12, name: "My name")
  /// ````
  DashboardResponse call({
    List<Role>? roles,
    bool? canAddUser,
    SelfProfile? profile,
    bool? isMember,
    bool? isAdmin,
    List<VotingPref>? voting_pref,
    List<CastePref>? castes,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfDashboardResponse.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfDashboardResponse.copyWith.fieldName(...)`
class _$DashboardResponseCWProxyImpl implements _$DashboardResponseCWProxy {
  const _$DashboardResponseCWProxyImpl(this._value);

  final DashboardResponse _value;

  @override
  DashboardResponse roles(List<Role> roles) => this(roles: roles);

  @override
  DashboardResponse canAddUser(bool canAddUser) => this(canAddUser: canAddUser);

  @override
  DashboardResponse profile(SelfProfile profile) => this(profile: profile);

  @override
  DashboardResponse isMember(bool isMember) => this(isMember: isMember);

  @override
  DashboardResponse isAdmin(bool isAdmin) => this(isAdmin: isAdmin);

  @override
  DashboardResponse voting_pref(List<VotingPref> voting_pref) =>
      this(voting_pref: voting_pref);

  @override
  DashboardResponse castes(List<CastePref> castes) => this(castes: castes);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `DashboardResponse(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// DashboardResponse(...).copyWith(id: 12, name: "My name")
  /// ````
  DashboardResponse call({
    Object? roles = const $CopyWithPlaceholder(),
    Object? canAddUser = const $CopyWithPlaceholder(),
    Object? profile = const $CopyWithPlaceholder(),
    Object? isMember = const $CopyWithPlaceholder(),
    Object? isAdmin = const $CopyWithPlaceholder(),
    Object? voting_pref = const $CopyWithPlaceholder(),
    Object? castes = const $CopyWithPlaceholder(),
  }) {
    return DashboardResponse(
      roles: roles == const $CopyWithPlaceholder() || roles == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roles!
          // ignore: cast_nullable_to_non_nullable
          : roles as List<Role>,
      canAddUser:
          canAddUser == const $CopyWithPlaceholder() || canAddUser == null
              // ignore: unnecessary_non_null_assertion
              ? _value.canAddUser!
              // ignore: cast_nullable_to_non_nullable
              : canAddUser as bool,
      profile: profile == const $CopyWithPlaceholder() || profile == null
          // ignore: unnecessary_non_null_assertion
          ? _value.profile!
          // ignore: cast_nullable_to_non_nullable
          : profile as SelfProfile,
      isMember: isMember == const $CopyWithPlaceholder() || isMember == null
          // ignore: unnecessary_non_null_assertion
          ? _value.isMember!
          // ignore: cast_nullable_to_non_nullable
          : isMember as bool,
      isAdmin: isAdmin == const $CopyWithPlaceholder() || isAdmin == null
          // ignore: unnecessary_non_null_assertion
          ? _value.isAdmin!
          // ignore: cast_nullable_to_non_nullable
          : isAdmin as bool,
      voting_pref:
          voting_pref == const $CopyWithPlaceholder() || voting_pref == null
              // ignore: unnecessary_non_null_assertion
              ? _value.voting_pref!
              // ignore: cast_nullable_to_non_nullable
              : voting_pref as List<VotingPref>,
      castes: castes == const $CopyWithPlaceholder() || castes == null
          // ignore: unnecessary_non_null_assertion
          ? _value.castes!
          // ignore: cast_nullable_to_non_nullable
          : castes as List<CastePref>,
    );
  }
}

extension $DashboardResponseCopyWith on DashboardResponse {
  /// Returns a callable class that can be used as follows: `instanceOfDashboardResponse.copyWith(...)` or like so:`instanceOfDashboardResponse.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$DashboardResponseCWProxy get copyWith =>
      _$DashboardResponseCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Node _$NodeFromJson(Map json) => Node(
      id: json['id'] as String,
      value: json['value'] as String,
    );

Map<String, dynamic> _$NodeToJson(Node instance) => <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
    };

Role _$RoleFromJson(Map json) => Role(
      roleLevel: json['roleLevel'] as int,
      alias: json['alias'] as String,
      canAddUser: json['canAddUser'] as bool,
      nodes: (json['nodes'] as List<dynamic>)
          .map((e) => Node.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
      'roleLevel': instance.roleLevel,
      'alias': instance.alias,
      'canAddUser': instance.canAddUser,
      'nodes': instance.nodes.map((e) => e.toJson()).toList(),
    };

SelfProfile _$SelfProfileFromJson(Map json) => SelfProfile(
      id: json['id'] as String,
      name: json['name'] as String? ?? '',
      phoneNumber: json['phoneNumber'] as String? ?? '',
      roleId: json['roleId'] as String?,
      adminUser: json['adminUser'] as bool? ?? false,
      roleLevel: json['roleLevel'] as int?,
      roleAlias: json['roleAlias'] as String? ?? '',
      profileUrl: json['profileUrl'] as String?,
      epicNo: json['epicNo'] as String?,
      address: json['address'] as String?,
      totalPoints: json['totalPoints'] as int?,
      gender: json['gender'] as String? ?? '',
      dob: json['dob'] as String? ?? '',
      referedBy: json['referedBy'] as String?,
      position: json['position'] as String?,
      ward: json['ward'] as String?,
      about: json['about'] as String?,
    );

Map<String, dynamic> _$SelfProfileToJson(SelfProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'gender': instance.gender,
      'dob': instance.dob,
      'phoneNumber': instance.phoneNumber,
      'roleId': instance.roleId,
      'roleLevel': instance.roleLevel,
      'roleAlias': instance.roleAlias,
      'adminUser': instance.adminUser,
      'profileUrl': instance.profileUrl,
      'epicNo': instance.epicNo,
      'address': instance.address,
      'about': instance.about,
      'totalPoints': instance.totalPoints,
      'referedBy': instance.referedBy,
      'position': instance.position,
      'ward': instance.ward,
    };

DashboardResponse _$DashboardResponseFromJson(Map json) => DashboardResponse(
      roles: (json['roles'] as List<dynamic>?)
              ?.map((e) => Role.fromJson(Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
      canAddUser: json['canAddUser'] as bool,
      profile: SelfProfile.fromJson(
          Map<String, dynamic>.from(json['profile'] as Map)),
      isMember: json['isMember'] as bool,
      isAdmin: json['isAdmin'] as bool,
      voting_pref: (json['voting_pref'] as List<dynamic>?)
              ?.map((e) =>
                  VotingPref.fromJson(Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
      castes: (json['castes'] as List<dynamic>?)
              ?.map((e) =>
                  CastePref.fromJson(Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
    );

Map<String, dynamic> _$DashboardResponseToJson(DashboardResponse instance) =>
    <String, dynamic>{
      'roles': instance.roles.map((e) => e.toJson()).toList(),
      'profile': instance.profile.toJson(),
      'isMember': instance.isMember,
      'isAdmin': instance.isAdmin,
      'canAddUser': instance.canAddUser,
      'voting_pref': instance.voting_pref.map((e) => e.toJson()).toList(),
      'castes': instance.castes.map((e) => e.toJson()).toList(),
    };
