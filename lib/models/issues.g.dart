// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'issues.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Issue _$IssueFromJson(Map json) => Issue(
      id: json['id'] as String,
      text: json['text'] as String,
      authorId: json['authorId'] as String,
      createTimestamp: json['createTimestamp'] as int,
      state: $enumDecodeNullable(_$RESOLUTIONEnumMap, json['state']) ??
          RESOLUTION.UNRESOLVED,
      authorProfileUrl: json['authorProfileUrl'] as String?,
      authorName: json['authorName'] as String?,
      ticket_no: json['ticket_no'] as String,
      type: $enumDecodeNullable(_$ISSUE_TYPEEnumMap, json['type']) ??
          ISSUE_TYPE.TEXT,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$IssueToJson(Issue instance) => <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'authorId': instance.authorId,
      'createTimestamp': instance.createTimestamp,
      'state': _$RESOLUTIONEnumMap[instance.state]!,
      'authorProfileUrl': instance.authorProfileUrl,
      'authorName': instance.authorName,
      'ticket_no': instance.ticket_no,
      'type': _$ISSUE_TYPEEnumMap[instance.type]!,
      'imageUrl': instance.imageUrl,
    };

const _$RESOLUTIONEnumMap = {
  RESOLUTION.RESOLVED: 'resolved',
  RESOLUTION.UNRESOLVED: 'unresolved',
  RESOLUTION.FAKE: 'fake',
};

const _$ISSUE_TYPEEnumMap = {
  ISSUE_TYPE.TEXT: 'text',
  ISSUE_TYPE.IMAGE: 'image',
};

CreateIssueBody _$CreateIssueBodyFromJson(Map json) => CreateIssueBody(
      action: json['action'] as String? ?? "create",
      type: $enumDecode(_$ISSUE_TYPEEnumMap, json['type']),
      text: json['text'] as String,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$CreateIssueBodyToJson(CreateIssueBody instance) =>
    <String, dynamic>{
      'action': instance.action,
      'type': _$ISSUE_TYPEEnumMap[instance.type]!,
      'text': instance.text,
      'imageUrl': instance.imageUrl,
    };

CreateIssueResult _$CreateIssueResultFromJson(Map json) => CreateIssueResult(
      Issue.fromJson(Map<String, dynamic>.from(json['issue'] as Map)),
    );

Map<String, dynamic> _$CreateIssueResultToJson(CreateIssueResult instance) =>
    <String, dynamic>{
      'issue': instance.issue.toJson(),
    };

IssueFetchBpdy _$IssueFetchBpdyFromJson(Map json) => IssueFetchBpdy(
      myFeed: json['myFeed'] as bool,
      state: $enumDecode(_$RESOLUTIONEnumMap, json['state']),
      startOffset: json['startOffset'] as int?,
    );

Map<String, dynamic> _$IssueFetchBpdyToJson(IssueFetchBpdy instance) =>
    <String, dynamic>{
      'myFeed': instance.myFeed,
      'state': _$RESOLUTIONEnumMap[instance.state]!,
      'startOffset': instance.startOffset,
    };

IssueFetchResult _$IssueFetchResultFromJson(Map json) => IssueFetchResult(
      (json['issues'] as List<dynamic>)
          .map((e) => Issue.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      json['pageEnd'] as bool,
      json['nextOffset'] as int?,
    );

Map<String, dynamic> _$IssueFetchResultToJson(IssueFetchResult instance) =>
    <String, dynamic>{
      'issues': instance.issues.map((e) => e.toJson()).toList(),
      'pageEnd': instance.pageEnd,
      'nextOffset': instance.nextOffset,
    };

IssueResolveBody _$IssueResolveBodyFromJson(Map json) => IssueResolveBody(
      action: json['action'] as String? ?? "resolve",
      issue_id: json['issue_id'] as String,
      state: $enumDecode(_$RESOLUTIONEnumMap, json['state']),
    );

Map<String, dynamic> _$IssueResolveBodyToJson(IssueResolveBody instance) =>
    <String, dynamic>{
      'action': instance.action,
      'issue_id': instance.issue_id,
      'state': _$RESOLUTIONEnumMap[instance.state]!,
    };
