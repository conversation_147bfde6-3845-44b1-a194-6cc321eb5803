import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'issues.g.dart';

enum RESOLUTION {
  @JsonValue("resolved")
  RESOLVED,
  @JsonValue("unresolved")
  UNRESOLVED,
  @JsonValue("fake")
  FAKE
}

enum ISSUE_TYPE {
  @JsonValue("text")
  TEXT,
  @JsonValue("image")
  IMAGE
}

@JsonSerializable()
class Issue {
  final String id;
  final String text;
  final String authorId;
  final int createTimestamp;
  @Json<PERSON>ey(defaultValue: RESOLUTION.UNRESOLVED)
  final RESOLUTION state;
  final String? authorProfileUrl;
  final String? authorName;
  final String ticket_no;
  @JsonKey(defaultValue: ISSUE_TYPE.TEXT)
  final ISSUE_TYPE type;
  final String? imageUrl;

  Issue(
      {required this.id,
      required this.text,
      required this.authorId,
      required this.createTimestamp,
      required this.state,
      required this.authorProfileUrl,
      required this.authorName,
      required this.ticket_no,
      required this.type,
      required this.imageUrl});

  String formatedCreateTime() {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat
          .format(DateTime.fromMillisecondsSinceEpoch(createTimestamp));
    } catch (e) {
      return "";
    }
  }

  factory Issue.fromJson(Map<String, dynamic> json) => _$IssueFromJson(json);
  Map<String, dynamic> toJson() => _$IssueToJson(this);
}

@JsonSerializable()
class CreateIssueBody {
  final String action;
  final ISSUE_TYPE type;
  final String text;
  final String? imageUrl;

  CreateIssueBody({
    this.action = "create",
    required this.type,
    required this.text,
    this.imageUrl,
  });

  factory CreateIssueBody.fromJson(Map<String, dynamic> json) =>
      _$CreateIssueBodyFromJson(json);
  Map<String, dynamic> toJson() => _$CreateIssueBodyToJson(this);
}

@JsonSerializable()
class CreateIssueResult {
  final Issue issue;

  CreateIssueResult(this.issue);

  factory CreateIssueResult.fromJson(Map<String, dynamic> json) =>
      _$CreateIssueResultFromJson(json);
  Map<String, dynamic> toJson() => _$CreateIssueResultToJson(this);
}

@JsonSerializable()
class IssueFetchBpdy {
  final bool myFeed;
  final RESOLUTION state;
  final int? startOffset;

  IssueFetchBpdy(
      {required this.myFeed, required this.state, required this.startOffset});

  factory IssueFetchBpdy.fromJson(Map<String, dynamic> json) =>
      _$IssueFetchBpdyFromJson(json);
  Map<String, dynamic> toJson() => _$IssueFetchBpdyToJson(this);
}

@JsonSerializable()
class IssueFetchResult {
  final List<Issue> issues;
  final bool pageEnd;
  final int? nextOffset;

  IssueFetchResult(this.issues, this.pageEnd, this.nextOffset);

  factory IssueFetchResult.fromJson(Map<String, dynamic> json) =>
      _$IssueFetchResultFromJson(json);
  Map<String, dynamic> toJson() => _$IssueFetchResultToJson(this);
}

@JsonSerializable()
class IssueResolveBody {
  final String action;
  final String issue_id;
  final RESOLUTION state;
  IssueResolveBody({
    this.action = "resolve",
    required this.issue_id,
    required this.state,
  });

  factory IssueResolveBody.fromJson(Map<String, dynamic> json) =>
      _$IssueResolveBodyFromJson(json);
  Map<String, dynamic> toJson() => _$IssueResolveBodyToJson(this);
}
