import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'ChatMessage.dart';
part 'RoomList.g.dart';

@CopyWith()
@JsonSerializable()
class RoomList {
  final String roomId;
  final String name;
  final int lastMessageTimestamp;
  final ChatMessage lastMessage;
  final int? unseenCount;

  RoomList(
      {required this.name,
      required this.roomId,
      required this.lastMessage,
      required this.lastMessageTimestamp,
      this.unseenCount});

  String formatedCreateTime() {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat
          .format(DateTime.fromMillisecondsSinceEpoch(lastMessageTimestamp));
    } catch (e) {
      return "";
    }
  }

  factory RoomList.fromJson(Map<String, dynamic> json) =>
      _$RoomListFromJson(json);
  Map<String, dynamic> toJson() => _$RoomList<PERSON>o<PERSON>(this);
}
