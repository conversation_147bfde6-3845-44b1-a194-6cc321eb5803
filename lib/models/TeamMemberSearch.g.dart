// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'TeamMemberSearch.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$TeamMemberSearchCWProxy {
  TeamMemberSearch profiles(List<TeamMemberProfile> profiles);

  TeamMemberSearch pageEnd(bool pageEnd);

  TeamMemberSearch nextOffset(String? nextOffset);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `TeamMemberSearch(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// TeamMemberSearch(...).copyWith(id: 12, name: "My name")
  /// ````
  TeamMemberSearch call({
    List<TeamMemberProfile>? profiles,
    bool? pageEnd,
    String? nextOffset,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfTeamMemberSearch.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfTeamMemberSearch.copyWith.fieldName(...)`
class _$TeamMemberSearchCWProxyImpl implements _$TeamMemberSearchCWProxy {
  const _$TeamMemberSearchCWProxyImpl(this._value);

  final TeamMemberSearch _value;

  @override
  TeamMemberSearch profiles(List<TeamMemberProfile> profiles) =>
      this(profiles: profiles);

  @override
  TeamMemberSearch pageEnd(bool pageEnd) => this(pageEnd: pageEnd);

  @override
  TeamMemberSearch nextOffset(String? nextOffset) =>
      this(nextOffset: nextOffset);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `TeamMemberSearch(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// TeamMemberSearch(...).copyWith(id: 12, name: "My name")
  /// ````
  TeamMemberSearch call({
    Object? profiles = const $CopyWithPlaceholder(),
    Object? pageEnd = const $CopyWithPlaceholder(),
    Object? nextOffset = const $CopyWithPlaceholder(),
  }) {
    return TeamMemberSearch(
      profiles: profiles == const $CopyWithPlaceholder() || profiles == null
          // ignore: unnecessary_non_null_assertion
          ? _value.profiles!
          // ignore: cast_nullable_to_non_nullable
          : profiles as List<TeamMemberProfile>,
      pageEnd: pageEnd == const $CopyWithPlaceholder() || pageEnd == null
          // ignore: unnecessary_non_null_assertion
          ? _value.pageEnd!
          // ignore: cast_nullable_to_non_nullable
          : pageEnd as bool,
      nextOffset: nextOffset == const $CopyWithPlaceholder()
          ? _value.nextOffset
          // ignore: cast_nullable_to_non_nullable
          : nextOffset as String?,
    );
  }
}

extension $TeamMemberSearchCopyWith on TeamMemberSearch {
  /// Returns a callable class that can be used as follows: `instanceOfTeamMemberSearch.copyWith(...)` or like so:`instanceOfTeamMemberSearch.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$TeamMemberSearchCWProxy get copyWith => _$TeamMemberSearchCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TeamMemberSearch _$TeamMemberSearchFromJson(Map json) => TeamMemberSearch(
      profiles: (json['profiles'] as List<dynamic>)
          .map((e) =>
              TeamMemberProfile.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      pageEnd: json['pageEnd'] as bool,
      nextOffset: json['nextOffset'] as String?,
    );

Map<String, dynamic> _$TeamMemberSearchToJson(TeamMemberSearch instance) =>
    <String, dynamic>{
      'profiles': instance.profiles.map((e) => e.toJson()).toList(),
      'pageEnd': instance.pageEnd,
      'nextOffset': instance.nextOffset,
    };
