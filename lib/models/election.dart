import 'package:mla_connect/models/Canditate.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:mla_connect/models/TeamMemberProfile.dart';

part 'election.g.dart';

@CopyWith()
@JsonSerializable()
class ElectoralProfile {
  final String id;
  final String name;
  final String gender;
  final int age;
  // final int pincode;
  final int ac_no;
  final int section_no;
  final int sl_no_part;
  final int part_no;
  @JsonKey(defaultValue: "")
  final String? voting_pref;

  ElectoralProfile(
      {required this.id,
      required this.name,
      required this.gender,
      required this.age,
      // required this.pincode,
      required this.ac_no,
      required this.section_no,
      required this.sl_no_part,
      required this.part_no,
      required this.voting_pref});

  factory ElectoralProfile.fromJson(Map<String, dynamic> json) =>
      _$ElectoralProfileFromJson(json);
  Map<String, dynamic> toJson() => _$ElectoralProfileToJson(this);
}

@CopyWith()
@JsonSerializable()
class SearchResult {
  // final List<VoterProfile> profiles;
  final List<Candidate> profiles;
  // final List<ElectoralProfile> profiles;

  final bool pageEnd;
  final String? nextOffset;

  SearchResult({
    required this.profiles,
    required this.pageEnd,
    required this.nextOffset,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) =>
      _$SearchResultFromJson(json);
  Map<String, dynamic> toJson() => _$SearchResultToJson(this);
}

@JsonSerializable()
class VString extends Equatable {
  final String eng;
  final String lkl;

  VString({required this.eng, required this.lkl});

  factory VString.fromJson(Map<String, dynamic> json) =>
      _$VStringFromJson(json);
  Map<String, dynamic> toJson() => _$VStringToJson(this);

  @override
  List<Object?> get props => [eng, lkl];
}

@JsonSerializable()
class VoterAddress extends Equatable {
  final VString address;
  final VString booth;
  final VString house_no;
  final VString? village;
  final int? pincode;

  VoterAddress(
      {required this.address,
      required this.booth,
      required this.house_no,
      required this.village,
      required this.pincode});

  factory VoterAddress.fromJson(Map<String, dynamic> json) =>
      _$VoterAddressFromJson(json);
  Map<String, dynamic> toJson() => _$VoterAddressToJson(this);

  @override
  List<Object?> get props => [address, booth, house_no, village, pincode];
}

@JsonSerializable()
class ElectionInfo extends Equatable {
  final String state;
  final String assembly_name;
  final String? voting_date;
  ElectionInfo({
    required this.state,
    required this.assembly_name,
    this.voting_date,
  });

  factory ElectionInfo.fromJson(Map<String, dynamic> json) =>
      _$ElectionInfoFromJson(json);
  Map<String, dynamic> toJson() => _$ElectionInfoToJson(this);

  @override
  List<Object?> get props => [state, assembly_name, voting_date];
}

@CopyWith()
@JsonSerializable()
class VoterProfile extends Equatable {
  final String id;
  final String epic_no;
  final String? profile_url;
  final int age;
  final String gender;
  final VString first_name;
  final VString last_name;

  //Relative Details
  final VString rel_first_name;
  final VString rel_last_name;
  final String rel_type;

  final VoterAddress address;
  final int? part_no;
  final int? sl_no_part;

  final VString? part_name;
  final ElectionInfo? election_info;

  //Editable fields
  final String mobile_no;
  final int? family_count;
  final bool? influencer;
  final bool? beneficiary;
  @JsonKey(defaultValue: "")
  final String beneficiary_type;
  final List<String>? epic_list;
  @JsonKey(defaultValue: false)
  final bool dead;
  final VotingPref? voting_pref;
  final TeamMemberProfile? resp_member;
  final CastePref? caste;
  final bool voted_last;
  final bool currently_present;

  VoterProfile({
    required this.id,
    required this.epic_no,
    this.profile_url,
    required this.age,
    required this.gender,
    required this.first_name,
    required this.last_name,
    required this.rel_first_name,
    required this.rel_last_name,
    required this.rel_type,
    required this.address,
    this.election_info,
    this.part_name,
    this.part_no,
    this.sl_no_part,
    required this.mobile_no,
    this.family_count,
    this.influencer,
    this.beneficiary,
    required this.beneficiary_type,
    this.epic_list,
    required this.dead,
    this.voting_pref,
    this.resp_member,
    this.caste,
    required this.voted_last,
    required this.currently_present,
  });

  factory VoterProfile.fromJson(Map<String, dynamic> json) =>
      _$VoterProfileFromJson(json);
  Map<String, dynamic> toJson() => _$VoterProfileToJson(this);

  @override
  List<Object?> get props => [
        mobile_no,
        family_count,
        influencer,
        beneficiary,
        beneficiary_type,
        epic_list,
        dead,
        voting_pref,
        resp_member,
        caste,
        voted_last,
        currently_present
      ];
}

@CopyWith()
@JsonSerializable()
class VoterFetchResult {
  final VoterProfile profile;

  VoterFetchResult({required this.profile});
  factory VoterFetchResult.fromJson(Map<String, dynamic> json) =>
      _$VoterFetchResultFromJson(json);
  Map<String, dynamic> toJson() => _$VoterFetchResultToJson(this);
}

@CopyWith()
@JsonSerializable()
class VotingPref {
  final String id;
  final String text;

  VotingPref({required this.id, required this.text});

  factory VotingPref.fromJson(Map<String, dynamic> json) =>
      _$VotingPrefFromJson(json);
  Map<String, dynamic> toJson() => _$VotingPrefToJson(this);
}

@JsonSerializable()
class CreatePrefResponse {
  final VotingPref pref;

  CreatePrefResponse(this.pref);

  factory CreatePrefResponse.fromJson(Map<String, dynamic> json) =>
      _$CreatePrefResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePrefResponseToJson(this);
}

@CopyWith()
@JsonSerializable()
class CastePref extends Equatable {
  final String id;
  final String text;
  CastePref({
    required this.id,
    required this.text,
  });
  factory CastePref.fromJson(Map<String, dynamic> json) =>
      _$CastePrefFromJson(json);
  Map<String, dynamic> toJson() => _$CastePrefToJson(this);

  @override
  List<Object?> get props => [id, text];
}

@JsonSerializable()
class CreateCasteResponse {
  final CastePref caste;
  CreateCasteResponse({
    required this.caste,
  });

  factory CreateCasteResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateCasteResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CreateCasteResponseToJson(this);
}
