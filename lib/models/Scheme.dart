import 'package:json_annotation/json_annotation.dart';
part 'Scheme.g.dart';

@JsonSerializable()
class Scheme {
  String name;
  String gender;
  int age;
  String residence;
  String caste;
  String state;
  bool differentlyAbled;
  bool minority;
  bool student;
  bool employed;
  String occupation;
  bool bpl;
  double annualIncome;
  String category;

  Scheme({
    required this.name,
    required this.gender,
    required this.age,
    required this.residence,
    required this.caste,
    required this.state,
    required this.differentlyAbled,
    required this.minority,
    required this.student,
    required this.employed,
    required this.occupation,
    required this.bpl,
    required this.annualIncome,
    required this.category,
  });

  factory Scheme.fromJson(Map<String, dynamic> json) => _$SchemeFromJson(json);
  Map<String, dynamic> toJson() => _$SchemeToJson(this);
}
