// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'TeamMemberProfile.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$TeamMemberProfileCWProxy {
  TeamMemberProfile roleId(String roleId);

  TeamMemberProfile name(String name);

  TeamMemberProfile id(String id);

  TeamMemberProfile profileUrl(String? profileUrl);

  TeamMemberProfile roleLevel(int roleLevel);

  TeamMemberProfile roleAlias(String roleAlias);

  TeamMemberProfile adminUser(bool adminUser);

  TeamMemberProfile phoneNumber(String phoneNumber);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `TeamMemberProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// TeamMemberProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  TeamMemberProfile call({
    String? roleId,
    String? name,
    String? id,
    String? profileUrl,
    int? roleLevel,
    String? roleAlias,
    bool? adminUser,
    String? phoneNumber,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfTeamMemberProfile.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfTeamMemberProfile.copyWith.fieldName(...)`
class _$TeamMemberProfileCWProxyImpl implements _$TeamMemberProfileCWProxy {
  const _$TeamMemberProfileCWProxyImpl(this._value);

  final TeamMemberProfile _value;

  @override
  TeamMemberProfile roleId(String roleId) => this(roleId: roleId);

  @override
  TeamMemberProfile name(String name) => this(name: name);

  @override
  TeamMemberProfile id(String id) => this(id: id);

  @override
  TeamMemberProfile profileUrl(String? profileUrl) =>
      this(profileUrl: profileUrl);

  @override
  TeamMemberProfile roleLevel(int roleLevel) => this(roleLevel: roleLevel);

  @override
  TeamMemberProfile roleAlias(String roleAlias) => this(roleAlias: roleAlias);

  @override
  TeamMemberProfile adminUser(bool adminUser) => this(adminUser: adminUser);

  @override
  TeamMemberProfile phoneNumber(String phoneNumber) =>
      this(phoneNumber: phoneNumber);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `TeamMemberProfile(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// TeamMemberProfile(...).copyWith(id: 12, name: "My name")
  /// ````
  TeamMemberProfile call({
    Object? roleId = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? profileUrl = const $CopyWithPlaceholder(),
    Object? roleLevel = const $CopyWithPlaceholder(),
    Object? roleAlias = const $CopyWithPlaceholder(),
    Object? adminUser = const $CopyWithPlaceholder(),
    Object? phoneNumber = const $CopyWithPlaceholder(),
  }) {
    return TeamMemberProfile(
      roleId: roleId == const $CopyWithPlaceholder() || roleId == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roleId!
          // ignore: cast_nullable_to_non_nullable
          : roleId as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      profileUrl: profileUrl == const $CopyWithPlaceholder()
          ? _value.profileUrl
          // ignore: cast_nullable_to_non_nullable
          : profileUrl as String?,
      roleLevel: roleLevel == const $CopyWithPlaceholder() || roleLevel == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roleLevel!
          // ignore: cast_nullable_to_non_nullable
          : roleLevel as int,
      roleAlias: roleAlias == const $CopyWithPlaceholder() || roleAlias == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roleAlias!
          // ignore: cast_nullable_to_non_nullable
          : roleAlias as String,
      adminUser: adminUser == const $CopyWithPlaceholder() || adminUser == null
          // ignore: unnecessary_non_null_assertion
          ? _value.adminUser!
          // ignore: cast_nullable_to_non_nullable
          : adminUser as bool,
      phoneNumber:
          phoneNumber == const $CopyWithPlaceholder() || phoneNumber == null
              // ignore: unnecessary_non_null_assertion
              ? _value.phoneNumber!
              // ignore: cast_nullable_to_non_nullable
              : phoneNumber as String,
    );
  }
}

extension $TeamMemberProfileCopyWith on TeamMemberProfile {
  /// Returns a callable class that can be used as follows: `instanceOfTeamMemberProfile.copyWith(...)` or like so:`instanceOfTeamMemberProfile.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$TeamMemberProfileCWProxy get copyWith =>
      _$TeamMemberProfileCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TeamMemberProfile _$TeamMemberProfileFromJson(Map json) => TeamMemberProfile(
      roleId: json['roleId'] as String,
      name: json['name'] as String,
      id: json['id'] as String,
      profileUrl: json['profileUrl'] as String?,
      roleLevel: json['roleLevel'] as int,
      roleAlias: json['roleAlias'] as String? ?? '',
      adminUser: json['adminUser'] as bool,
      phoneNumber: json['phoneNumber'] as String,
    );

Map<String, dynamic> _$TeamMemberProfileToJson(TeamMemberProfile instance) =>
    <String, dynamic>{
      'roleId': instance.roleId,
      'name': instance.name,
      'id': instance.id,
      'roleLevel': instance.roleLevel,
      'roleAlias': instance.roleAlias,
      'phoneNumber': instance.phoneNumber,
      'adminUser': instance.adminUser,
      'profileUrl': instance.profileUrl,
    };

UserProfile _$UserProfileFromJson(Map json) => UserProfile(
      id: json['id'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      adminUser: json['adminUser'] as bool,
      profileUrl: json['profileUrl'] as String,
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phoneNumber': instance.phoneNumber,
      'adminUser': instance.adminUser,
      'profileUrl': instance.profileUrl,
    };

UserFetchResult _$UserFetchResultFromJson(Map json) => UserFetchResult(
      profiles: (json['profiles'] as List<dynamic>)
          .map((e) => UserProfile.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      pageEnd: json['pageEnd'] as bool,
      nextOffset: json['nextOffset'] as String?,
    );

Map<String, dynamic> _$UserFetchResultToJson(UserFetchResult instance) =>
    <String, dynamic>{
      'profiles': instance.profiles.map((e) => e.toJson()).toList(),
      'pageEnd': instance.pageEnd,
      'nextOffset': instance.nextOffset,
    };
