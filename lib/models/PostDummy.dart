import 'package:mla_connect/models/issues.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:intl/intl.dart';
part 'PostDummy.g.dart';

@CopyWith()
@JsonSerializable()
class PostDummy {
  final String? mediaurl;
  final String postType;
  final String? text;
  final String id;
  final int createTimestamp;
  final String? authorName;
  final String? authorProfileUrl;
  final String authorId;

  PostDummy(
      {this.mediaurl,
      required this.authorName,
      this.authorProfileUrl,
      required this.postType,
      required this.id,
      required this.createTimestamp,
      required this.authorId,
      this.text});

  String formatedCreateTime() {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat
          .format(DateTime.fromMillisecondsSinceEpoch(createTimestamp));
    } catch (e) {
      return "";
    }
  }

  factory PostDummy.fromJson(Map<String, dynamic> json) =>
      _$PostDummyFromJson(json);
  Map<String, dynamic> toJson() => _$PostDummyToJson(this);
}
