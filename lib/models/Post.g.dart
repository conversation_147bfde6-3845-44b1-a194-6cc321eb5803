// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'Post.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$PostCWProxy {
  Post mediaurl(String? mediaurl);

  Post authorName(String? authorName);

  Post authorProfileUrl(String? authorProfileUrl);

  Post postType(String postType);

  Post id(String id);

  Post createTimestamp(int createTimestamp);

  Post authorId(String authorId);

  Post like(List<String>? like);

  Post share(Map<String, List<String>>? share);

  Post text(String? text);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Post(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Post(...).copyWith(id: 12, name: "My name")
  /// ````
  Post call({
    String? mediaurl,
    String? authorName,
    String? authorProfileUrl,
    String? postType,
    String? id,
    int? createTimestamp,
    String? authorId,
    List<String>? like,
    Map<String, List<String>>? share,
    String? text,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfPost.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfPost.copyWith.fieldName(...)`
class _$PostCWProxyImpl implements _$PostCWProxy {
  const _$PostCWProxyImpl(this._value);

  final Post _value;

  @override
  Post mediaurl(String? mediaurl) => this(mediaurl: mediaurl);

  @override
  Post authorName(String? authorName) => this(authorName: authorName);

  @override
  Post authorProfileUrl(String? authorProfileUrl) =>
      this(authorProfileUrl: authorProfileUrl);

  @override
  Post postType(String postType) => this(postType: postType);

  @override
  Post id(String id) => this(id: id);

  @override
  Post createTimestamp(int createTimestamp) =>
      this(createTimestamp: createTimestamp);

  @override
  Post authorId(String authorId) => this(authorId: authorId);

  @override
  Post like(List<String>? like) => this(like: like);

  @override
  Post share(Map<String, List<String>>? share) => this(share: share);

  @override
  Post text(String? text) => this(text: text);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Post(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Post(...).copyWith(id: 12, name: "My name")
  /// ````
  Post call({
    Object? mediaurl = const $CopyWithPlaceholder(),
    Object? authorName = const $CopyWithPlaceholder(),
    Object? authorProfileUrl = const $CopyWithPlaceholder(),
    Object? postType = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? createTimestamp = const $CopyWithPlaceholder(),
    Object? authorId = const $CopyWithPlaceholder(),
    Object? like = const $CopyWithPlaceholder(),
    Object? share = const $CopyWithPlaceholder(),
    Object? text = const $CopyWithPlaceholder(),
  }) {
    return Post(
      mediaurl: mediaurl == const $CopyWithPlaceholder()
          ? _value.mediaurl
          // ignore: cast_nullable_to_non_nullable
          : mediaurl as String?,
      authorName: authorName == const $CopyWithPlaceholder()
          ? _value.authorName
          // ignore: cast_nullable_to_non_nullable
          : authorName as String?,
      authorProfileUrl: authorProfileUrl == const $CopyWithPlaceholder()
          ? _value.authorProfileUrl
          // ignore: cast_nullable_to_non_nullable
          : authorProfileUrl as String?,
      postType: postType == const $CopyWithPlaceholder() || postType == null
          // ignore: unnecessary_non_null_assertion
          ? _value.postType!
          // ignore: cast_nullable_to_non_nullable
          : postType as String,
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      createTimestamp: createTimestamp == const $CopyWithPlaceholder() ||
              createTimestamp == null
          // ignore: unnecessary_non_null_assertion
          ? _value.createTimestamp!
          // ignore: cast_nullable_to_non_nullable
          : createTimestamp as int,
      authorId: authorId == const $CopyWithPlaceholder() || authorId == null
          // ignore: unnecessary_non_null_assertion
          ? _value.authorId!
          // ignore: cast_nullable_to_non_nullable
          : authorId as String,
      like: like == const $CopyWithPlaceholder()
          ? _value.like
          // ignore: cast_nullable_to_non_nullable
          : like as List<String>?,
      share: share == const $CopyWithPlaceholder()
          ? _value.share
          // ignore: cast_nullable_to_non_nullable
          : share as Map<String, List<String>>?,
      text: text == const $CopyWithPlaceholder()
          ? _value.text
          // ignore: cast_nullable_to_non_nullable
          : text as String?,
    );
  }
}

extension $PostCopyWith on Post {
  /// Returns a callable class that can be used as follows: `instanceOfPost.copyWith(...)` or like so:`instanceOfPost.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$PostCWProxy get copyWith => _$PostCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Post _$PostFromJson(Map json) => Post(
      mediaurl: json['mediaurl'] as String?,
      authorName: json['authorName'] as String?,
      authorProfileUrl: json['authorProfileUrl'] as String?,
      postType: json['postType'] as String,
      id: json['id'] as String,
      createTimestamp: json['createTimestamp'] as int,
      authorId: json['authorId'] as String,
      like: (json['like'] as List<dynamic>?)?.map((e) => e as String).toList(),
      share: (json['share'] as Map?)?.map(
        (k, e) => MapEntry(
            k as String, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
      text: json['text'] as String?,
    );

Map<String, dynamic> _$PostToJson(Post instance) => <String, dynamic>{
      'mediaurl': instance.mediaurl,
      'postType': instance.postType,
      'text': instance.text,
      'id': instance.id,
      'createTimestamp': instance.createTimestamp,
      'authorName': instance.authorName,
      'authorProfileUrl': instance.authorProfileUrl,
      'authorId': instance.authorId,
      'like': instance.like,
      'share': instance.share,
    };

CreatePostResult _$CreatePostResultFromJson(Map json) => CreatePostResult(
      post: Post.fromJson(Map<String, dynamic>.from(json['post'] as Map)),
    );

Map<String, dynamic> _$CreatePostResultToJson(CreatePostResult instance) =>
    <String, dynamic>{
      'post': instance.post.toJson(),
    };
