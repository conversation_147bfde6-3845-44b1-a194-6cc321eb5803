import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'TeamMemberProfile.g.dart';

@CopyWith()
@JsonSerializable()
class TeamMemberProfile extends Equatable {
  final String roleId;
  final String name;
  final String id;
  final int roleLevel;
  @JsonKey(defaultValue: "")
  final String roleAlias;
  final String phoneNumber;
  final bool adminUser;
  final String? profileUrl;
  TeamMemberProfile(
      {required this.roleId,
      required this.name,
      required this.id,
      required this.profileUrl,
      required this.roleLevel,
      required this.roleAlias,
      required this.adminUser,
      required this.phoneNumber});

  factory TeamMemberProfile.fromJson(Map<String, dynamic> json) =>
      _$TeamMemberProfileFromJson(json);
  Map<String, dynamic> toJson() => _$TeamMemberProfileToJson(this);

  @override
  List<Object?> get props => [
        roleId,
        name,
        id,
        roleAlias,
        roleAlias,
        phoneNumber,
        adminUser,
        profileUrl
      ];
}

@JsonSerializable()
class UserProfile {
  final String id;
  final String name;
  final String phoneNumber;
  final bool adminUser;
  final String profileUrl;
  UserProfile({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.adminUser,
    required this.profileUrl,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

@JsonSerializable()
class UserFetchResult {
  final List<UserProfile> profiles;
  final bool pageEnd;
  final String? nextOffset;

  UserFetchResult({
    required this.profiles,
    required this.pageEnd,
    required this.nextOffset,
  });

  factory UserFetchResult.fromJson(Map<String, dynamic> json) =>
      _$UserFetchResultFromJson(json);
  Map<String, dynamic> toJson() => _$UserFetchResultToJson(this);
}
