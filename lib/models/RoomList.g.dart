// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'RoomList.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$RoomListCWProxy {
  RoomList name(String name);

  RoomList roomId(String roomId);

  RoomList lastMessage(ChatMessage lastMessage);

  RoomList lastMessageTimestamp(int lastMessageTimestamp);

  RoomList unseenCount(int? unseenCount);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `RoomList(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// RoomList(...).copyWith(id: 12, name: "My name")
  /// ````
  RoomList call({
    String? name,
    String? roomId,
    ChatMessage? lastMessage,
    int? lastMessageTimestamp,
    int? unseenCount,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfRoomList.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfRoomList.copyWith.fieldName(...)`
class _$RoomListCWProxyImpl implements _$RoomListCWProxy {
  const _$RoomListCWProxyImpl(this._value);

  final RoomList _value;

  @override
  RoomList name(String name) => this(name: name);

  @override
  RoomList roomId(String roomId) => this(roomId: roomId);

  @override
  RoomList lastMessage(ChatMessage lastMessage) =>
      this(lastMessage: lastMessage);

  @override
  RoomList lastMessageTimestamp(int lastMessageTimestamp) =>
      this(lastMessageTimestamp: lastMessageTimestamp);

  @override
  RoomList unseenCount(int? unseenCount) => this(unseenCount: unseenCount);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `RoomList(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// RoomList(...).copyWith(id: 12, name: "My name")
  /// ````
  RoomList call({
    Object? name = const $CopyWithPlaceholder(),
    Object? roomId = const $CopyWithPlaceholder(),
    Object? lastMessage = const $CopyWithPlaceholder(),
    Object? lastMessageTimestamp = const $CopyWithPlaceholder(),
    Object? unseenCount = const $CopyWithPlaceholder(),
  }) {
    return RoomList(
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      roomId: roomId == const $CopyWithPlaceholder() || roomId == null
          // ignore: unnecessary_non_null_assertion
          ? _value.roomId!
          // ignore: cast_nullable_to_non_nullable
          : roomId as String,
      lastMessage:
          lastMessage == const $CopyWithPlaceholder() || lastMessage == null
              // ignore: unnecessary_non_null_assertion
              ? _value.lastMessage!
              // ignore: cast_nullable_to_non_nullable
              : lastMessage as ChatMessage,
      lastMessageTimestamp:
          lastMessageTimestamp == const $CopyWithPlaceholder() ||
                  lastMessageTimestamp == null
              // ignore: unnecessary_non_null_assertion
              ? _value.lastMessageTimestamp!
              // ignore: cast_nullable_to_non_nullable
              : lastMessageTimestamp as int,
      unseenCount: unseenCount == const $CopyWithPlaceholder()
          ? _value.unseenCount
          // ignore: cast_nullable_to_non_nullable
          : unseenCount as int?,
    );
  }
}

extension $RoomListCopyWith on RoomList {
  /// Returns a callable class that can be used as follows: `instanceOfRoomList.copyWith(...)` or like so:`instanceOfRoomList.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$RoomListCWProxy get copyWith => _$RoomListCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoomList _$RoomListFromJson(Map json) => RoomList(
      name: json['name'] as String,
      roomId: json['roomId'] as String,
      lastMessage: ChatMessage.fromJson(
          Map<String, dynamic>.from(json['lastMessage'] as Map)),
      lastMessageTimestamp: json['lastMessageTimestamp'] as int,
      unseenCount: json['unseenCount'] as int?,
    );

Map<String, dynamic> _$RoomListToJson(RoomList instance) => <String, dynamic>{
      'roomId': instance.roomId,
      'name': instance.name,
      'lastMessageTimestamp': instance.lastMessageTimestamp,
      'lastMessage': instance.lastMessage.toJson(),
      'unseenCount': instance.unseenCount,
    };
