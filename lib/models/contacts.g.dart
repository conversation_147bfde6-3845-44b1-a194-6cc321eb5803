// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contacts.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$PhoneContactCWProxy {
  PhoneContact id(String id);

  PhoneContact name(String name);

  PhoneContact phones(List<String> phones);

  PhoneContact synced(bool synced);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `PhoneContact(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// PhoneContact(...).copyWith(id: 12, name: "My name")
  /// ````
  PhoneContact call({
    String? id,
    String? name,
    List<String>? phones,
    bool? synced,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfPhoneContact.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfPhoneContact.copyWith.fieldName(...)`
class _$PhoneContactCWProxyImpl implements _$PhoneContactCWProxy {
  const _$PhoneContactCWProxyImpl(this._value);

  final PhoneContact _value;

  @override
  PhoneContact id(String id) => this(id: id);

  @override
  PhoneContact name(String name) => this(name: name);

  @override
  PhoneContact phones(List<String> phones) => this(phones: phones);

  @override
  PhoneContact synced(bool synced) => this(synced: synced);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `PhoneContact(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// PhoneContact(...).copyWith(id: 12, name: "My name")
  /// ````
  PhoneContact call({
    Object? id = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? phones = const $CopyWithPlaceholder(),
    Object? synced = const $CopyWithPlaceholder(),
  }) {
    return PhoneContact(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      phones: phones == const $CopyWithPlaceholder() || phones == null
          // ignore: unnecessary_non_null_assertion
          ? _value.phones!
          // ignore: cast_nullable_to_non_nullable
          : phones as List<String>,
      synced: synced == const $CopyWithPlaceholder() || synced == null
          // ignore: unnecessary_non_null_assertion
          ? _value.synced!
          // ignore: cast_nullable_to_non_nullable
          : synced as bool,
    );
  }
}

extension $PhoneContactCopyWith on PhoneContact {
  /// Returns a callable class that can be used as follows: `instanceOfPhoneContact.copyWith(...)` or like so:`instanceOfPhoneContact.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$PhoneContactCWProxy get copyWith => _$PhoneContactCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PhoneContact _$PhoneContactFromJson(Map json) => PhoneContact(
      id: json['id'] as String,
      name: json['name'] as String,
      phones:
          (json['phones'] as List<dynamic>).map((e) => e as String).toList(),
      synced: json['synced'] as bool,
    );

Map<String, dynamic> _$PhoneContactToJson(PhoneContact instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phones': instance.phones,
      'synced': instance.synced,
    };

DeviceInfo _$DeviceInfoFromJson(Map json) => DeviceInfo(
      brand: json['brand'] as String?,
      device: json['device'] as String?,
      id: json['id'] as String?,
      manufacturer: json['manufacturer'] as String?,
      model: json['model'] as String?,
      product: json['product'] as String?,
      androidId: json['androidId'] as String?,
      // finger //print: json['finger //print'] as String?,
    );

Map<String, dynamic> _$DeviceInfoToJson(DeviceInfo instance) =>
    <String, dynamic>{
      'brand': instance.brand,
      'device': instance.device,
      'id': instance.id,
      'manufacturer': instance.manufacturer,
      'model': instance.model,
      'product': instance.product,
      'androidId': instance.androidId,
      'finger //print': instance.finger ,
    };

PhoneSyncRequest _$PhoneSyncRequestFromJson(Map json) => PhoneSyncRequest(
      contacts: (json['contacts'] as List<dynamic>)
          .map(
              (e) => PhoneContact.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      deviceInfo: DeviceInfo.fromJson(
          Map<String, dynamic>.from(json['deviceInfo'] as Map)),
    );

Map<String, dynamic> _$PhoneSyncRequestToJson(PhoneSyncRequest instance) =>
    <String, dynamic>{
      'deviceInfo': instance.deviceInfo.toJson(),
      'contacts': instance.contacts.map((e) => e.toJson()).toList(),
    };

PhoneSyncResult _$PhoneSyncResultFromJson(Map json) => PhoneSyncResult(
      synced:
          (json['synced'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$PhoneSyncResultToJson(PhoneSyncResult instance) =>
    <String, dynamic>{
      'synced': instance.synced,
    };
