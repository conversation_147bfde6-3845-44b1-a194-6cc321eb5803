import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ChatMessage.g.dart';

@CopyWith()
@JsonSerializable()
class ChatMessage {
  final String senderId;
  final String recipientId;
  final String recipientName;
  final String messageId;
  final String? message;
  final String messageType;
  final int timeStamp;
  final String? profileImageUrl;
  final String senderName;
  final String? mediaUrl;
  final ChatMessagePoll? pollData;

  ChatMessage(
      {this.mediaUrl,
      this.message,
      required this.messageId,
      required this.messageType,
      this.profileImageUrl,
      required this.recipientId,
      required this.recipientName,
      required this.senderId,
      required this.senderName,
      this.pollData,
      required this.timeStamp});

  String formatedCreateTime() {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat.format(DateTime.fromMillisecondsSinceEpoch(timeStamp));
    } catch (e) {
      return "";
    }
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);
}

@CopyWith()
@JsonSerializable()
class ChatMessageUnread {
  final ChatMessage msg;
  @JsonKey(defaultValue: 0)
  final int unreadMsg;
  ChatMessageUnread({required this.msg, required this.unreadMsg});
  factory ChatMessageUnread.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageUnreadFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageUnreadToJson(this);
}

@CopyWith()
@JsonSerializable()
class ChatMessagePoll {
  final String question;
  final Map<String, List<String?>> options;

  ChatMessagePoll({required this.options, required this.question});
  factory ChatMessagePoll.fromJson(Map<String, dynamic> json) =>
      _$ChatMessagePollFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessagePollToJson(this);
}
