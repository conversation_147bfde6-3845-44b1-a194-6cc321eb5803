// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'PosterV2.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterNetworkModelV2 _$PosterNetworkModelV2FromJson(Map json) =>
    PosterNetworkModelV2(
      id: json['id'] as String,
      posterUrl: json['posterUrl'] as String,
      createTimeMs: json['createTimeMs'] as int?,
      caption: json['caption'] as String,
    );

Map<String, dynamic> _$PosterNetworkModelV2ToJson(
        PosterNetworkModelV2 instance) =>
    <String, dynamic>{
      'id': instance.id,
      'posterUrl': instance.posterUrl,
      'createTimeMs': instance.createTimeMs,
      'caption': instance.caption,
    };

PosterCreateResultV2 _$PosterCreateResultV2FromJson(Map json) =>
    PosterCreateResultV2(
      poster: PosterNetworkModelV2.fromJson(
          Map<String, dynamic>.from(json['poster'] as Map)),
    );

Map<String, dynamic> _$PosterCreateResultV2ToJson(
        PosterCreateResultV2 instance) =>
    <String, dynamic>{
      'poster': instance.poster.toJson(),
    };

PosterApiRequestV2 _$PosterApiRequestV2FromJson(Map json) => PosterApiRequestV2(
      action: json['action'] as String,
      poster: json['poster'] == null
          ? null
          : PosterNetworkModelV2.fromJson(
              Map<String, dynamic>.from(json['poster'] as Map)),
      id: json['id'] as String?,
      startOffset: json['startOffset'],
    );

Map<String, dynamic> _$PosterApiRequestV2ToJson(PosterApiRequestV2 instance) =>
    <String, dynamic>{
      'action': instance.action,
      'poster': instance.poster?.toJson(),
      'id': instance.id,
      'startOffset': instance.startOffset,
    };

PosterFetchResultV2 _$PosterFetchResultV2FromJson(Map json) =>
    PosterFetchResultV2(
      posters: (json['posters'] as List<dynamic>)
          .map((e) => PosterNetworkModelV2.fromJson(
              Map<String, dynamic>.from(e as Map)))
          .toList(),
      nextOffset: json['nextOffset'],
      pageEnd: json['pageEnd'] as bool,
    );

Map<String, dynamic> _$PosterFetchResultV2ToJson(
        PosterFetchResultV2 instance) =>
    <String, dynamic>{
      'posters': instance.posters.map((e) => e.toJson()).toList(),
      'nextOffset': instance.nextOffset,
      'pageEnd': instance.pageEnd,
    };
