// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'Scheme.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Scheme _$SchemeFromJson(Map json) => Scheme(
      name: json['name'] as String,
      gender: json['gender'] as String,
      age: json['age'] as int,
      residence: json['residence'] as String,
      caste: json['caste'] as String,
      state: json['state'] as String,
      differentlyAbled: json['differentlyAbled'] as bool,
      minority: json['minority'] as bool,
      student: json['student'] as bool,
      employed: json['employed'] as bool,
      occupation: json['occupation'] as String,
      bpl: json['bpl'] as bool,
      annualIncome: (json['annualIncome'] as num).toDouble(),
      category: json['category'] as String,
    );

Map<String, dynamic> _$SchemeToJson(Scheme instance) => <String, dynamic>{
      'name': instance.name,
      'gender': instance.gender,
      'age': instance.age,
      'residence': instance.residence,
      'caste': instance.caste,
      'state': instance.state,
      'differentlyAbled': instance.differentlyAbled,
      'minority': instance.minority,
      'student': instance.student,
      'employed': instance.employed,
      'occupation': instance.occupation,
      'bpl': instance.bpl,
      'annualIncome': instance.annualIncome,
      'category': instance.category,
    };
