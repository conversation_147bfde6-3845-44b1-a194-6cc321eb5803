// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'LeaderboardResult.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$LeaderboardModelCWProxy {
  LeaderboardModel phoneNumber(String phoneNumber);

  LeaderboardModel points(int points);

  LeaderboardModel profileUrl(String? profileUrl);

  LeaderboardModel name(String name);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `LeaderboardModel(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// LeaderboardModel(...).copyWith(id: 12, name: "My name")
  /// ````
  LeaderboardModel call({
    String? phoneNumber,
    int? points,
    String? profileUrl,
    String? name,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfLeaderboardModel.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfLeaderboardModel.copyWith.fieldName(...)`
class _$LeaderboardModelCWProxyImpl implements _$LeaderboardModelCWProxy {
  const _$LeaderboardModelCWProxyImpl(this._value);

  final LeaderboardModel _value;

  @override
  LeaderboardModel phoneNumber(String phoneNumber) =>
      this(phoneNumber: phoneNumber);

  @override
  LeaderboardModel points(int points) => this(points: points);

  @override
  LeaderboardModel profileUrl(String? profileUrl) =>
      this(profileUrl: profileUrl);

  @override
  LeaderboardModel name(String name) => this(name: name);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `LeaderboardModel(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// LeaderboardModel(...).copyWith(id: 12, name: "My name")
  /// ````
  LeaderboardModel call({
    Object? phoneNumber = const $CopyWithPlaceholder(),
    Object? points = const $CopyWithPlaceholder(),
    Object? profileUrl = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
  }) {
    return LeaderboardModel(
      phoneNumber:
          phoneNumber == const $CopyWithPlaceholder() || phoneNumber == null
              // ignore: unnecessary_non_null_assertion
              ? _value.phoneNumber!
              // ignore: cast_nullable_to_non_nullable
              : phoneNumber as String,
      points: points == const $CopyWithPlaceholder() || points == null
          // ignore: unnecessary_non_null_assertion
          ? _value.points!
          // ignore: cast_nullable_to_non_nullable
          : points as int,
      profileUrl: profileUrl == const $CopyWithPlaceholder()
          ? _value.profileUrl
          // ignore: cast_nullable_to_non_nullable
          : profileUrl as String?,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
    );
  }
}

extension $LeaderboardModelCopyWith on LeaderboardModel {
  /// Returns a callable class that can be used as follows: `instanceOfLeaderboardModel.copyWith(...)` or like so:`instanceOfLeaderboardModel.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$LeaderboardModelCWProxy get copyWith => _$LeaderboardModelCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeaderboardModel _$LeaderboardModelFromJson(Map json) => LeaderboardModel(
      phoneNumber: json['phoneNumber'] as String,
      points: json['points'] as int,
      profileUrl: json['profileUrl'] as String?,
      name: json['name'] as String,
    );

Map<String, dynamic> _$LeaderboardModelToJson(LeaderboardModel instance) =>
    <String, dynamic>{
      'phoneNumber': instance.phoneNumber,
      'points': instance.points,
      'profileUrl': instance.profileUrl,
      'name': instance.name,
    };

LeaderboardResult _$LeaderboardResultFromJson(Map json) => LeaderboardResult(
      leaderBoard: (json['leaderBoard'] as List<dynamic>?)
          ?.map((e) =>
              LeaderboardModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$LeaderboardResultToJson(LeaderboardResult instance) =>
    <String, dynamic>{
      'leaderBoard': instance.leaderBoard?.map((e) => e.toJson()).toList(),
    };
