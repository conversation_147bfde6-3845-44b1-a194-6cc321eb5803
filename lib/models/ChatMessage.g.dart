// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ChatMessage.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$ChatMessageCWProxy {
  ChatMessage mediaUrl(String? mediaUrl);

  ChatMessage message(String? message);

  ChatMessage messageId(String messageId);

  ChatMessage messageType(String messageType);

  ChatMessage profileImageUrl(String? profileImageUrl);

  ChatMessage recipientId(String recipientId);

  ChatMessage recipientName(String recipientName);

  ChatMessage senderId(String senderId);

  ChatMessage senderName(String senderName);

  ChatMessage pollData(ChatMessagePoll? pollData);

  ChatMessage timeStamp(int timeStamp);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ChatMessage(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ChatMessage(...).copyWith(id: 12, name: "My name")
  /// ````
  ChatMessage call({
    String? mediaUrl,
    String? message,
    String? messageId,
    String? messageType,
    String? profileImageUrl,
    String? recipientId,
    String? recipientName,
    String? senderId,
    String? senderName,
    ChatMessagePoll? pollData,
    int? timeStamp,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfChatMessage.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfChatMessage.copyWith.fieldName(...)`
class _$ChatMessageCWProxyImpl implements _$ChatMessageCWProxy {
  const _$ChatMessageCWProxyImpl(this._value);

  final ChatMessage _value;

  @override
  ChatMessage mediaUrl(String? mediaUrl) => this(mediaUrl: mediaUrl);

  @override
  ChatMessage message(String? message) => this(message: message);

  @override
  ChatMessage messageId(String messageId) => this(messageId: messageId);

  @override
  ChatMessage messageType(String messageType) => this(messageType: messageType);

  @override
  ChatMessage profileImageUrl(String? profileImageUrl) =>
      this(profileImageUrl: profileImageUrl);

  @override
  ChatMessage recipientId(String recipientId) => this(recipientId: recipientId);

  @override
  ChatMessage recipientName(String recipientName) =>
      this(recipientName: recipientName);

  @override
  ChatMessage senderId(String senderId) => this(senderId: senderId);

  @override
  ChatMessage senderName(String senderName) => this(senderName: senderName);

  @override
  ChatMessage pollData(ChatMessagePoll? pollData) => this(pollData: pollData);

  @override
  ChatMessage timeStamp(int timeStamp) => this(timeStamp: timeStamp);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ChatMessage(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ChatMessage(...).copyWith(id: 12, name: "My name")
  /// ````
  ChatMessage call({
    Object? mediaUrl = const $CopyWithPlaceholder(),
    Object? message = const $CopyWithPlaceholder(),
    Object? messageId = const $CopyWithPlaceholder(),
    Object? messageType = const $CopyWithPlaceholder(),
    Object? profileImageUrl = const $CopyWithPlaceholder(),
    Object? recipientId = const $CopyWithPlaceholder(),
    Object? recipientName = const $CopyWithPlaceholder(),
    Object? senderId = const $CopyWithPlaceholder(),
    Object? senderName = const $CopyWithPlaceholder(),
    Object? pollData = const $CopyWithPlaceholder(),
    Object? timeStamp = const $CopyWithPlaceholder(),
  }) {
    return ChatMessage(
      mediaUrl: mediaUrl == const $CopyWithPlaceholder()
          ? _value.mediaUrl
          // ignore: cast_nullable_to_non_nullable
          : mediaUrl as String?,
      message: message == const $CopyWithPlaceholder()
          ? _value.message
          // ignore: cast_nullable_to_non_nullable
          : message as String?,
      messageId: messageId == const $CopyWithPlaceholder() || messageId == null
          // ignore: unnecessary_non_null_assertion
          ? _value.messageId!
          // ignore: cast_nullable_to_non_nullable
          : messageId as String,
      messageType:
          messageType == const $CopyWithPlaceholder() || messageType == null
              // ignore: unnecessary_non_null_assertion
              ? _value.messageType!
              // ignore: cast_nullable_to_non_nullable
              : messageType as String,
      profileImageUrl: profileImageUrl == const $CopyWithPlaceholder()
          ? _value.profileImageUrl
          // ignore: cast_nullable_to_non_nullable
          : profileImageUrl as String?,
      recipientId:
          recipientId == const $CopyWithPlaceholder() || recipientId == null
              // ignore: unnecessary_non_null_assertion
              ? _value.recipientId!
              // ignore: cast_nullable_to_non_nullable
              : recipientId as String,
      recipientName:
          recipientName == const $CopyWithPlaceholder() || recipientName == null
              // ignore: unnecessary_non_null_assertion
              ? _value.recipientName!
              // ignore: cast_nullable_to_non_nullable
              : recipientName as String,
      senderId: senderId == const $CopyWithPlaceholder() || senderId == null
          // ignore: unnecessary_non_null_assertion
          ? _value.senderId!
          // ignore: cast_nullable_to_non_nullable
          : senderId as String,
      senderName:
          senderName == const $CopyWithPlaceholder() || senderName == null
              // ignore: unnecessary_non_null_assertion
              ? _value.senderName!
              // ignore: cast_nullable_to_non_nullable
              : senderName as String,
      pollData: pollData == const $CopyWithPlaceholder()
          ? _value.pollData
          // ignore: cast_nullable_to_non_nullable
          : pollData as ChatMessagePoll?,
      timeStamp: timeStamp == const $CopyWithPlaceholder() || timeStamp == null
          // ignore: unnecessary_non_null_assertion
          ? _value.timeStamp!
          // ignore: cast_nullable_to_non_nullable
          : timeStamp as int,
    );
  }
}

extension $ChatMessageCopyWith on ChatMessage {
  /// Returns a callable class that can be used as follows: `instanceOfChatMessage.copyWith(...)` or like so:`instanceOfChatMessage.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$ChatMessageCWProxy get copyWith => _$ChatMessageCWProxyImpl(this);
}

abstract class _$ChatMessageUnreadCWProxy {
  ChatMessageUnread msg(ChatMessage msg);

  ChatMessageUnread unreadMsg(int unreadMsg);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ChatMessageUnread(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ChatMessageUnread(...).copyWith(id: 12, name: "My name")
  /// ````
  ChatMessageUnread call({
    ChatMessage? msg,
    int? unreadMsg,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfChatMessageUnread.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfChatMessageUnread.copyWith.fieldName(...)`
class _$ChatMessageUnreadCWProxyImpl implements _$ChatMessageUnreadCWProxy {
  const _$ChatMessageUnreadCWProxyImpl(this._value);

  final ChatMessageUnread _value;

  @override
  ChatMessageUnread msg(ChatMessage msg) => this(msg: msg);

  @override
  ChatMessageUnread unreadMsg(int unreadMsg) => this(unreadMsg: unreadMsg);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ChatMessageUnread(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ChatMessageUnread(...).copyWith(id: 12, name: "My name")
  /// ````
  ChatMessageUnread call({
    Object? msg = const $CopyWithPlaceholder(),
    Object? unreadMsg = const $CopyWithPlaceholder(),
  }) {
    return ChatMessageUnread(
      msg: msg == const $CopyWithPlaceholder() || msg == null
          // ignore: unnecessary_non_null_assertion
          ? _value.msg!
          // ignore: cast_nullable_to_non_nullable
          : msg as ChatMessage,
      unreadMsg: unreadMsg == const $CopyWithPlaceholder() || unreadMsg == null
          // ignore: unnecessary_non_null_assertion
          ? _value.unreadMsg!
          // ignore: cast_nullable_to_non_nullable
          : unreadMsg as int,
    );
  }
}

extension $ChatMessageUnreadCopyWith on ChatMessageUnread {
  /// Returns a callable class that can be used as follows: `instanceOfChatMessageUnread.copyWith(...)` or like so:`instanceOfChatMessageUnread.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$ChatMessageUnreadCWProxy get copyWith =>
      _$ChatMessageUnreadCWProxyImpl(this);
}

abstract class _$ChatMessagePollCWProxy {
  ChatMessagePoll options(Map<String, List<String?>> options);

  ChatMessagePoll question(String question);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ChatMessagePoll(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ChatMessagePoll(...).copyWith(id: 12, name: "My name")
  /// ````
  ChatMessagePoll call({
    Map<String, List<String?>>? options,
    String? question,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfChatMessagePoll.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfChatMessagePoll.copyWith.fieldName(...)`
class _$ChatMessagePollCWProxyImpl implements _$ChatMessagePollCWProxy {
  const _$ChatMessagePollCWProxyImpl(this._value);

  final ChatMessagePoll _value;

  @override
  ChatMessagePoll options(Map<String, List<String?>> options) =>
      this(options: options);

  @override
  ChatMessagePoll question(String question) => this(question: question);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ChatMessagePoll(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ChatMessagePoll(...).copyWith(id: 12, name: "My name")
  /// ````
  ChatMessagePoll call({
    Object? options = const $CopyWithPlaceholder(),
    Object? question = const $CopyWithPlaceholder(),
  }) {
    return ChatMessagePoll(
      options: options == const $CopyWithPlaceholder() || options == null
          // ignore: unnecessary_non_null_assertion
          ? _value.options!
          // ignore: cast_nullable_to_non_nullable
          : options as Map<String, List<String?>>,
      question: question == const $CopyWithPlaceholder() || question == null
          // ignore: unnecessary_non_null_assertion
          ? _value.question!
          // ignore: cast_nullable_to_non_nullable
          : question as String,
    );
  }
}

extension $ChatMessagePollCopyWith on ChatMessagePoll {
  /// Returns a callable class that can be used as follows: `instanceOfChatMessagePoll.copyWith(...)` or like so:`instanceOfChatMessagePoll.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$ChatMessagePollCWProxy get copyWith => _$ChatMessagePollCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatMessage _$ChatMessageFromJson(Map json) => ChatMessage(
      mediaUrl: json['mediaUrl'] as String?,
      message: json['message'] as String?,
      messageId: json['messageId'] as String,
      messageType: json['messageType'] as String,
      profileImageUrl: json['profileImageUrl'] as String?,
      recipientId: json['recipientId'] as String,
      recipientName: json['recipientName'] as String,
      senderId: json['senderId'] as String,
      senderName: json['senderName'] as String,
      pollData: json['pollData'] == null
          ? null
          : ChatMessagePoll.fromJson(
              Map<String, dynamic>.from(json['pollData'] as Map)),
      timeStamp: json['timeStamp'] as int,
    );

Map<String, dynamic> _$ChatMessageToJson(ChatMessage instance) =>
    <String, dynamic>{
      'senderId': instance.senderId,
      'recipientId': instance.recipientId,
      'recipientName': instance.recipientName,
      'messageId': instance.messageId,
      'message': instance.message,
      'messageType': instance.messageType,
      'timeStamp': instance.timeStamp,
      'profileImageUrl': instance.profileImageUrl,
      'senderName': instance.senderName,
      'mediaUrl': instance.mediaUrl,
      'pollData': instance.pollData?.toJson(),
    };

ChatMessageUnread _$ChatMessageUnreadFromJson(Map json) => ChatMessageUnread(
      msg: ChatMessage.fromJson(Map<String, dynamic>.from(json['msg'] as Map)),
      unreadMsg: json['unreadMsg'] as int? ?? 0,
    );

Map<String, dynamic> _$ChatMessageUnreadToJson(ChatMessageUnread instance) =>
    <String, dynamic>{
      'msg': instance.msg.toJson(),
      'unreadMsg': instance.unreadMsg,
    };

ChatMessagePoll _$ChatMessagePollFromJson(Map json) => ChatMessagePoll(
      options: (json['options'] as Map).map(
        (k, e) => MapEntry(k as String,
            (e as List<dynamic>).map((e) => e as String?).toList()),
      ),
      question: json['question'] as String,
    );

Map<String, dynamic> _$ChatMessagePollToJson(ChatMessagePoll instance) =>
    <String, dynamic>{
      'question': instance.question,
      'options': instance.options,
    };
