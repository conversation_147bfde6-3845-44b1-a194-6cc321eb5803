import 'package:mla_connect/models/TeamMemberProfile.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'TeamMemberSearch.g.dart';

@CopyWith()
@JsonSerializable()
class TeamMemberSearch {
  final List<TeamMemberProfile> profiles;
  final bool pageEnd;
  final String? nextOffset;

  TeamMemberSearch({
    required this.profiles,
    required this.pageEnd,
    required this.nextOffset,
  });

  factory TeamMemberSearch.fromJson(Map<String, dynamic> json) =>
      _$TeamMemberSearchFromJson(json);
  Map<String, dynamic> toJson() => _$TeamMemberSearchToJson(this);
}
