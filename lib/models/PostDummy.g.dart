// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'PostDummy.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$PostDummyCWProxy {
  PostDummy mediaurl(String? mediaurl);

  PostDummy authorName(String? authorName);

  PostDummy authorProfileUrl(String? authorProfileUrl);

  PostDummy postType(String postType);

  PostDummy id(String id);

  PostDummy createTimestamp(int createTimestamp);

  PostDummy authorId(String authorId);

  PostDummy text(String? text);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `PostDummy(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// PostDummy(...).copyWith(id: 12, name: "My name")
  /// ````
  PostDummy call({
    String? mediaurl,
    String? authorName,
    String? authorProfileUrl,
    String? postType,
    String? id,
    int? createTimestamp,
    String? authorId,
    String? text,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfPostDummy.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfPostDummy.copyWith.fieldName(...)`
class _$PostDummyCWProxyImpl implements _$PostDummyCWProxy {
  const _$PostDummyCWProxyImpl(this._value);

  final PostDummy _value;

  @override
  PostDummy mediaurl(String? mediaurl) => this(mediaurl: mediaurl);

  @override
  PostDummy authorName(String? authorName) => this(authorName: authorName);

  @override
  PostDummy authorProfileUrl(String? authorProfileUrl) =>
      this(authorProfileUrl: authorProfileUrl);

  @override
  PostDummy postType(String postType) => this(postType: postType);

  @override
  PostDummy id(String id) => this(id: id);

  @override
  PostDummy createTimestamp(int createTimestamp) =>
      this(createTimestamp: createTimestamp);

  @override
  PostDummy authorId(String authorId) => this(authorId: authorId);

  @override
  PostDummy text(String? text) => this(text: text);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `PostDummy(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// PostDummy(...).copyWith(id: 12, name: "My name")
  /// ````
  PostDummy call({
    Object? mediaurl = const $CopyWithPlaceholder(),
    Object? authorName = const $CopyWithPlaceholder(),
    Object? authorProfileUrl = const $CopyWithPlaceholder(),
    Object? postType = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? createTimestamp = const $CopyWithPlaceholder(),
    Object? authorId = const $CopyWithPlaceholder(),
    Object? text = const $CopyWithPlaceholder(),
  }) {
    return PostDummy(
      mediaurl: mediaurl == const $CopyWithPlaceholder()
          ? _value.mediaurl
          // ignore: cast_nullable_to_non_nullable
          : mediaurl as String?,
      authorName: authorName == const $CopyWithPlaceholder()
          ? _value.authorName
          // ignore: cast_nullable_to_non_nullable
          : authorName as String?,
      authorProfileUrl: authorProfileUrl == const $CopyWithPlaceholder()
          ? _value.authorProfileUrl
          // ignore: cast_nullable_to_non_nullable
          : authorProfileUrl as String?,
      postType: postType == const $CopyWithPlaceholder() || postType == null
          // ignore: unnecessary_non_null_assertion
          ? _value.postType!
          // ignore: cast_nullable_to_non_nullable
          : postType as String,
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      createTimestamp: createTimestamp == const $CopyWithPlaceholder() ||
              createTimestamp == null
          // ignore: unnecessary_non_null_assertion
          ? _value.createTimestamp!
          // ignore: cast_nullable_to_non_nullable
          : createTimestamp as int,
      authorId: authorId == const $CopyWithPlaceholder() || authorId == null
          // ignore: unnecessary_non_null_assertion
          ? _value.authorId!
          // ignore: cast_nullable_to_non_nullable
          : authorId as String,
      text: text == const $CopyWithPlaceholder()
          ? _value.text
          // ignore: cast_nullable_to_non_nullable
          : text as String?,
    );
  }
}

extension $PostDummyCopyWith on PostDummy {
  /// Returns a callable class that can be used as follows: `instanceOfPostDummy.copyWith(...)` or like so:`instanceOfPostDummy.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$PostDummyCWProxy get copyWith => _$PostDummyCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostDummy _$PostDummyFromJson(Map json) => PostDummy(
      mediaurl: json['mediaurl'] as String?,
      authorName: json['authorName'] as String?,
      authorProfileUrl: json['authorProfileUrl'] as String?,
      postType: json['postType'] as String,
      id: json['id'] as String,
      createTimestamp: json['createTimestamp'] as int,
      authorId: json['authorId'] as String,
      text: json['text'] as String?,
    );

Map<String, dynamic> _$PostDummyToJson(PostDummy instance) => <String, dynamic>{
      'mediaurl': instance.mediaurl,
      'postType': instance.postType,
      'text': instance.text,
      'id': instance.id,
      'createTimestamp': instance.createTimestamp,
      'authorName': instance.authorName,
      'authorProfileUrl': instance.authorProfileUrl,
      'authorId': instance.authorId,
    };
