import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'reports.g.dart';

@CopyWith()
@JsonSerializable()
class AreaReport {
  final String id;
  final String pdf_url;
  final String name;

  AreaReport({required this.id, required this.pdf_url, required this.name});

  factory AreaReport.fromJson(Map<String, dynamic> json) =>
      _$AreaReportFromJson(json);
  Map<String, dynamic> toJson() => _$AreaReportToJson(this);
}

@CopyWith()
@JsonSerializable()
class Panchayat {
  final String id;
  final String name;
  final String village;

  Panchayat({required this.id, required this.name, required this.village});

  factory Panchayat.fromJson(Map<String, dynamic> json) =>
      _$PanchayatFromJson(json);
  Map<String, dynamic> toJson() => _$PanchayatToJson(this);
}

@JsonSerializable()
class ReportFetchresult {
  final List<AreaReport> reports;

  ReportFetchresult(this.reports);

  factory ReportFetchresult.fromJson(Map<String, dynamic> json) =>
      _$ReportFetchresultFromJson(json);
  Map<String, dynamic> toJson() => _$ReportFetchresultToJson(this);
}

@JsonSerializable()
class ReportUploadResult {
  final AreaReport report;

  ReportUploadResult(this.report);

  factory ReportUploadResult.fromJson(Map<String, dynamic> json) =>
      _$ReportUploadResultFromJson(json);
  Map<String, dynamic> toJson() => _$ReportUploadResultToJson(this);
}

@JsonSerializable()
class PanchyatFetchResult {
  final List<Panchayat> panchayat;

  PanchyatFetchResult(this.panchayat);

  factory PanchyatFetchResult.fromJson(Map<String, dynamic> json) =>
      _$PanchyatFetchResultFromJson(json);
  Map<String, dynamic> toJson() => _$PanchyatFetchResultToJson(this);
}
