import 'dart:ui';

import 'package:mla_connect/screens/poster/models.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'poster.g.dart';

//Entity models
@JsonEnum()
enum PosterTextAlign { Left, Center, Right }

@JsonEnum()
enum PosterTextFont { Arial_14, Arial_24, Arial_48 }

Color fromColorJson(int json) {
  return Color(json);
}

int colorToJson(Color color) {
  return color.value;
}

@JsonSerializable()
class OverlapPoint {
  final double dx;
  final double dy;
  OverlapPoint({
    required this.dx,
    required this.dy,
  });

  factory OverlapPoint.fromJson(Map<String, dynamic> json) =>
      _$OverlapPointFromJson(json);
  Map<String, dynamic> toJson() => _$OverlapPointToJson(this);
}

@JsonSerializable()
class PosterNetworkModel {
  final String id;
  final String posterUrl;
  final String overlapImageType;
  final double overlapImageSize;
  final OverlapPoint overlapPos;
  final int? createTimeMs;
  final String caption;

  @JsonKey(defaultValue: PosterTextAlign.Center)
  final PosterTextAlign textAlign;
  @JsonKey(defaultValue: PosterTextFont.Arial_24)
  final PosterTextFont textFont;
  @JsonKey(fromJson: fromColorJson, toJson: colorToJson)
  final Color textColor;
  @JsonKey(fromJson: fromColorJson, toJson: colorToJson)
  final Color textBg;

  PosterNetworkModel(
      {required this.id,
      required this.posterUrl,
      this.createTimeMs,
      required this.overlapImageType,
      required this.overlapImageSize,
      required this.overlapPos,
      required this.textAlign,
      required this.textFont,
      required this.textColor,
      required this.caption,
      required this.textBg});

  factory PosterNetworkModel.fromLocalData(
      {required PosterOverlayData data,
      required String posterUrl,
      required String caption,
      required String id}) {
    return PosterNetworkModel(
      id: id,
      posterUrl: posterUrl,
      overlapImageType: data.overlapImageType,
      overlapImageSize: data.overlapImageSize,
      overlapPos: OverlapPoint(dx: data.overlapPos.dx, dy: data.overlapPos.dy),
      textAlign: data.textAlign,
      textFont: data.textFont,
      textColor: data.textColor,
      textBg: data.textBg,
      caption: caption,
    );
  }
  // String formatedCreateTime() {
  //   try {
  //     final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
  //     return inputFormat
  //         .format(DateTime.fromMillisecondsSinceEpoch(createTimeMs!));
  //   } catch (e) {
  //     return "";
  //   }
  // }

  factory PosterNetworkModel.fromJson(Map<String, dynamic> json) =>
      _$PosterNetworkModelFromJson(json);
  Map<String, dynamic> toJson() => _$PosterNetworkModelToJson(this);
}

//Api Response models

@JsonSerializable()
class PosterCreateResult {
  final PosterNetworkModel poster;
  PosterCreateResult({
    required this.poster,
  });

  factory PosterCreateResult.fromJson(Map<String, dynamic> json) =>
      _$PosterCreateResultFromJson(json);
  Map<String, dynamic> toJson() => _$PosterCreateResultToJson(this);
}

@JsonSerializable()
class PosterFetchResult {
  final List<PosterNetworkModel> posters;
  final dynamic nextOffset;
  final bool pageEnd;

  PosterFetchResult({
    required this.posters,
    required this.nextOffset,
    required this.pageEnd,
  });

  factory PosterFetchResult.fromJson(Map<String, dynamic> json) =>
      _$PosterFetchResultFromJson(json);
  Map<String, dynamic> toJson() => _$PosterFetchResultToJson(this);
}

// APIs request models

@JsonSerializable()
class PosterApiRequest {
  final String action;
  final PosterNetworkModel? poster;
  final String? id;
  final dynamic startOffset;

  PosterApiRequest({
    required this.action,
    this.poster,
    this.id,
    this.startOffset,
  });

  factory PosterApiRequest.fromJson(Map<String, dynamic> json) =>
      _$PosterApiRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PosterApiRequestToJson(this);
}
