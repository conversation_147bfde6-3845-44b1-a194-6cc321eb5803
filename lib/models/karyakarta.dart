// assembly: [], block: [], booth: []
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'karyakarta.g.dart';

@CopyWith()
@JsonSerializable()
class KaryakartaList {
  final List<KaryakartaData> assembly;
  final List<KaryakartaData> block;
  final List<KaryakartaData> booth;

  KaryakartaList(
      {required this.assembly, required this.block, required this.booth});

  factory KaryakartaList.fromJson(Map<String, dynamic> json) =>
      _$KaryakartaListFromJson(json);
  Map<String, dynamic> toJson() => _$KaryakartaListToJson(this);
}

@CopyWith()
@JsonSerializable()
class KaryakartaData {
  final String? dob;
  final String? gender;
  final String? name;
  final String? profileUrl;
  final String? phoneNumber;

  KaryakartaData(
      {this.dob, this.gender, this.name, this.phoneNumber, this.profileUrl});

  factory KaryakartaData.fromJson(Map<String, dynamic> json) =>
      _$KaryakartaDataFromJson(json);
  Map<String, dynamic> toJson() => _$KaryakartaDataToJson(this);
}
