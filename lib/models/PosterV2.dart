import 'dart:ui';
import 'package:mla_connect/screens/poster/models.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'PosterV2.g.dart';

@JsonSerializable()
class PosterNetworkModelV2 {
  final String id;
  final String posterUrl;
  final int? createTimeMs;
  final String caption;
  PosterNetworkModelV2(
      {required this.id,
      required this.posterUrl,
      this.createTimeMs,
      required this.caption});

  factory PosterNetworkModelV2.fromJson(Map<String, dynamic> json) =>
      _$PosterNetworkModelV2FromJson(json);
  Map<String, dynamic> toJson() => _$PosterNetworkModelV2ToJson(this);
}

@JsonSerializable()
class PosterCreateResultV2 {
  final PosterNetworkModelV2 poster;
  PosterCreateResultV2({
    required this.poster,
  });

  factory PosterCreateResultV2.fromJson(Map<String, dynamic> json) =>
      _$PosterCreateResultV2FromJson(json);
  Map<String, dynamic> toJson() => _$PosterCreateResultV2ToJson(this);
}

@JsonSerializable()
class PosterApiRequestV2 {
  final String action;
  final PosterNetworkModelV2? poster;
  final String? id;
  final dynamic startOffset;

  PosterApiRequestV2({
    required this.action,
    this.poster,
    this.id,
    this.startOffset,
  });

  factory PosterApiRequestV2.fromJson(Map<String, dynamic> json) =>
      _$PosterApiRequestV2FromJson(json);
  Map<String, dynamic> toJson() => _$PosterApiRequestV2ToJson(this);
}

@JsonSerializable()
class PosterFetchResultV2 {
  final List<PosterNetworkModelV2> posters;
  final dynamic nextOffset;
  final bool pageEnd;

  PosterFetchResultV2({
    required this.posters,
    required this.nextOffset,
    required this.pageEnd,
  });

  factory PosterFetchResultV2.fromJson(Map<String, dynamic> json) =>
      _$PosterFetchResultV2FromJson(json);
  Map<String, dynamic> toJson() => _$PosterFetchResultV2ToJson(this);
}
