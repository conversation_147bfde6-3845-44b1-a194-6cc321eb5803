// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sector.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SectorData _$SectorDataFromJson(Map json) => SectorData(
      json['id'] as String,
      json['name'] as String,
      (json['booths'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          [],
    );

Map<String, dynamic> _$SectorDataToJson(SectorData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'booths': instance.booths,
    };

SectorFetchResult _$SectorFetchResultFromJson(Map json) => SectorFetchResult(
      (json['sectors'] as List<dynamic>?)
              ?.map((e) =>
                  SectorData.fromJson(Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
      (json['availableBooths'] as List<dynamic>?)
              ?.map((e) => Node.fromJson(Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
    );

Map<String, dynamic> _$SectorFetchResultToJson(SectorFetchResult instance) =>
    <String, dynamic>{
      'sectors': instance.sectors.map((e) => e.toJson()).toList(),
      'availableBooths':
          instance.availableBooths.map((e) => e.toJson()).toList(),
    };

SectorCreateResult _$SectorCreateResultFromJson(Map json) => SectorCreateResult(
      SectorData.fromJson(Map<String, dynamic>.from(json['sector'] as Map)),
    );

Map<String, dynamic> _$SectorCreateResultToJson(SectorCreateResult instance) =>
    <String, dynamic>{
      'sector': instance.sector.toJson(),
    };
