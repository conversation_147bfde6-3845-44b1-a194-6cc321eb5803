import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';

part 'new_voter.g.dart';

enum VOTER_EVENT_TYPE { CREATE, DELETE }

class NewVoterEvent {
  final VOTER_EVENT_TYPE type;
  final NewVoter voter;
  NewVoterEvent({
    required this.type,
    required this.voter,
  });
  NewVoterEvent.newEntry(NewVoter v)
      : this.type = VOTER_EVENT_TYPE.CREATE,
        this.voter = v;

  NewVoterEvent.delete(NewVoter v)
      : this.type = VOTER_EVENT_TYPE.DELETE,
        this.voter = v;
}

enum VOTER_RESOLUTION {
  @JsonValue("resolved")
  RESOLVED,
  @JsonValue("unresolved")
  UNRESOLVED,
}

@CopyWith()
@JsonSerializable()
class NewVoter {
  final String id;
  final String name;
  final String phone;
  final String adharNumber;
  final String boothNumber;
  final String memberUserId;
  @<PERSON><PERSON>Key(defaultValue: VOTER_RESOLUTION.UNRESOLVED)
  final VOTER_RESOLUTION resolution;

  NewVoter(
      {required this.id,
      required this.name,
      required this.phone,
      required this.adharNumber,
      required this.boothNumber,
      required this.resolution,
      required this.memberUserId});

  factory NewVoter.fromJson(Map<String, dynamic> json) =>
      _$NewVoterFromJson(json);
  Map<String, dynamic> toJson() => _$NewVoterToJson(this);
}

@JsonSerializable()
class VoterFetchCall {
  final VOTER_RESOLUTION resolution;
  final String action;
  final String? startOffset;
  final bool admin;
  VoterFetchCall({
    required this.resolution,
    required this.action,
    required this.startOffset,
    required this.admin,
  });

  VoterFetchCall.normal(
      {required VOTER_RESOLUTION resolution,
      required String? startOffset,
      required bool adminPage})
      : this.resolution = resolution,
        this.action = "fetch",
        this.admin = adminPage,
        this.startOffset = startOffset;

  factory VoterFetchCall.fromJson(Map<String, dynamic> json) =>
      _$VoterFetchCallFromJson(json);
  Map<String, dynamic> toJson() => _$VoterFetchCallToJson(this);
}

@JsonSerializable()
class NewApplicationResult {
  final List<NewVoter> voters;
  final bool pageEnd;
  final String? nextOffset;
  NewApplicationResult({
    required this.voters,
    required this.pageEnd,
    this.nextOffset,
  });

  factory NewApplicationResult.fromJson(Map<String, dynamic> json) =>
      _$NewApplicationResultFromJson(json);
  Map<String, dynamic> toJson() => _$NewApplicationResultToJson(this);
}

@JsonSerializable()
class NewVoterCall {
  final String action;
  final NewVoter new_voter;
  NewVoterCall({
    required this.action,
    required this.new_voter,
  });

  NewVoterCall.create(NewVoter v)
      : action = 'create',
        new_voter = v;

  NewVoterCall.delete(NewVoter v)
      : action = 'delete',
        new_voter = v;

  NewVoterCall.resolve(NewVoter v)
      : action = 'resolve',
        new_voter = v;

  factory NewVoterCall.fromJson(Map<String, dynamic> json) =>
      _$NewVoterCallFromJson(json);
  Map<String, dynamic> toJson() => _$NewVoterCallToJson(this);
}

@JsonSerializable()
class CreateNewVoterResponse {
  final NewVoter new_voter;

  CreateNewVoterResponse(this.new_voter);

  factory CreateNewVoterResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateNewVoterResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CreateNewVoterResponseToJson(this);
}
