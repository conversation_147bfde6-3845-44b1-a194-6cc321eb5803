// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'greet.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$GreetPeopleListCWProxy {
  GreetPeopleList data(List<GreetPeopleData> data);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `GreetPeopleList(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// GreetPeopleList(...).copyWith(id: 12, name: "My name")
  /// ````
  GreetPeopleList call({
    List<GreetPeopleData>? data,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfGreetPeopleList.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfGreetPeopleList.copyWith.fieldName(...)`
class _$GreetPeopleListCWProxyImpl implements _$GreetPeopleListCWProxy {
  const _$GreetPeopleListCWProxyImpl(this._value);

  final GreetPeopleList _value;

  @override
  GreetPeopleList data(List<GreetPeopleData> data) => this(data: data);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `GreetPeopleList(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// GreetPeopleList(...).copyWith(id: 12, name: "My name")
  /// ````
  GreetPeopleList call({
    Object? data = const $CopyWithPlaceholder(),
  }) {
    return GreetPeopleList(
      data: data == const $CopyWithPlaceholder() || data == null
          // ignore: unnecessary_non_null_assertion
          ? _value.data!
          // ignore: cast_nullable_to_non_nullable
          : data as List<GreetPeopleData>,
    );
  }
}

extension $GreetPeopleListCopyWith on GreetPeopleList {
  /// Returns a callable class that can be used as follows: `instanceOfGreetPeopleList.copyWith(...)` or like so:`instanceOfGreetPeopleList.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$GreetPeopleListCWProxy get copyWith => _$GreetPeopleListCWProxyImpl(this);
}

abstract class _$GreetPeopleDataCWProxy {
  GreetPeopleData name(String name);

  GreetPeopleData totalPoints(int totalPoints);

  GreetPeopleData profileUrl(String profileUrl);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `GreetPeopleData(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// GreetPeopleData(...).copyWith(id: 12, name: "My name")
  /// ````
  GreetPeopleData call({
    String? name,
    int? totalPoints,
    String? profileUrl,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfGreetPeopleData.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfGreetPeopleData.copyWith.fieldName(...)`
class _$GreetPeopleDataCWProxyImpl implements _$GreetPeopleDataCWProxy {
  const _$GreetPeopleDataCWProxyImpl(this._value);

  final GreetPeopleData _value;

  @override
  GreetPeopleData name(String name) => this(name: name);

  @override
  GreetPeopleData totalPoints(int totalPoints) =>
      this(totalPoints: totalPoints);

  @override
  GreetPeopleData profileUrl(String profileUrl) => this(profileUrl: profileUrl);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `GreetPeopleData(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// GreetPeopleData(...).copyWith(id: 12, name: "My name")
  /// ````
  GreetPeopleData call({
    Object? name = const $CopyWithPlaceholder(),
    Object? totalPoints = const $CopyWithPlaceholder(),
    Object? profileUrl = const $CopyWithPlaceholder(),
  }) {
    return GreetPeopleData(
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      totalPoints:
          totalPoints == const $CopyWithPlaceholder() || totalPoints == null
              // ignore: unnecessary_non_null_assertion
              ? _value.totalPoints!
              // ignore: cast_nullable_to_non_nullable
              : totalPoints as int,
      profileUrl:
          profileUrl == const $CopyWithPlaceholder() || profileUrl == null
              // ignore: unnecessary_non_null_assertion
              ? _value.profileUrl!
              // ignore: cast_nullable_to_non_nullable
              : profileUrl as String,
    );
  }
}

extension $GreetPeopleDataCopyWith on GreetPeopleData {
  /// Returns a callable class that can be used as follows: `instanceOfGreetPeopleData.copyWith(...)` or like so:`instanceOfGreetPeopleData.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$GreetPeopleDataCWProxy get copyWith => _$GreetPeopleDataCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GreetPeopleList _$GreetPeopleListFromJson(Map json) => GreetPeopleList(
      data: (json['data'] as List<dynamic>)
          .map((e) =>
              GreetPeopleData.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$GreetPeopleListToJson(GreetPeopleList instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
    };

GreetPeopleData _$GreetPeopleDataFromJson(Map json) => GreetPeopleData(
      name: json['name'] as String,
      totalPoints: json['totalPoints'] as int,
      profileUrl: json['profileUrl'] as String,
    );

Map<String, dynamic> _$GreetPeopleDataToJson(GreetPeopleData instance) =>
    <String, dynamic>{
      'profileUrl': instance.profileUrl,
      'totalPoints': instance.totalPoints,
      'name': instance.name,
    };
