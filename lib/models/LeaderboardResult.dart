import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'LeaderboardResult.g.dart';

@CopyWith()
@JsonSerializable()
class LeaderboardModel {
  final String phoneNumber;
  final int points;
  final String? profileUrl;
  final String name;

  LeaderboardModel(
      {required this.phoneNumber,
      required this.points,
      this.profileUrl,
      required this.name});

  factory LeaderboardModel.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardModelFromJson(json);
  Map<String, dynamic> toJson() => _$LeaderboardModelToJson(this);
}

@JsonSerializable()
class LeaderboardResult {
  final List<LeaderboardModel>? leaderBoard;
  LeaderboardResult({required this.leaderBoard});

  factory LeaderboardResult.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardResultFromJson(json);
  Map<String, dynamic> toJson() => _$LeaderboardResultToJson(this);
}
