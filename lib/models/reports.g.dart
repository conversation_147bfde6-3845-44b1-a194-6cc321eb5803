// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reports.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$AreaReportCWProxy {
  AreaReport id(String id);

  AreaReport pdf_url(String pdf_url);

  AreaReport name(String name);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AreaReport(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AreaReport(...).copyWith(id: 12, name: "My name")
  /// ````
  AreaReport call({
    String? id,
    String? pdf_url,
    String? name,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfAreaReport.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfAreaReport.copyWith.fieldName(...)`
class _$AreaReportCWProxyImpl implements _$AreaReportCWProxy {
  const _$AreaReportCWProxyImpl(this._value);

  final AreaReport _value;

  @override
  AreaReport id(String id) => this(id: id);

  @override
  AreaReport pdf_url(String pdf_url) => this(pdf_url: pdf_url);

  @override
  AreaReport name(String name) => this(name: name);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AreaReport(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AreaReport(...).copyWith(id: 12, name: "My name")
  /// ````
  AreaReport call({
    Object? id = const $CopyWithPlaceholder(),
    Object? pdf_url = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
  }) {
    return AreaReport(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      pdf_url: pdf_url == const $CopyWithPlaceholder() || pdf_url == null
          // ignore: unnecessary_non_null_assertion
          ? _value.pdf_url!
          // ignore: cast_nullable_to_non_nullable
          : pdf_url as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
    );
  }
}

extension $AreaReportCopyWith on AreaReport {
  /// Returns a callable class that can be used as follows: `instanceOfAreaReport.copyWith(...)` or like so:`instanceOfAreaReport.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$AreaReportCWProxy get copyWith => _$AreaReportCWProxyImpl(this);
}

abstract class _$PanchayatCWProxy {
  Panchayat id(String id);

  Panchayat name(String name);

  Panchayat village(String village);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Panchayat(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Panchayat(...).copyWith(id: 12, name: "My name")
  /// ````
  Panchayat call({
    String? id,
    String? name,
    String? village,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfPanchayat.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfPanchayat.copyWith.fieldName(...)`
class _$PanchayatCWProxyImpl implements _$PanchayatCWProxy {
  const _$PanchayatCWProxyImpl(this._value);

  final Panchayat _value;

  @override
  Panchayat id(String id) => this(id: id);

  @override
  Panchayat name(String name) => this(name: name);

  @override
  Panchayat village(String village) => this(village: village);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Panchayat(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Panchayat(...).copyWith(id: 12, name: "My name")
  /// ````
  Panchayat call({
    Object? id = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? village = const $CopyWithPlaceholder(),
  }) {
    return Panchayat(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      village: village == const $CopyWithPlaceholder() || village == null
          // ignore: unnecessary_non_null_assertion
          ? _value.village!
          // ignore: cast_nullable_to_non_nullable
          : village as String,
    );
  }
}

extension $PanchayatCopyWith on Panchayat {
  /// Returns a callable class that can be used as follows: `instanceOfPanchayat.copyWith(...)` or like so:`instanceOfPanchayat.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$PanchayatCWProxy get copyWith => _$PanchayatCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AreaReport _$AreaReportFromJson(Map json) => AreaReport(
      id: json['id'] as String,
      pdf_url: json['pdf_url'] as String,
      name: json['name'] as String,
    );

Map<String, dynamic> _$AreaReportToJson(AreaReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'pdf_url': instance.pdf_url,
      'name': instance.name,
    };

Panchayat _$PanchayatFromJson(Map json) => Panchayat(
      id: json['id'] as String,
      name: json['name'] as String,
      village: json['village'] as String,
    );

Map<String, dynamic> _$PanchayatToJson(Panchayat instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'village': instance.village,
    };

ReportFetchresult _$ReportFetchresultFromJson(Map json) => ReportFetchresult(
      (json['reports'] as List<dynamic>)
          .map((e) => AreaReport.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$ReportFetchresultToJson(ReportFetchresult instance) =>
    <String, dynamic>{
      'reports': instance.reports.map((e) => e.toJson()).toList(),
    };

ReportUploadResult _$ReportUploadResultFromJson(Map json) => ReportUploadResult(
      AreaReport.fromJson(Map<String, dynamic>.from(json['report'] as Map)),
    );

Map<String, dynamic> _$ReportUploadResultToJson(ReportUploadResult instance) =>
    <String, dynamic>{
      'report': instance.report.toJson(),
    };

PanchyatFetchResult _$PanchyatFetchResultFromJson(Map json) =>
    PanchyatFetchResult(
      (json['panchayat'] as List<dynamic>)
          .map((e) => Panchayat.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$PanchyatFetchResultToJson(
        PanchyatFetchResult instance) =>
    <String, dynamic>{
      'panchayat': instance.panchayat.map((e) => e.toJson()).toList(),
    };
