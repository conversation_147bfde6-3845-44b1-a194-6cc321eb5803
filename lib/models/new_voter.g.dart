// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'new_voter.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$NewVoterCWProxy {
  NewVoter id(String id);

  NewVoter name(String name);

  NewVoter phone(String phone);

  NewVoter adharNumber(String adharNumber);

  NewVoter boothNumber(String boothNumber);

  NewVoter resolution(VOTER_RESOLUTION resolution);

  NewVoter memberUserId(String memberUserId);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `NewVoter(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// NewVoter(...).copyWith(id: 12, name: "My name")
  /// ````
  NewVoter call({
    String? id,
    String? name,
    String? phone,
    String? adharNumber,
    String? boothNumber,
    VOTER_RESOLUTION? resolution,
    String? memberUserId,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfNewVoter.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfNewVoter.copyWith.fieldName(...)`
class _$NewVoterCWProxyImpl implements _$NewVoterCWProxy {
  const _$NewVoterCWProxyImpl(this._value);

  final NewVoter _value;

  @override
  NewVoter id(String id) => this(id: id);

  @override
  NewVoter name(String name) => this(name: name);

  @override
  NewVoter phone(String phone) => this(phone: phone);

  @override
  NewVoter adharNumber(String adharNumber) => this(adharNumber: adharNumber);

  @override
  NewVoter boothNumber(String boothNumber) => this(boothNumber: boothNumber);

  @override
  NewVoter resolution(VOTER_RESOLUTION resolution) =>
      this(resolution: resolution);

  @override
  NewVoter memberUserId(String memberUserId) =>
      this(memberUserId: memberUserId);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `NewVoter(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// NewVoter(...).copyWith(id: 12, name: "My name")
  /// ````
  NewVoter call({
    Object? id = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? phone = const $CopyWithPlaceholder(),
    Object? adharNumber = const $CopyWithPlaceholder(),
    Object? boothNumber = const $CopyWithPlaceholder(),
    Object? resolution = const $CopyWithPlaceholder(),
    Object? memberUserId = const $CopyWithPlaceholder(),
  }) {
    return NewVoter(
      id: id == const $CopyWithPlaceholder() || id == null
          // ignore: unnecessary_non_null_assertion
          ? _value.id!
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      phone: phone == const $CopyWithPlaceholder() || phone == null
          // ignore: unnecessary_non_null_assertion
          ? _value.phone!
          // ignore: cast_nullable_to_non_nullable
          : phone as String,
      adharNumber:
          adharNumber == const $CopyWithPlaceholder() || adharNumber == null
              // ignore: unnecessary_non_null_assertion
              ? _value.adharNumber!
              // ignore: cast_nullable_to_non_nullable
              : adharNumber as String,
      boothNumber:
          boothNumber == const $CopyWithPlaceholder() || boothNumber == null
              // ignore: unnecessary_non_null_assertion
              ? _value.boothNumber!
              // ignore: cast_nullable_to_non_nullable
              : boothNumber as String,
      resolution:
          resolution == const $CopyWithPlaceholder() || resolution == null
              // ignore: unnecessary_non_null_assertion
              ? _value.resolution!
              // ignore: cast_nullable_to_non_nullable
              : resolution as VOTER_RESOLUTION,
      memberUserId:
          memberUserId == const $CopyWithPlaceholder() || memberUserId == null
              // ignore: unnecessary_non_null_assertion
              ? _value.memberUserId!
              // ignore: cast_nullable_to_non_nullable
              : memberUserId as String,
    );
  }
}

extension $NewVoterCopyWith on NewVoter {
  /// Returns a callable class that can be used as follows: `instanceOfNewVoter.copyWith(...)` or like so:`instanceOfNewVoter.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$NewVoterCWProxy get copyWith => _$NewVoterCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NewVoter _$NewVoterFromJson(Map json) => NewVoter(
      id: json['id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String,
      adharNumber: json['adharNumber'] as String,
      boothNumber: json['boothNumber'] as String,
      resolution:
          $enumDecodeNullable(_$VOTER_RESOLUTIONEnumMap, json['resolution']) ??
              VOTER_RESOLUTION.UNRESOLVED,
      memberUserId: json['memberUserId'] as String,
    );

Map<String, dynamic> _$NewVoterToJson(NewVoter instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'adharNumber': instance.adharNumber,
      'boothNumber': instance.boothNumber,
      'memberUserId': instance.memberUserId,
      'resolution': _$VOTER_RESOLUTIONEnumMap[instance.resolution]!,
    };

const _$VOTER_RESOLUTIONEnumMap = {
  VOTER_RESOLUTION.RESOLVED: 'resolved',
  VOTER_RESOLUTION.UNRESOLVED: 'unresolved',
};

VoterFetchCall _$VoterFetchCallFromJson(Map json) => VoterFetchCall(
      resolution: $enumDecode(_$VOTER_RESOLUTIONEnumMap, json['resolution']),
      action: json['action'] as String,
      startOffset: json['startOffset'] as String?,
      admin: json['admin'] as bool,
    );

Map<String, dynamic> _$VoterFetchCallToJson(VoterFetchCall instance) =>
    <String, dynamic>{
      'resolution': _$VOTER_RESOLUTIONEnumMap[instance.resolution]!,
      'action': instance.action,
      'startOffset': instance.startOffset,
      'admin': instance.admin,
    };

NewApplicationResult _$NewApplicationResultFromJson(Map json) =>
    NewApplicationResult(
      voters: (json['voters'] as List<dynamic>)
          .map((e) => NewVoter.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      pageEnd: json['pageEnd'] as bool,
      nextOffset: json['nextOffset'] as String?,
    );

Map<String, dynamic> _$NewApplicationResultToJson(
        NewApplicationResult instance) =>
    <String, dynamic>{
      'voters': instance.voters.map((e) => e.toJson()).toList(),
      'pageEnd': instance.pageEnd,
      'nextOffset': instance.nextOffset,
    };

NewVoterCall _$NewVoterCallFromJson(Map json) => NewVoterCall(
      action: json['action'] as String,
      new_voter: NewVoter.fromJson(
          Map<String, dynamic>.from(json['new_voter'] as Map)),
    );

Map<String, dynamic> _$NewVoterCallToJson(NewVoterCall instance) =>
    <String, dynamic>{
      'action': instance.action,
      'new_voter': instance.new_voter.toJson(),
    };

CreateNewVoterResponse _$CreateNewVoterResponseFromJson(Map json) =>
    CreateNewVoterResponse(
      NewVoter.fromJson(Map<String, dynamic>.from(json['new_voter'] as Map)),
    );

Map<String, dynamic> _$CreateNewVoterResponseToJson(
        CreateNewVoterResponse instance) =>
    <String, dynamic>{
      'new_voter': instance.new_voter.toJson(),
    };
