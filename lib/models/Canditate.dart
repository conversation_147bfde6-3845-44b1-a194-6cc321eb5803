import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';

part 'Canditate.g.dart';

@CopyWith()
@JsonSerializable()
class Candidate {
  final String? gender;
  final String? address_2;
  final String? mobile_no;
  final String? rln_name_v1;
  final String? rln_lastname_en;
  final String? name_v1;
  final String epic_no;
  final String? polling_station_2;
  final String id;
  final String? lastname_v1;
  final String? address;
  final String? rln_type;
  final String? part_no;
  final String? dob;
  final String? house_no;
  final String? house_no_v1;
  final String? sl_no_part;
  final String? rln_lastname_v1;
  final String? polling_station;
  final String? section_no;
  final String? ac_no;
  final String? age;
  final String? name_en;
  final String? lastname_en;
  final String? rln_name_en;

  Candidate({
    this.gender,
    this.address_2,
    this.mobile_no,
    this.rln_name_v1,
    this.rln_lastname_en,
    this.name_v1,
    required this.epic_no,
    this.polling_station_2,
    required this.id,
    this.lastname_v1,
    this.address,
    this.rln_type,
    this.part_no,
    this.dob,
    this.house_no,
    this.house_no_v1,
    this.sl_no_part,
    this.rln_lastname_v1,
    this.polling_station,
    this.section_no,
    this.ac_no,
    this.age,
    this.name_en,
    this.lastname_en,
    this.rln_name_en,
  });

  factory Candidate.fromJson(Map<String, dynamic> json) {
    return Candidate(
      gender: json['gender'].toString(),
      address_2: json['address_2'].toString(),
      mobile_no: json['mobile_no'].toString(),
      rln_name_v1: json['rln_name_v1'].toString(),
      rln_lastname_en: json['rln_lastname_en'].toString(),
      name_v1: json['name_v1'].toString(),
      epic_no: json['epic_no'].toString(),
      polling_station_2: json['polling_station_2'].toString(),
      id: json['id'].toString(),
      lastname_v1: json['lastname_v1'].toString(),
      address: json['address'].toString(),
      rln_type: json['rln_type'].toString(),
      part_no: json['part_no'].toString(),
      dob: json['dob'].toString(),
      house_no: json['house_no'].toString(),
      house_no_v1: json['house_no_v1'].toString(),
      sl_no_part: json['sl_no_part'].toString(),
      rln_lastname_v1: json['rln_lastname_v1'].toString(),
      polling_station: json['polling_station'].toString(),
      section_no: json['section_no'].toString(),
      ac_no: json['ac_no'].toString(),
      age: json['age'].toString(),
      name_en: json['name_en'].toString(),
      lastname_en: json['lastname_en'].toString(),
      rln_name_en: json['rln_name_en'].toString(),
    );
  }
  // _$CandidateFromJson(json);
  Map<String, dynamic> toJson() => _$CandidateToJson(this);
}

@CopyWith()
@JsonSerializable()
class AllCandidates {
  final List<Candidate> candidates;

  AllCandidates({required this.candidates});

  factory AllCandidates.fromJson(Map<String, dynamic> json) =>
      _$AllCandidatesFromJson(json);
  Map<String, dynamic> toJson() => _$AllCandidatesToJson(this);
}
