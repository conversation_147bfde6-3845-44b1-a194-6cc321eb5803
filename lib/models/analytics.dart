import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:intl/intl.dart';

part 'analytics.g.dart';

class ItemSubCat {
  final String id;
  final String value;
  ItemSubCat({
    required this.id,
    required this.value,
  });
}

@JsonSerializable()
@CopyWith()
class AnalyticsItem {
  final String name;
  @JsonKey(name: "last_updated")
  final int lastUpdatedInMs;
  final int roleLevel;
  @JsonKey(ignore: true)
  final List<ItemSubCat> options;

  AnalyticsItem(
      {required this.name,
      required this.lastUpdatedInMs,
      required this.roleLevel,
      List<ItemSubCat>? options})
      : this.options = options ?? [];

  String formatedCreateTime() {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat
          .format(DateTime.fromMillisecondsSinceEpoch(lastUpdatedInMs));
    } catch (e) {
      return "";
    }
  }

  factory AnalyticsItem.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsItemFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsItemToJson(this);
}

@JsonSerializable()
class AnalyticsHome {
  final List<AnalyticsItem> items;
  AnalyticsHome({
    required this.items,
  });

  factory AnalyticsHome.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsHomeFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsHomeToJson(this);
}

/**
 * GLobal Functions for Custom ToJson and ForJson
 */

List<CasteData> _casteFromJson(Map jsonI) {
  final json = Map<String, dynamic>.from(jsonI);
  return json.entries
      .map((e) => CasteData(id: e.key, count: e.value as int))
      .toList();
}

List<VotingData> _votingFromJson(Map jsonI) {
  final json = Map<String, dynamic>.from(jsonI);
  return json.entries
      .map((e) => VotingData(
          id: e.key,
          caste_dis: _casteFromJson(
              Map<String, dynamic>.from(e.value['caste'] as Map)),
          count: e.value['total'] as int))
      .toList();
}

class CasteData {
  final String id;
  final int count;
  const CasteData({
    required this.id,
    required this.count,
  });
}

class VotingData {
  final String id;
  final List<CasteData> caste_dis;
  final int count;
  const VotingData({
    required this.id,
    required this.caste_dis,
    required this.count,
  });
}

@JsonSerializable(createToJson: false)
class AnalyticsData {
  @JsonKey(name: "caste", fromJson: _casteFromJson)
  final List<CasteData> caste;
  @JsonKey(name: "voting_pref", fromJson: _votingFromJson)
  final List<VotingData> votes;
  AnalyticsData({
    required this.caste,
    required this.votes,
  });

  factory AnalyticsData.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsDataFromJson(json);
}

@JsonSerializable(createToJson: false)
class AnalyticsResponse {
  final AnalyticsData data;
  AnalyticsResponse({
    required this.data,
  });
  factory AnalyticsResponse.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsResponseFromJson(json);
}
