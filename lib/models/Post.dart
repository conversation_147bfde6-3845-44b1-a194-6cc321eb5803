import 'package:mla_connect/models/issues.dart';
import 'package:mla_connect/utils/shareUtil.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:intl/intl.dart';
part 'Post.g.dart';

@CopyWith()
@JsonSerializable()
class Post {
  final String? mediaurl;
  final String postType;
  final String? text;
  final String id;
  final int createTimestamp;
  final String? authorName;
  final String? authorProfileUrl;
  final String authorId;
  final List<String>? like;
  final Map<String, List<String>>? share;

  Post(
      {this.mediaurl,
      required this.authorName,
      this.authorProfileUrl,
      required this.postType,
      required this.id,
      required this.createTimestamp,
      required this.authorId,
      this.like,
      this.share,
      this.text});

  String formatedCreateTime() {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat
          .format(DateTime.fromMillisecondsSinceEpoch(createTimestamp));
    } catch (e) {
      return "";
    }
  }

  factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);
  Map<String, dynamic> toJson() => _$PostToJson(this);
}

@JsonSerializable()
class CreatePostResult {
  final Post post;

  CreatePostResult({required this.post});

  factory CreatePostResult.fromJson(Map<String, dynamic> json) =>
      _$CreatePostResultFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePostResultToJson(this);
}

enum EventType { DELETE, NEW_POST, LIKE, UNLIKE }

enum ShareType { Whatsapp, Facebook, Any }

class PostEvent {
  final EventType type;
  final Post? post;

  PostEvent(this.type, this.post);
}

class IssueEvent {
  final EventType type;
  final Issue? issue;

  IssueEvent(this.type, this.issue);
}
