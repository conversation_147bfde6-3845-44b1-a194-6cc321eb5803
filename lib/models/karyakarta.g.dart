// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'karyakarta.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$KaryakartaListCWProxy {
  KaryakartaList assembly(List<KaryakartaData> assembly);

  KaryakartaList block(List<KaryakartaData> block);

  KaryakartaList booth(List<KaryakartaData> booth);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `<PERSON><PERSON><PERSON><PERSON>List(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// KaryakartaList(...).copyWith(id: 12, name: "My name")
  /// ````
  KaryakartaList call({
    List<KaryakartaData>? assembly,
    List<KaryakartaData>? block,
    List<KaryakartaData>? booth,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfKaryakartaList.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfKaryakartaList.copyWith.fieldName(...)`
class _$KaryakartaListCWProxyImpl implements _$KaryakartaListCWProxy {
  const _$KaryakartaListCWProxyImpl(this._value);

  final KaryakartaList _value;

  @override
  KaryakartaList assembly(List<KaryakartaData> assembly) =>
      this(assembly: assembly);

  @override
  KaryakartaList block(List<KaryakartaData> block) => this(block: block);

  @override
  KaryakartaList booth(List<KaryakartaData> booth) => this(booth: booth);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `KaryakartaList(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// KaryakartaList(...).copyWith(id: 12, name: "My name")
  /// ````
  KaryakartaList call({
    Object? assembly = const $CopyWithPlaceholder(),
    Object? block = const $CopyWithPlaceholder(),
    Object? booth = const $CopyWithPlaceholder(),
  }) {
    return KaryakartaList(
      assembly: assembly == const $CopyWithPlaceholder() || assembly == null
          // ignore: unnecessary_non_null_assertion
          ? _value.assembly!
          // ignore: cast_nullable_to_non_nullable
          : assembly as List<KaryakartaData>,
      block: block == const $CopyWithPlaceholder() || block == null
          // ignore: unnecessary_non_null_assertion
          ? _value.block!
          // ignore: cast_nullable_to_non_nullable
          : block as List<KaryakartaData>,
      booth: booth == const $CopyWithPlaceholder() || booth == null
          // ignore: unnecessary_non_null_assertion
          ? _value.booth!
          // ignore: cast_nullable_to_non_nullable
          : booth as List<KaryakartaData>,
    );
  }
}

extension $KaryakartaListCopyWith on KaryakartaList {
  /// Returns a callable class that can be used as follows: `instanceOfKaryakartaList.copyWith(...)` or like so:`instanceOfKaryakartaList.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$KaryakartaListCWProxy get copyWith => _$KaryakartaListCWProxyImpl(this);
}

abstract class _$KaryakartaDataCWProxy {
  KaryakartaData dob(String? dob);

  KaryakartaData gender(String? gender);

  KaryakartaData name(String? name);

  KaryakartaData phoneNumber(String? phoneNumber);

  KaryakartaData profileUrl(String? profileUrl);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `KaryakartaData(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// KaryakartaData(...).copyWith(id: 12, name: "My name")
  /// ````
  KaryakartaData call({
    String? dob,
    String? gender,
    String? name,
    String? phoneNumber,
    String? profileUrl,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfKaryakartaData.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfKaryakartaData.copyWith.fieldName(...)`
class _$KaryakartaDataCWProxyImpl implements _$KaryakartaDataCWProxy {
  const _$KaryakartaDataCWProxyImpl(this._value);

  final KaryakartaData _value;

  @override
  KaryakartaData dob(String? dob) => this(dob: dob);

  @override
  KaryakartaData gender(String? gender) => this(gender: gender);

  @override
  KaryakartaData name(String? name) => this(name: name);

  @override
  KaryakartaData phoneNumber(String? phoneNumber) =>
      this(phoneNumber: phoneNumber);

  @override
  KaryakartaData profileUrl(String? profileUrl) => this(profileUrl: profileUrl);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `KaryakartaData(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// KaryakartaData(...).copyWith(id: 12, name: "My name")
  /// ````
  KaryakartaData call({
    Object? dob = const $CopyWithPlaceholder(),
    Object? gender = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? phoneNumber = const $CopyWithPlaceholder(),
    Object? profileUrl = const $CopyWithPlaceholder(),
  }) {
    return KaryakartaData(
      dob: dob == const $CopyWithPlaceholder()
          ? _value.dob
          // ignore: cast_nullable_to_non_nullable
          : dob as String?,
      gender: gender == const $CopyWithPlaceholder()
          ? _value.gender
          // ignore: cast_nullable_to_non_nullable
          : gender as String?,
      name: name == const $CopyWithPlaceholder()
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String?,
      phoneNumber: phoneNumber == const $CopyWithPlaceholder()
          ? _value.phoneNumber
          // ignore: cast_nullable_to_non_nullable
          : phoneNumber as String?,
      profileUrl: profileUrl == const $CopyWithPlaceholder()
          ? _value.profileUrl
          // ignore: cast_nullable_to_non_nullable
          : profileUrl as String?,
    );
  }
}

extension $KaryakartaDataCopyWith on KaryakartaData {
  /// Returns a callable class that can be used as follows: `instanceOfKaryakartaData.copyWith(...)` or like so:`instanceOfKaryakartaData.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$KaryakartaDataCWProxy get copyWith => _$KaryakartaDataCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

KaryakartaList _$KaryakartaListFromJson(Map json) => KaryakartaList(
      assembly: (json['assembly'] as List<dynamic>)
          .map((e) =>
              KaryakartaData.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      block: (json['block'] as List<dynamic>)
          .map((e) =>
              KaryakartaData.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      booth: (json['booth'] as List<dynamic>)
          .map((e) =>
              KaryakartaData.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$KaryakartaListToJson(KaryakartaList instance) =>
    <String, dynamic>{
      'assembly': instance.assembly.map((e) => e.toJson()).toList(),
      'block': instance.block.map((e) => e.toJson()).toList(),
      'booth': instance.booth.map((e) => e.toJson()).toList(),
    };

KaryakartaData _$KaryakartaDataFromJson(Map json) => KaryakartaData(
      dob: json['dob'] as String?,
      gender: json['gender'] as String?,
      name: json['name'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      profileUrl: json['profileUrl'] as String?,
    );

Map<String, dynamic> _$KaryakartaDataToJson(KaryakartaData instance) =>
    <String, dynamic>{
      'dob': instance.dob,
      'gender': instance.gender,
      'name': instance.name,
      'profileUrl': instance.profileUrl,
      'phoneNumber': instance.phoneNumber,
    };
