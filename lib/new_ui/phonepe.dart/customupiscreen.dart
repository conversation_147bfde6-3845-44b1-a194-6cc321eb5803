// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:easy_upi_payment/easy_upi_payment.dart';
 
// class UpiScreen extends StatefulWidget {
//   @override
//   _UpiScreenState createState() => _UpiScreenState();
// }

// class _UpiScreenState extends State<UpiScreen> {
    
    
//  Future<void> startTransaction()async {
//   try{
//      final response = await EasyUpiPaymentPlatform.instance.startPayment(
//       EasyUpiPaymentModel(
//           // payeeVpa: 'kaleem.nsit-1@okhdfcbank',
//           // payeeName: 'kaleem',
//           // amount: 1.0,
//           // description: 'Testing payment',
//           payeeVpa: 'govmattersonline@ybl',
//           payeeName: 'govmatter',
//           amount: 10,
//           description: 'Testing payment',
//           payeeMerchantCode: 'GOVMATTERSONLINE'
//         ),
//       );
//   }catch(e){
//      //print("payment error" + e.toString());
//   }
//      }  
     
 
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('UPI'),
//       ),
//       body:Center(child: TextButton(onPressed:  () async{
//        await startTransaction() ;  
//       }, child: Text("Tap")),),
      
//     );
//   }
// }