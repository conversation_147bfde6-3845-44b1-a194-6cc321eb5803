import 'dart:io';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/imagePickerUtil.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:photo_gallery/photo_gallery.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';

class CreatePost extends StatefulWidget {
  const CreatePost({Key? key}) : super(key: key);

  @override
  State<CreatePost> createState() => _CreatePostState();
}

class _CreatePostState extends State<CreatePost> {
  List<Album>? _albums;
  bool _loading = false;
  bool defaultProfile = false;
  File? imageFile;
  bool uploadingPostLoader = false;
  String? selectedMedia;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _loading = true;
    // initAsync();
  }

  TextEditingController captionController = new TextEditingController();
  VideoPlayerController? _controller;

  Future<void> initAsync() async {
    if (await _promptPermissionSetting()) {
      List<Album> albums =
          await PhotoGallery.listAlbums(mediumType: MediumType.image);
      setState(() {
        _albums = albums;
        _loading = false;
      });
    }
    setState(() {
      _loading = false;
    });
  }

  Future<bool> _promptPermissionSetting() async {
    if (Platform.isIOS &&
            await Permission.storage.request().isGranted &&
            await Permission.photos.request().isGranted ||
        Platform.isAndroid && await Permission.storage.request().isGranted) {
      return true;
    }
    return false;
  }

  createPost() async {
    String validationText = validation();
    if (validationText == "text") {
      if (captionController.text.trim().isEmpty) {
        final snackBar = SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
          content: const Text('Select any media or write text'),
        );
        ScaffoldMessenger.of(context).showSnackBar(snackBar);
        return;
      }
    }
    if (validationText == "text" ||
        validationText == "image" ||
        validationText == "video") {
      final snackBar = SnackBar(
        content: const Text('Posting...'),
      );
      setState(() {
        uploadingPostLoader = true;
      });
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      bool result;

      if (validationText == "text") {
        result = await FirebaseCloud.instance
            .createPost(validationText, text: captionController.text);
      } else {
        String url = await FirebaseCloud.instance
            .uploadFileToFirebaseStorage(imageFile!);
        if (captionController.text == "")
          result = await FirebaseCloud.instance
              .createPost(validationText, mediaUrl: url);
        else {
          result = await FirebaseCloud.instance.createPost(validationText,
              mediaUrl: url, text: captionController.text);
        }
      }

      if (result) {
        Navigator.pop(context);
      } else {
        final snackBarFailed = SnackBar(
          content: const Text('Failed, Try Again'),
        );
        ScaffoldMessenger.of(context).showSnackBar(snackBarFailed);
      }
    } else {
      Fluttertoast.showToast(msg: validationText);
    }
  }

  String validation() {
    // bool caption = false, pic = false;

    // if (captionController.text.isNotEmpty) {
    //   caption = true;
    // }
    // if (imageFile != null) {
    //   pic = true;
    // }
    if (selectedMedia == null) {
      return 'text';
    }
    if (selectedMedia == 'video') {
      return 'video';
    } else if (selectedMedia == 'image') {
      return 'image';
    }
    // if (caption && pic)
    //   return "image";
    // else if (caption)
    //   return "text";
    // else if (pic)
    //   return "image";
    else
      return "Select a media or write a text";
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller?.dispose();

    super.dispose();
  }

  _pickVideo() async {
    _controller?.dispose();
    final XFile? image = await ImageUtil.pickVideoFromFile();

    if (image != null) {
      final path = image.path;

      if (await ImageUtil.checkImageSize(image.path, context)) {
        return;
      }
      File f = File(path);

      _controller = VideoPlayerController.file(f);
      await _controller!.initialize();
      await _controller!.play();
      _controller!.setLooping(true);

      setState(() {
        imageFile = f;
        defaultProfile = true;
        selectedMedia = 'video';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
            title: Text("Create Post"),
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context, true);
              },
              icon: Icon(Icons.arrow_back),
              color: Colors.black,
            )),
        body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.all(10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Post Details and Preview",
                      style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    defaultProfile
                        ?
                        // ? AspectRatio(
                        // aspectRatio: 4.0 / 3.0,
                        // child:
                        Column(children: [
                            ClipRRect(
                              child: selectedMedia == 'video'
                                  ? Container(
                                      height:
                                          MediaQuery.of(context).size.width *
                                              0.95,
                                      width: MediaQuery.of(context).size.width *
                                          0.95,
                                      child: VideoPlayer(_controller!))
                                  : Image.file(
                                      imageFile!,
                                      height:
                                          MediaQuery.of(context).size.width *
                                              0.95,
                                      width: MediaQuery.of(context).size.width *
                                          0.95,
                                      fit: BoxFit.contain,
                                    ),
                            ),
                            SizedBox(
                              height: 20,
                            ),
                            Center(
                                child: ElevatedButton(
                                    style: ButtonStyle(
                                      shape: MaterialStateProperty.all(
                                        RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                      ),
                                      minimumSize: MaterialStateProperty.all(
                                          const Size(300, 45)),
                                      backgroundColor:
                                          MaterialStateProperty.all(
                                              Colors.black),
                                    ),
                                    onPressed: () async {
                                      if (selectedMedia == "image") {
                                        final XFile? image =
                                            await ImageUtil.pickImageFromFile();

                                        if (image != null) {
                                          if (await ImageUtil.checkImageSize(
                                              image.path, context)) {
                                            return;
                                          }
                                          final path = image.path;
                                          File f = File(path);

                                          setState(() {
                                            imageFile = f;
                                            defaultProfile = true;
                                          });
                                           //print(image.path);
                                        }
                                      } else {
                                        await _pickVideo();
                                      }
                                    },
                                    child: Text(
                                      "Change Media",
                                      style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                    ))),
                          ])
                        : Container(
                            decoration: BoxDecoration(
                              color: Color.fromARGB(255, 181, 178, 178),
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(30),
                                  bottomRight: Radius.circular(30)),
                            ),
                            height: 200,
                            width: MediaQuery.of(context).size.width,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      IconButton(
                                          onPressed: () async {
                                            final XFile? image = await ImageUtil
                                                .pickImageFromFile();
                                            if (image != null) {
                                              final path = image.path;
                                              File f = File(path);
                                              setState(() {
                                                imageFile = f;
                                                defaultProfile = true;
                                                selectedMedia = 'image';
                                              });
                                            }
                                          },
                                          icon: ImageIcon(
                                            AssetImage(
                                                "assets/icons/Camera.png"),
                                            size: 50,
                                            color: Colors.white,
                                          )),
                                      Text(
                                        "Select Photo",
                                        style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
                                      )
                                    ]),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    IconButton(
                                        onPressed: _pickVideo,
                                        icon: ImageIcon(
                                          AssetImage("assets/icons/Video.png"),
                                          size: 50,
                                          color: Colors.white,
                                        )),
                                    Text(
                                      "Select Video",
                                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                    SizedBox(
                      height: 40,
                    ),
                    Padding(
                        padding: EdgeInsets.all(0),
                        child: Card(
                            elevation: 3,
                            child: TextFormField(
                              controller: captionController,
                              decoration: InputDecoration(
                                hintText: 'Enter Caption',
                                contentPadding: EdgeInsets.all(20.0),
                              ),
                              minLines:
                                  6, // any number you need (It works as the rows for the textarea)
                              keyboardType: TextInputType.multiline,
                              maxLines: null,
                            ))),
                    SizedBox(
                      height: 50,
                    ),
                    Center(
                        child: uploadingPostLoader
                            ? CircularProgressIndicator()
                            : ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  minimumSize: MaterialStateProperty.all(
                                      const Size(300, 45)),
                                  backgroundColor:
                                      MaterialStateProperty.all(Colors.black),
                                ),
                                onPressed: createPost,
                                child: Text(
                                  "Post",
                                  style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                ))),
                    SizedBox(
                      height: 30,
                    )
                  ],
                ))));
  }
}
