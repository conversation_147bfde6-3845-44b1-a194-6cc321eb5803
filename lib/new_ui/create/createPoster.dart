import 'dart:io';
import 'package:dotted_border/dotted_border.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/new_ui/home/<USER>';
import 'package:mla_connect/screens/poster/create_poster.dart';
import 'package:mla_connect/screens/poster/models.dart';
import 'package:mla_connect/screens/poster/poster_util.dart';
import 'package:mla_connect/utils/imagePickerUtil.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:image_cropper/image_cropper.dart';
import 'dart:io' as Io;
import 'package:image/image.dart' as im;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../../screens/poster/poster_admin_page.dart';
import '../home/<USER>';

class CreatePoster extends StatefulWidget {
  const CreatePoster({Key? key}) : super(key: key);

  @override
  State<CreatePoster> createState() => _CreatePosterState();
}

class _CreatePosterState extends State<CreatePoster> {
  TextEditingController captionController = new TextEditingController(text: "");
  bool receivedPoster = false;
  String? posterPath;
  PosterOverlayData? posterOverlayData;
  String? imagePath;
  bool generatingImage = false;
  bool error = false;

  LoginService login = LoginService();

  // void createMyPoster(PosterOverlayData res) async {
  //   try {
  //     final posterUrl = await FirebaseCloud.instance
  //         .uploadFileToFirebaseStorage(File(posterPath!));
  //     final poster = PosterNetworkModel.fromLocalData(
  //         data: res, posterUrl: posterUrl, id: "");
  //     final newPoster = await FirebaseCloud.instance.createPoster(poster);
  //     Navigator.pop(context, newPoster);
  //   } catch (e) {
  //     context.snackBar(e.toError());
  //   } finally {
  //     if (mounted) {
  //       setState(() {
  //         //    isCreatingPoster = false;
  //       });
  //     }
  //   }
  // }

  void generatePreviewFromModel() async {
    try {
      setState(() {
        error = false;
        generatingImage = true;
      });
      final profile = await FirebaseCloud.instance.getDashboardData();
      if (profile.profile.profileUrl == null) {
        final snackBar = SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
          content: const Text('Upload Profile Picture First'),
        );
        ScaffoldMessenger.of(context).showSnackBar(snackBar);
      }

      // final profileImg =
      //     await PosterUtils.getCacheImagePath(profile.profile.profileUrl!);
      // final ovr = await PosterUtils.overlapPoster(
      //     posterOverlayData!, profileImg, profile.profile.name);
      //  //print("OVR DATA AAYA");
      //  //print(ovr);

      // if (ovr != null) {
      //   generatingImage = false;
      //   setState(() {
      //     imagePath = ovr;
      //   });
      // } else {
      //   error = true;
      //   final snackBar = SnackBar(
      //     behavior: SnackBarBehavior.floating,
      //     margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
      //     content: const Text('Something went wrong, try again'),
      //   );
      //   ScaffoldMessenger.of(context).showSnackBar(snackBar);
      // }
    } catch (e) {
      // error = e.toError();
    } finally {
      setState(() {});
    }
  }

  slectImageCrafto() async {
    try {
      final file = await ImageUtil.pickImageFromFile();
      final path = file?.path;

      if (path == null) throw Exception("Image path not found");
      posterPath = path;
      CroppedFile? croppedFile = await ImageCropper().cropImage(
        sourcePath: posterPath!,
        aspectRatio: CropAspectRatio(ratioX: 4, ratioY: 3),
        uiSettings: [
          AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              toolbarColor: Colors.white,
              toolbarWidgetColor: Colors.black,
              // initAspectRatio: CropAspectRatioPreset.ratio3x2,
              lockAspectRatio: false),
          IOSUiSettings(
            title: 'Crop Image',
          ),
          WebUiSettings(
            context: context,
          ),
        ],
      );
      if (croppedFile != null) {
         //print("file name is ");
         //print(croppedFile.path);
//resize Image
        var image = im.decodeImage(File(croppedFile.path).readAsBytesSync());
        var thumbnail = im.copyResize(image!, width: 2000, height: 1500);
        //new code
        // var _newImage = im.Image(2000, 1500 + 300);
        // im.fill(_newImage, im.getColor(255, 255, 255));
        // thumbnail = im.copyInto(_newImage, thumbnail, dstX: 0, dstY: 0);

        //----
        final tempDir = await getTemporaryDirectory();
        final imageFile = File(tempDir.path + "/" + Uuid().v1() + ".jpg");
        FileImage(imageFile).evict();

        ///

        await imageFile.writeAsBytes(im.encodeJpg(thumbnail));
        setState(() {
          imagePath = imageFile.path;
        });
         //print("EK BNAAR AURE");
        try {
          File imageFile = File(imagePath!);
          int fileSize = await imageFile.length();
           //print("File size: $fileSize bytes");
        } catch (e) {
           //print("Error: $e");
        }
         //print(imagePath);
      }
    } catch (e) {
       //print(e);
      context.snackBar(e.toError());
    }
  }

  selectImage() async {
    try {
      final file = await ImageUtil.pickImageFromFile();
      final path = file?.path;

      if (path == null) throw Exception("Image path not found");
      posterPath = path;
      final res = await context
          .pushWidget((context) => CreatePosterPage(posterPath: path));
      if (res is PosterOverlayData) {
        posterOverlayData = res;

        generatePreviewFromModel();
      }
    } catch (e) {
      final snackBar = SnackBar(
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
        content: Text(e.toString()),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Color(0xff4C3AB4),
            title: Text("Create Poster", style: TextStyle(color: Colors.white),),
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: Icon(Icons.arrow_back),
              color: Colors.white,
            )),
        body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.all(10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Poster Details and Preview",
                      style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Column(
                      children: [
                     imagePath == null
                          ? Container()
                          : ClipRRect(
                              child: Image.file(
                                File(imagePath!),
                                // height:
                                //     MediaQuery.of(context).size.width * 0.95,
                                // width: MediaQuery.of(context).size.width * 0.95,
                                fit: BoxFit.contain,
                              ),
                            ),
                      SizedBox(
                        height: 20,
                      ),
                      imagePath == null
                          ? Container()
                          : Center(
                              child: ElevatedButton(
                                  style: ButtonStyle(
                                    shape: WidgetStateProperty.all(
                                      RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    
                                    backgroundColor:
                                     WidgetStateProperty.all<Color>(Colors.black),
                                  ),
                                  onPressed: () async {
                                    slectImageCrafto();
                                    // selectImage();
                                  },
                                  child: Text(
                                    "Change Media",
                                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                  ))),
                    ]),
                    imagePath == null
                        ? generatingImage
                            ? PI1()
                            : Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)
                                ),
                                height: 200,
                                width: MediaQuery.of(context).size.width,
                                child: DottedBorder(
                                  dashPattern: [9,5],
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            IconButton(
                                                onPressed: () async {
                                                  slectImageCrafto();
                                                  // selectImage();
                                                },
                                                icon: ImageIcon(
                                                  AssetImage(
                                                      "assets/don.png"),
                                                  size: 20,
                                                  color: Colors.black,
                                                )),
                                                SizedBox(height: 10,),
                                            Text(
                                              "Select Photo",
                                              style:
                                                   TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.black, fontSize: 20),
                                            )
                                          ]),
                                    ],
                                  ),
                                ),
                              )
                        : Container(),
                    SizedBox(
                      height:100,
                    ),
                    Stack(
                      children: [
                        Padding(
                            padding: EdgeInsets.all(0),
                            child: Container(
                                // elevation: 3,
                                decoration: BoxDecoration(border: Border.all(color: Color(0xffE8E8E8)), borderRadius: BorderRadius.circular(5)),
                                child: TextFormField(
                                  
                                  controller: captionController,
                                  decoration: InputDecoration(
                                    hintText: 'Enter Caption',
                                    contentPadding: EdgeInsets.all(20.0),
                                  ),
                                  minLines:
                                      10, // any number you need (It works as the rows for the textarea)
                                  keyboardType: TextInputType.multiline,
                                  maxLines: null,
                                ))),
                                Positioned(
                        left: MediaQuery.of(context).size.height*0.03,
                        top: MediaQuery.of(context).size.height*0,
                        child: Container(
                         padding: EdgeInsets.only(left: 5 ,right: 5),
                          color: Colors.white,
                     child: Text("Enter Caption" , style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold , fontSize: 14  ),),
                        ),
                      )
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: ElevatedButton(
                          style: ButtonStyle(
                            shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                            minimumSize: MaterialStateProperty.all(
                                const Size(300, 45)),
                            backgroundColor:
                                MaterialStateProperty.all(Color(0xff4C3AB4)),
                          ),
                          onPressed: () async {
                            if (imagePath == null) {
                              final snackBar = SnackBar(
                                behavior: SnackBarBehavior.floating,
                                margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
                                content: const Text('Select any image'),
                              );
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);
                            } else {
                              final snackBar = SnackBar(
                                content: const Text('Posting...'),
                              );

                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);

                              var result = await login.makeCreateRequest(
                                  context,
                                  imagePath!,
                                  captionController.text);
                              if (result['status'] == 200) {
                                Navigator.pushReplacement(context,
                                    MaterialPageRoute(
                                        builder: (BuildContext context) {
                                  return PosterAdminPage();
                                }));
                              } else if (result["status"] == 403) {
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return SessionExpiredDialog();
                                  },
                                );
                              } else {
                                context.snackBar("something went wrong");
                              }
                               //print("result in cretae screen $result");
                               

                            }
                          },
                          child: Text(
                            "Post",
                            style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 16,color: Colors.white,),
                          )),
                    ),
                    SizedBox(
                      height: 30,
                    )
                  ],
                ))));
  }

  void compressImage(String inputPath, String outputPath, int targetFileSize) {
    File inputFile = File(inputPath);
    File outputFile = File(outputPath);

    // Read the image file
    List<int> imageBytes = inputFile.readAsBytesSync();
    Uint8List uint8List = Uint8List.fromList(imageBytes);

    // Decode the image
    im.Image? image = im.decodeImage(uint8List);

    // Set the maximum width and height for compression
    int maxWidth = 800;
    int maxHeight = 800;

    // Resize the image if it exceeds the maximum dimensions
    if (image!.width > maxWidth || image.height > maxHeight) {
      image = im.copyResize(image, width: maxWidth, height: maxHeight);
    }

    // Adjust the compression quality dynamically to meet the target file size
    int quality = 85;
    while (outputFile.lengthSync() > targetFileSize) {
      if (quality <= 0) {
         //print("Unable to meet target file size. Consider reducing dimensions.");
        break;
      }

      // Reduce quality and re-encode the image
      quality -= 5;
      List<int> compressedBytes = im.encodeJpg(image, quality: quality);
      outputFile.writeAsBytesSync(compressedBytes);
    }

     //print("Final quality used: $quality");
  }
}
