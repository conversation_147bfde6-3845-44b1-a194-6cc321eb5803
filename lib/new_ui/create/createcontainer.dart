import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class   CreateContainer
 extends StatelessWidget {
  final void Function()? ontap ;
  final String imageUrl ;
  final String labeltext ;
  const   CreateContainer
  ({Key? key ,required this.ontap ,required this.imageUrl ,required this.labeltext}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  InkWell(
          onTap: ontap   
           ,
          child: Container(
            margin: EdgeInsets.only(left: 20, right: 20),
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
            decoration: BoxDecoration(
              color: Color(0xff4C3AB4),
              borderRadius: BorderRadius.circular(10.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 2,
                  offset: Offset(0, 1.5),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
              ImageIcon(
                  AssetImage(imageUrl),
                  color: Colors.white,
                ),
                SizedBox(width: 8.0),
                Text(
                  labeltext,
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 16,
                    color: Colors.white,
                    letterSpacing: 2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                  child: Icon(
                    Icons.arrow_forward,
                    size:
                        20, // This is a Material icon, adjust if you have a custom icon
                    color: Color(0xff4C3AB4), // Icon color
                  ),
                ),
              ],
            ),
          ),
        );
 
  }
}
 
 class   FestivalContainer
 extends StatelessWidget {
  final void Function()? ontap ;
  
  final String labeltext ;
  const   FestivalContainer
  ({Key? key ,required this.ontap   ,required this.labeltext}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  InkWell(
          onTap: ontap   
           ,
          child: Container(
            margin: EdgeInsets.only(left: 20, right: 20),
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 30.0),
            decoration: BoxDecoration(
              color: Color(0xff4C3AB4),
              borderRadius: BorderRadius.circular(10.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 2,
                  offset: Offset(0, 1.5),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
               
                SizedBox(width: 8.0),
                Text(
                  labeltext,
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 16,
                    color: Colors.white,
                    letterSpacing: 2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                   
              ],
            ),
          ),
        );
 
  }
}
 
 class   FestivalCon 
 extends StatelessWidget {
  final void Function()? ontap ;
  
  final String labeltext ;
  const   FestivalCon 
  ({Key? key ,required this.ontap   ,required this.labeltext}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  InkWell(
          onTap: ontap   
           ,
          child: Container(
            margin: EdgeInsets.only(left: 20, right: 20),
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 10.0),
            decoration: BoxDecoration(
              color: Color(0xff4C3AB4),
              borderRadius: BorderRadius.circular(10.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 2,
                  offset: Offset(0, 1.5),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
               
                SizedBox(width: 8.0),
                Text(
                  labeltext,
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 16,
                    color: Colors.white,
                    letterSpacing: 2,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                  Spacer()  ,
                  Icon(Icons.arrow_forward ,color: Colors.white,)
              ],
            ),
          ),
        );
 
  }
}
 