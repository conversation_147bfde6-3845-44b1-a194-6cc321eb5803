import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/new_ui/create/createOptions.dart';
import 'package:mla_connect/new_ui/create/createPoster.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/custom_textfield.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
 

class ApproveKaryakartaRequest extends StatefulWidget {
  const ApproveKaryakartaRequest({Key? key}) : super(key: key);

  @override
  State<ApproveKaryakartaRequest> createState() =>
      _ApproveKaryakartaRequestState();
}

class _ApproveKaryakartaRequestState extends State<ApproveKaryakartaRequest> {
  LoginService login = LoginService();
List<TextEditingController> boothController= [];
  List<TextEditingController> BlockController= [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchKaryakartaList();
  }
bool isload =false ;
  var karyakarta = {};
  final _formkey = GlobalKey<FormState>();
  fetchKaryakartaList() async {
    var res = await login.getKaryaKartaRequest(context);
     
    karyakarta = res['res'];
       setState(() {});
    for (int i = 0; i <= karyakarta['appliedKks'].length ; i++) {
      boothController.add(TextEditingController());
      BlockController.add(TextEditingController());
    }

   //print(karyakarta);
    setState(() {});
  
  }

  var karyakartaListData;

   

  
    List<String> _items = [ "ASSEMBLY", "BOOTH", "BLOCK"  ] ;
 String _selectedItem="ASSEMBLY";
  @override
  void dispose() {
    // Dispose all controllers when the widget is disposed
    for (var controller in boothController) {
      controller.dispose();
    }
    for (var controller in BlockController) {
      controller.dispose();
    }
    super.dispose();
  }
  int clickedIndex = -1;
  @override
  Widget build(BuildContext context) {
    fetchKaryakartaList();
   
    return   SingleChildScrollView(
      child: Column(
          children: [
             SizedBox(
              height: 20,
            ),
            karyakarta['appliedKks'] != null
                ? Container(
      height: (MediaQuery.of(context).size.height*0.8)*karyakarta['appliedKks'].length,
      child: ListView.builder(
        physics: NeverScrollableScrollPhysics(),
        itemCount: karyakarta['appliedKks'].length,
        itemBuilder: (context, index) {
          var itemsKK = karyakarta['appliedKks'];

      
          return ListTile(
            title: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                 color: Colors.white,
                border: Border.all(
                  color: Color.fromARGB(255, 218, 217, 217), // Border color
                  width: 1.0, // Border width
                ),
                borderRadius:
                    BorderRadius.circular(5), // Optional: border radius
              ),
              padding:EdgeInsets.symmetric(vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                 padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Text('Column Name: ${itemsKK[index]['name'].toString()}' ,style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 20),),
                  ),
                  SizedBox(
                    height: 3,
                  ),
                  Padding(
                   padding:EdgeInsets.symmetric(horizontal: 20),
                    child: Text('Number: ${itemsKK[index]['number']}' ,style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 20),),
                  ),
                  SizedBox(
                    height: 3,
                  ),
                  Padding(
                   padding:EdgeInsets.symmetric(horizontal: 20),
                    child: Text('Epic ID: ${itemsKK[index]['epicNumber']}' ,style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 20),),
                  ),
                  SizedBox(
                    height: 3,
                  ),
                  Padding(
                    padding:EdgeInsets.symmetric(horizontal: 20) ,
                    child: Text('Refrence Name: ${itemsKK[index]['referenceName']}' ,style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 20),),
                  ),
                  SizedBox(
                    height: 10,
                  ),

                   CustomTextFormField(hint: 'e.g. 2' ,label: 'BOOTH NUMBER',       validator: (value) {
                        if (value!.isEmpty) {
                          return "boothNumber is required";
                        }
                      }, controller:  boothController[index] , ) ,
                  // SizedBox(
                  //   height: MediaQuery.of(context).size.height * 0.07,
                  //   child: TextFormField(
                  //     controller: boothController[index],
                  //     validator: (value) {
                  //       if (value!.isEmpty) {
                  //         return "boothNumber is required";
                  //       }
                  //     },
                  //     // controller: '₹100',
                  //     decoration: InputDecoration(
                  //       labelText: "boothNumber",
                  //       labelStyle:  TextStyle(fontFamily: 'Gilroy',
                  //           color: Colors.black,
                  //           fontSize: 12,
                  //           fontWeight: FontWeight
                  //               .bold // Set label text color to black
                  //           ),
                  //       filled: true,
                  //       enabled: true,
                  //       fillColor: Colors.white,
                  //       enabledBorder: OutlineInputBorder(
                  //         borderRadius: BorderRadius.circular(10.0),
                  //         borderSide: BorderSide(
                  //             color: Color(0xffE8E8E8)), // Black border color
                  //       ),
                  //       focusedBorder: OutlineInputBorder(
                  //         borderRadius: BorderRadius.circular(10.0),
                  //         borderSide: BorderSide(
                  //             color: Color(
                  //                 0xffE8E8E8)), // Black border color when the field is focused
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  SizedBox(
                    height: 10,
                  ),
                   CustomTextFormField(hint: 'e.g. 2' ,label: 'BLOCK NUMBER',     validator: (value) {
                        if (value!.isEmpty) {
                          return "block Number is required";
                        }
                      }, controller: BlockController[index] , ) ,
                  // SizedBox(
                  //   height: MediaQuery.of(context).size.height * 0.07,
                  //   child: TextFormField(
                  //     controller: BlockController[index],
                  //     // controller: '₹100',
                  //     validator: (value) {
                  //       if (value!.isEmpty) {
                  //         return "blockNumber is required";
                  //       }
                  //     },
                  //     decoration: InputDecoration(
                  //       labelText: "blockNumber",
                  //       labelStyle:  TextStyle(fontFamily: 'Gilroy',
                  //           color: Colors.black,
                  //           fontSize: 12,
                  //           fontWeight: FontWeight
                  //               .bold // Set label text color to black
                  //           ),
                  //       filled: true,
                  //       enabled: true,
                  //       fillColor: Colors.white,
                  //       enabledBorder: OutlineInputBorder(
                  //         borderRadius: BorderRadius.circular(10.0),
                  //         borderSide: BorderSide(
                  //             color: Color(0xffE8E8E8)), // Black border color
                  //       ),
                  //       focusedBorder: OutlineInputBorder(
                  //         borderRadius: BorderRadius.circular(10.0),
                  //         borderSide: BorderSide(
                  //             color: Color(
                  //                 0xffE8E8E8)), // Black border color when the field is focused
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  SizedBox( height: 10,),
                   Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 20 ,right: 20 , top: 20),
                    width: MediaQuery.of(context).size.width,
                    alignment: Alignment.center,  decoration: BoxDecoration(
        border: Border.all(
          color: Color(0xFFE8E8E8),// Border color
        width: 1.0, // Border width
        ),
        borderRadius: BorderRadius.circular(5), // Optional: border radius
      ),
                    child: Row(
                      children: [
                        SizedBox(width: 10,),
                        SizedBox(
                          width: MediaQuery.of(context).size.width*0.65,
                          child:   DropdownButton<String>(
                 value: _selectedItem,
                isExpanded: true,
                underline: SizedBox(),
                iconDisabledColor: Colors.white,
                            icon: SvgPicture.asset('assets/icons/dropdown.svg') ,
                 items: _items .map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
                 onChanged: (String? value) { // Specify the type of value as String?
                 setState(() {
                   _selectedItem = value!;
                 });
                 },
               ),
                        ),
                          
                      ],
                    ),
                  ),
                   Positioned(
                        left: 25,
                        top: 13,
                        child: Container(
                         padding: EdgeInsets.only(left: 5 ,right: 5),
                          color: Colors.white,
                     child: Text("SELECT ONE" , style:  TextStyle(fontFamily: 'Gilroy-Bold',fontWeight: FontWeight.bold , fontSize: 14  ),),
                        ),
                      )
                ],
              ),
            //      Container(
            //   width: 200,
            //   padding: EdgeInsets.only(left: 10 ,right: 10),
            //   decoration: BoxDecoration( color: Colors.white, border: Border.all(color: Colors.grey ,) ,borderRadius: BorderRadius.circular(20)),
            //    child: DropdownButton<String>(
            //      value: _selectedItem,
            //     isExpanded: true,
            //     underline: SizedBox(),
            //      items: _items .map((String item) {
            //   return DropdownMenuItem<String>(
            //     value: item,
            //     child: Text(item),
            //   );
            // }).toList(),
            //      onChanged: (String? value) { // Specify the type of value as String?
            //      setState(() {
            //        _selectedItem = value!;
            //      });
            //      },
            //    ),
            //  ),
                  Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      
                      SizedBox(
                        height: MediaQuery.of(context).size.width*0.15,
                        width: MediaQuery.of(context).size.width*0.4,
                        child: ElevatedButton(
                          onPressed: () async {
                            var id = karyakarta['appliedKks'][index]['userId'];
                             //print(id);
                        var result=     await login.karyakartarejectRequest(context, false ,id);
                        if(result['status']  ==200){

                        }else if (result["status"] == 403){
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                          }else  {
                            context.snackBar("Something went wrong!");
                          }
                            // Implement rejection logic here
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xffAE3120),
                          ),
                          child: Text('Reject' , style: TextStyle(color: Colors.white),),
                        ),
                      ),
                      SizedBox(
                         height: MediaQuery.of(context).size.width*0.15,
                        width: MediaQuery.of(context).size.width*0.4,
                        child: ElevatedButton(
                          onPressed: () async {
    //                       setState(() {
    //                     clickedIndex = index; // Update the clicked index
    //                   });
    //                       if (clickedIndex == index) {
    //    //print('Text 1 for index $index: ${boothController [index].text}');
    //    //print('Text 2 for index $index: ${BlockController[index].text}');
    // }
                           if (boothController[index].text.isEmpty  ) {
      // If any text field is empty, show a Snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Booth number is required .'),
        ),
      );
    }
   if (BlockController[index].text.isEmpty  ) {
      // If any text field is empty, show a Snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Booth number is required .'),
        ),
      );
    }
     setState(() {
                              isload =true ;
                            });
                              var isSucess =
                                  await login.karyakartaApproveRequest(
                                      context,
                                      true,
                                      itemsKK[index]['number'],
                                      boothController [index].text,
                                      BlockController[index].text,
                                       itemsKK[index]['userId'] ,"BOOTH");
                              if (isSucess["status" ] == 200) {
                                setState(() {
                              isload =false ;
                            });
                                
                              } else if (isSucess["status"] == 403){
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                          } else {
                               ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Something went worng'),
        ),
        
      );
       setState(() {
                              isload =false ;
                            });
                              }
                            
                            setState(() {
                              isload =false ;
                            });
                            // Implement approval logic here
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xff299718),
                          ),
                          child: Text('Approve', style: TextStyle(color: Colors.white),),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    )
                : 
                // karyakarta['appliedKks'] != null? Center(child: CircularProgressIndicator()) :
                 Text("No Request")
          ],
        
      ),
    );
  }

  
  
}

 
