import 'dart:io';

import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/imagePickerUtil.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';

class CreateFacebookPostPage extends StatefulWidget {
  const CreateFacebookPostPage({Key? key}) : super(key: key);

  @override
  State<CreateFacebookPostPage> createState() => _CreateFacebookPostPageState();
}

class _CreateFacebookPostPageState extends State<CreateFacebookPostPage> {
  TextEditingController fbUrlController = new TextEditingController();
  final _fbForm = GlobalKey<FormState>();

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    // timer?.cancel;
  }

  bool posting = false;
  post() async {
    final snackBar = SnackBar(
      content: const Text('Posting...'),
    );
    setState(() {
      posting = true;
    });
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
    bool result;

    String url = await FirebaseCloud.instance
        .uploadFileToFirebaseStorage(File(imagePath!));
    result = await FirebaseCloud.instance
        .createPost('fbpost', text: fbUrlController.text, mediaUrl: url);

    if (result) {
      Navigator.pop(context);
    } else {
      setState(() {
        posting = false;
      });
      final snackBarFailed = SnackBar(
        content: const Text('Failed, Try Again'),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBarFailed);
    }
  }

  String? imagePath;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
            title: Text("Create Facebook Post"),
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context, true);
              },
              icon: Icon(Icons.arrow_back),
              color: Colors.black,
            )),
        body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.all(10),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text(
                      //   "Create Tweet",
                      //   style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                      // ),
                      SizedBox(
                        height: 20,
                      ),
                      Text(
                        "  Enter Facebook Post Link",
                        style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      imagePath != null
                          ? ClipRRect(
                              child: Image.file(
                                File(imagePath!),
                                fit: BoxFit.contain,
                              ),
                            )
                          : Container(
                              decoration: BoxDecoration(
                                color: Color.fromARGB(255, 181, 178, 178),
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(30),
                                    bottomRight: Radius.circular(30)),
                              ),
                              height: 200,
                              width: MediaQuery.of(context).size.width,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        IconButton(
                                            onPressed: () async {
                                              String? imageLocation =
                                                  await ImageUtil
                                                      .selectImageAndCrop();
                                              if (imageLocation != null) {
                                                setState(() {
                                                  imagePath = imageLocation;
                                                });
                                              }
                                            },
                                            icon: ImageIcon(
                                              AssetImage(
                                                  "assets/icons/Camera.png"),
                                              size: 50,
                                              color: Colors.white,
                                            )),
                                        Text(
                                          "Select Screenshot",
                                          style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
                                        )
                                      ]),
                                ],
                              ),
                            ),
                      imagePath == null
                          ? Container()
                          : Center(
                              child: ElevatedButton(
                                  style: ButtonStyle(
                                    shape: MaterialStateProperty.all(
                                      RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    minimumSize: MaterialStateProperty.all(
                                        const Size(300, 45)),
                                    backgroundColor:
                                        MaterialStateProperty.all(Colors.black),
                                  ),
                                  onPressed: () async {
                                    String? imageLocation =
                                        await ImageUtil.selectImageAndCrop();
                                    if (imageLocation != null) {
                                      setState(() {
                                        imagePath = imageLocation;
                                      });
                                    }
                                  },
                                  child: Text(
                                    "Change Media",
                                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                  ))),
                      SizedBox(
                        height: 20,
                      ),
                      Card(
                          elevation: 3,
                          child: Form(
                              key: _fbForm, //assigning key to form

                              child: TextFormField(
                                validator: (value) {
                                  RegExp exp = RegExp(
                                      r'https?://(www\.)?facebook.com/[^\s]+',
                                      caseSensitive: false,
                                      multiLine: false);
                                  Match? match = exp.firstMatch(value ?? "");
                                  if (match != null) {
                                    return null;
                                  } else {
                                    return "Wrong Facebook Link";
                                  }
                                },
                                controller: fbUrlController,
                                decoration: InputDecoration(
                                    hintText: 'Facebook Post URL',
                                    contentPadding: EdgeInsets.all(20.0),
                                    errorBorder: UnderlineInputBorder(
                                        borderSide:
                                            BorderSide(color: Colors.white))),
                                // any number you need (It works as the rows for the textarea)
                                keyboardType: TextInputType.multiline,
                                maxLines: null,
                              ))),
                      SizedBox(
                        height: 20,
                      ),
                      // tweetId != null
                      //     ? loadTweet
                      //         ? Column(
                      //             mainAxisAlignment: MainAxisAlignment.start,
                      //             crossAxisAlignment: CrossAxisAlignment.start,
                      //             children: [
                      //               Text(
                      //                 "Your Tweet",
                      //                 style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                      //               ),
                      //               TwitterEmbed(
                      //                 link: tweetId!,
                      //                 callback: tweetLoaded,
                      //               )
                      //             ],
                      //           )
                      //         : Container()
                      //     : Container(),
                      Center(
                        child:
                            // tweetId != null
                            // ? uploadingPostLoader
                            // ? PI1()
                            // :
                            ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  minimumSize: MaterialStateProperty.all(
                                      const Size(300, 45)),
                                  backgroundColor:
                                      MaterialStateProperty.all(Colors.black),
                                ),
                                onPressed: () {
                                   //print("clicked");
                                   //print(fbUrlController.text);
                                  if (_fbForm.currentState!.validate()) {
                                    if (imagePath == null) {
                                      final snackBar = SnackBar(
                                        behavior: SnackBarBehavior.floating,
                                        margin:
                                            EdgeInsets.fromLTRB(15, 5, 15, 30),
                                        content:
                                            const Text('Select Screenshot'),
                                      );
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(snackBar);
                                    } else {
                                      post();
                                    }
                                  }
                                },
                                child: posting
                                    ? PI1()
                                    : Text(
                                        "Post",
                                        style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                      )),
                        // : ElevatedButton(
                        //     style: ButtonStyle(
                        //       shape: MaterialStateProperty.all(
                        //         RoundedRectangleBorder(
                        //           borderRadius: BorderRadius.circular(10),
                        //         ),
                        //       ),
                        //       minimumSize: MaterialStateProperty.all(
                        //           const Size(300, 45)),
                        //       backgroundColor:
                        //           MaterialStateProperty.all(Colors.black),
                        //     ),
                        //     onPressed: () {
                        //       setState(() {
                        //         loadTweet = true;
                        //       });
                        //       _tweetForm.currentState!.validate();
                        //       getTweetId(tweetUrlController.text);
                        //     },
                        //     child: Text(
                        //       "Get Tweet",
                        //       style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                        //     ))
                      ),
                      SizedBox(
                        height: 30,
                      )
                    ]))));
  }
}
