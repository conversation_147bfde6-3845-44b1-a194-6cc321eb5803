import 'package:mla_connect/controller/partyname_controller.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/custom_textfield.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/instance_manager.dart';

class CreatePartyList extends StatefulWidget {
  const CreatePartyList({Key? key}) : super(key: key);

  @override
  State<CreatePartyList> createState() => _CreatePartyListState();
}

class _CreatePartyListState extends State<CreatePartyList> {
  PartyName partyName= Get.find() ;
  final TextEditingController _partyNameController = TextEditingController();
  List<String> _partyNames = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchHomeData();
  }

  void _addPartyNameToList() {
    setState(() {
      String partyName = _partyNameController.text;
      if (partyName.isNotEmpty) {
        _partyNames.add(partyName);
        _partyNameController.clear();
      }
    });
  } 
  LoginService login =LoginService() ; 
  var config ;
fetchHomeData() async {
   config =  await login.fetchConfig(context);
   setState(() {  });}
  @override
  Widget build(BuildContext context) {
  fetchHomeData() ;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xff4C3AB4) ,
      ),
      body: SingleChildScrollView(
        child: Column(
          
         
          children: [
       
      config==null ?CircularProgressIndicator() :  Container( 
              
              padding: EdgeInsets.all(10),
              margin:EdgeInsets.all(20) ,
               decoration:  BoxDecoration(  color: Color(0xffEBEBEB),  border: Border.all(color: Color(0xffEBEBEB))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Populated Party Name List :  ", style:  TextStyle(fontFamily: 'Gilroy-Bold',fontWeight: FontWeight.bold ,fontSize: 20), ),
                 SizedBox(height: 10,) , SizedBox(
                    height: (MediaQuery.of(context).size.height*0.027) *  config['partyList'].length,
                   child: ListView.builder(
                               
                               itemCount:config['partyList'].length,
                               itemBuilder: (context, index) {
                                 return   SizedBox(child: Text(" ${index+1} : "+ config['partyList'][index]  , style:  TextStyle(fontFamily: 'Gilroy-Bold',fontWeight: FontWeight.bold ,fontSize: 15),)) ;
                                 
                               },
                             ),
                 ),
                ],
              )) ,
              SizedBox( height: 20,) ,
              Text("Create New Party Name List :  ", style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold ,fontSize: 20), ),
              SizedBox(height: 10,),
              Text(  _partyNames.toString()  , style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold ,fontSize: 18),) ,
              SizedBox(height: 10,),
              CustomTextFormField(hint: "Enter party name",label: 'CREATE PARTY NAME',controller: _partyNameController,),
            // Container(
            //   height:MediaQuery.of(context).size.height*0.1,
            //   padding: EdgeInsets.all(20),
            //   child: ListView.builder(
                
            //     itemCount: _partyNames.length,
            //     itemBuilder: (context, index) {
            //       return   Text( '['+_partyNames.toString()+','+']' , style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold ,fontSize: 15),) ;
                  
            //     },
            //   ),
            // ),
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 16),
            //   child: TextField(
            //     controller: _partyNameController,
            //     decoration: InputDecoration(
            //       labelText: 'Enter Party Name',
            //       border: OutlineInputBorder(),
            //     ),
            //   ),
            // ),
            SizedBox(height: 20,),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: SizedBox(
                height: MediaQuery.of(context).size.height*0.06,
                width:  MediaQuery.of(context).size.width ,
                child: ElevatedButton(
                   style: ButtonStyle(
                            backgroundColor: MaterialStateProperty.all<Color>(
                       Color(0xff4C3AB4),
                        )),
                  onPressed: _addPartyNameToList,
                  child: Text('Add Party Name to List', style:  TextStyle(fontFamily: 'Gilroy-Bold',fontWeight: FontWeight.bold ,fontSize: 16),),
                ),
              ),
            ),
            SizedBox(height: 20,),
            Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
              child: SizedBox(
                 height: MediaQuery.of(context).size.height*0.06,
                  width:  MediaQuery.of(context).size.width ,
                child: ElevatedButton(
                   style: ElevatedButton.styleFrom(
                  side: BorderSide(  color: Color(0xff4C3AB4)), // Custom border
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5), // Rounded corners
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                       backgroundColor:  Color.fromARGB(255, 233, 233, 233)
                ),
                  //  ButtonStyle(
                  //           backgroundColor: MaterialStateProperty.all<Color>(
                  //      Color(0xff4C3AB4).withOpacity(0.3),
                       
                  //       )),
                  onPressed:  ()async {
                 var result =    await  login.postPartyList(context , _partyNames) ;
                 if(result['status'] ==200) {
                  Navigator.pop(context) ;
                 }else if (result['status'] ==403) {
  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return SessionExpiredDialog();
                    },
                  );
 }else {
  context.snackBar("Something went wrong!" ) ;
 }
                  },
                  child: Text('Confrim Party List', style:  TextStyle(fontFamily: 'Gilroy-Bold',fontWeight: FontWeight.bold ,fontSize: 16 , color: Color(0xff4C3AB4)),),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _partyNameController.dispose();
    super.dispose();
  }
}