import 'package:mla_connect/controller/downloadingDataController.dart';
import 'package:mla_connect/new_ui/create/create_partylist.dart';
 
 
import 'package:mla_connect/screens/poster/poster_admin_page.dart';
 
import 'package:flutter/material.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:get/get.dart';

 
import '../votermessaging.dart/votermessage.dart';
import 'createcontainer.dart';
 
 

class CreateOptionsList extends StatefulWidget {
  const CreateOptionsList({Key? key}) : super(key: key);

  @override
  State<CreateOptionsList> createState() => _CreateOptionsListState();
}

class _CreateOptionsListState extends State<CreateOptionsList> {
  

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(backgroundColor:  Color(0xff4C3AB4) , // Change the color here
   title: Text("CREATE",style:  TextStyle(fontFamily: '<PERSON>roy',fontSize: 18, color: Colors.white),),centerTitle: true,elevation: 0,),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [ 
            
          SizedBox(
            height: 30,
          ),
           CreateContainer(ontap: () => context.pushWidget((context) => PosterAdminPage()),imageUrl:"assets/icons/ic_round-post-add.png" ,labeltext: "Create Poster") ,
         SizedBox(
            height: 30,
          ),
                   CreateContainer(ontap: () => context.pushWidget((context) => CreatePartyList()),imageUrl:"assets/icons/ic_round-post-add.png" ,labeltext: "Create Party List") ,
  // SizedBox(
  //           height: 30,
  //         ),
  //         CreateContainer(ontap:  () => context.pushWidget((context) => VoterMessage()
  //         //  RoomListPage()
  //         ),imageUrl:"assets/icons/tabler_message.png" ,labeltext: "Voter Messaging") ,
     
        ],
      ),
    );
  }
}
