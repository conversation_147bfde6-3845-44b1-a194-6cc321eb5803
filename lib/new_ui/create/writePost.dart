import 'dart:convert';

import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class WritePost extends StatefulWidget {
  const WritePost({Key? key}) : super(key: key);

  @override
  State<WritePost> createState() => _WritePostState();
}

class _WritePostState extends State<WritePost> {
  bool uploadingPostLoader = false;
  final _writePostForm = GlobalKey<FormState>(); //for storing form state.
  TextEditingController titleController = new TextEditingController();
  TextEditingController descriptionController = new TextEditingController();

  void createPost() async {
    if (_writePostForm.currentState!.validate()) {
      if (titleController.text.isNotEmpty &&
          descriptionController.text.isNotEmpty) {
        final snackBar = SnackBar(
          content: const Text('Posting...'),
        );
        setState(() {
          uploadingPostLoader = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(snackBar);

        Map<String, String> json = {
          'title': titleController.text,
          'description': descriptionController.text
        };
        bool result = await FirebaseCloud.instance
            .createPost("text", text: jsonEncode(json));
        if (result) {
          Navigator.pop(context);
        } else {
          final snackBarFailed = SnackBar(
            content: const Text('Failed, Try Again'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBarFailed);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
            title: Text("Write Post"),
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context, true);
              },
              icon: Icon(Icons.arrow_back),
              color: Colors.black,
            )),
        body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.all(10),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 20,
                      ),
                      Text(
                        "  Post Title",
                        style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Form(
                          key: _writePostForm,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Card(
                                  elevation: 3,

                                  // key: _tweetForm, //assigning key to form

                                  child: TextFormField(
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return "Enter Title";
                                      }
                                    },
                                    controller: titleController,
                                    decoration: InputDecoration(
                                      hintText: 'Post Title',
                                      errorBorder: UnderlineInputBorder(
                                          borderSide:
                                              BorderSide(color: Colors.white)),
                                      contentPadding: EdgeInsets.all(20.0),
                                    ),
                                    // any number you need (It works as the rows for the textarea)
                                    keyboardType: TextInputType.multiline,
                                    maxLines: null,
                                  )),
                              SizedBox(
                                height: 50,
                              ),
                              Text(
                                "  Post Description",
                                style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              Card(
                                  elevation: 3,

                                  // key: _tweetForm, //assigning key to form

                                  child: TextFormField(
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return "Enter Description";
                                      }
                                    },
                                    controller: descriptionController,

                                    decoration: InputDecoration(
                                      errorStyle:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                      errorBorder: UnderlineInputBorder(
                                          borderSide:
                                              BorderSide(color: Colors.white)),
                                      hintText: 'Post Description',
                                      contentPadding: EdgeInsets.all(20.0),
                                    ),
                                    // any number you need (It works as the rows for the textarea)
                                    keyboardType: TextInputType.multiline,
                                    maxLines: null,
                                    minLines: 8,
                                  ))
                            ],
                          )),
                      SizedBox(height: 40),
                      Center(
                          child: uploadingPostLoader
                              ? CircularProgressIndicator()
                              : ElevatedButton(
                                  style: ButtonStyle(
                                    shape: MaterialStateProperty.all(
                                      RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    minimumSize: MaterialStateProperty.all(
                                        const Size(300, 45)),
                                    backgroundColor:
                                        MaterialStateProperty.all(Colors.black),
                                  ),
                                  onPressed: createPost,
                                  child: Text(
                                    "Post",
                                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
                                  ))),
                    ]))));
  }
}
