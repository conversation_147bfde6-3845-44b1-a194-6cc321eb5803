import 'package:mla_connect/new_ui/home/<USER>';
import 'package:mla_connect/new_ui/onboarding/loginbypass/loginpage.dart';
import 'package:mla_connect/new_ui/onboarding/onBoardinghHeader.dart';

import 'package:mla_connect/utils/colorConstants.dart';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../controller/service/login.dart';
import '../../screens/onBoarding_page.dart';

import 'package:sign_in_button/sign_in_button.dart';

class PhoneInput extends StatefulWidget {
  const PhoneInput({Key? key}) : super(key: key);

  @override
  State<PhoneInput> createState() => _PhoneInputState();
}

class _PhoneInputState extends State<PhoneInput> {
  LoginService loginService = LoginService();
  bool tAndCChecked = false;

  bool isLoading = false;

  late TextEditingController _phoneController;
  bool _phoneValidate = false;
  bool validate() {
    bool valid = true;
    Future<void>? _launched;

    if (_phoneController.text.length != 10) {
      _phoneValidate = true;
      valid = false;
    } else {
      _phoneValidate = false;
    }

    setState(() {});
    return valid;
  }

  String token = '';

  @override
  void initState() {
    super.initState();
    _phoneController = TextEditingController();
    _initializeGoogleSignIn();
  }

  Future<bool> _onWillPop() async {
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final Uri toLaunch = Uri.parse(
        'https://docs.google.com/document/d/1c6hyikFEgJrwAW3BfX-Fpb9SShVL3dbBq_elRVkERjg/edit?pli=1');
    return Scaffold(
        body:   SingleChildScrollView(
          child: Stack(
              children: [
                Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
           SizedBox(
            height: MediaQuery.of(context).size.height*0.04,
          ),
          OnBoardingheader(),
          SizedBox(
            height: 20,
          ),
          SizedBox(
            height: 10,
          ),
          isLoading == true
                ? CircularProgressIndicator()
                : Padding(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    child: Container(
                      // margin: EdgeInsets.all(10),
                      width: double.infinity,
                      decoration: BoxDecoration(border: Border.all(
                        color: Color(0xff4C3AB4) ,
                      ) ,
                      borderRadius: BorderRadius.circular(5)),
                      child: SignInButton(
                        Buttons.google,
                         padding: EdgeInsets.all(5),

                        // elevation: 3,
                        text: "Continue with Google",
                        onPressed: () {
                          if (tAndCChecked) {
                            Fluttertoast.showToast(
                                msg: "Check Terms and Conditions");
                          } else {
                            setState(() {
                              isLoading = true;
                            });
                            goggleSignIn(context);
                            setState(() {
                              isLoading = false;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 20,),
                  // Padding(
                  //   padding: const EdgeInsets.only(left: 20, right: 20),
                  //   child: Container(
                  //     // margin: EdgeInsets.all(10),
                  //     width: double.infinity,
                  //     decoration: BoxDecoration(border: Border.all(
                  //       color: Color(0xff4C3AB4) ,
                  //     ) ,
                  //     borderRadius: BorderRadius.circular(5)),
                  //     child: SignInButton(
                  //       Buttons.values.first,
                  //        padding: EdgeInsets.all(5),

                  //       // elevation: 3,
                  //       text: "Continue with Id/Password",
                  //       onPressed: () {
                  //         if (tAndCChecked) {
                  //           Fluttertoast.showToast(
                  //               msg: "Check Terms and Conditions");
                  //         } else {
                  //           setState(() {
                  //             isLoading = true;
                  //           });
                  //            Navigator.push(context, MaterialPageRoute(builder:  (context) {
                  //     return  LoginPageByIdPass();
                  //   },));
                  //           // goggleSignIn(context);
                  //           setState(() {
                  //             isLoading = false;
                  //           });
                  //         }
                  //       },
                  //     ),
                  //   ),
                  // ),
          // Container(
          //       width: MediaQuery.of(context).size.width  ,
          //       child: Row(
          //         crossAxisAlignment: CrossAxisAlignment.center,
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         // mainAxisSize: MainAxisSize.min,
          //         children: [
          //           Checkbox(
          //               shape: CircleBorder(),
          //               activeColor: PRIMARY_COLOR_ORANGE,
          //               value: !tAndCChecked,
          //               onChanged: ((value) => {
          //                     setState(() {
          //                       tAndCChecked = !tAndCChecked;
          //                     })
          //                   })),
          //           RichText(
          //               text: TextSpan(
          //                   text: "By proceeding , I accept ",
          //                   style:  TextStyle(fontFamily: 'Gilroy-SemiBold',color: Colors.black ,fontSize: 13,  ),
          //                   children: [
          //                 TextSpan(
          //                     text: "T&C, Privacy Policy",
          //                     style:  TextStyle(fontFamily: 'Gilroy-SemiBold',color: PRIMARY_COLOR_ORANGE ,fontSize: 13),
          //                     recognizer: TapGestureRecognizer()
          //                       ..onTap = () async {
          //                         await _launchInBrowser(toLaunch);
          //                       })
          //               ])),
          //         ],
          //       )),
          SizedBox(
            height: MediaQuery.of(context).size.height*0.25,
          ),

              ],
            ),
             Positioned(
              right: MediaQuery.of(context).size.height*0.29,
              top: MediaQuery.of(context).size.height*0.8,
            bottom:  MediaQuery.of(context).size.height*0,

           child: Container(
             width: 170,
             height: 170.0,
             decoration: BoxDecoration(
            color: Color(0xFF4C3AB4).withOpacity(0.1), // Light Violet color
            shape: BoxShape.circle,
             ),
             ),
               ),
              ],
            ),
        ),
        );
  }

  Future<void> _launchInBrowser(Uri url) async {
    if (!await launchUrl(
      url,
      mode: LaunchMode.externalApplication,
    )) {
      throw Exception('Could not launch $url');
    }
  }

  // Platform-specific GoogleSignIn configuration
  late final GoogleSignIn _googleSignIn;

  void _initializeGoogleSignIn() {
    if (kIsWeb) {
      // For web, use the recommended configuration
      _googleSignIn = GoogleSignIn(
        clientId: "289800123153-gi2uniougcacrods6tj76b6nasib4e59.apps.googleusercontent.com",
        scopes: [
          'email',
          'profile',
          'openid',
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
        ],
      );
    } else {
      // For Android/iOS
      _googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'profile',
          'openid',
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
        ],
      );
    }
  }
  Future<void> _handleSignIn() async {
    try {
      await _googleSignIn.signIn();
    } catch (error) {
       //print(error);
    }
  }

  goggleSignIn(BuildContext context) async {
    try {
      print('Starting Google Sign-In process...');
      print('Platform: ${kIsWeb ? "Web" : "Mobile"}');

      setState(() {
        isLoading = true;
      });

      // Sign out any existing user to ensure fresh authentication
      await _googleSignIn.signOut();
      print('Signed out existing user');

      // For web, use signInSilently first as recommended
      GoogleSignInAccount? user;
      if (kIsWeb) {
        // Try silent sign-in first
        user = await _googleSignIn.signInSilently();
        if (user == null) {
          print('Silent sign-in failed, trying interactive sign-in');
          user = await _googleSignIn.signIn();
        }
      } else {
        user = await _googleSignIn.signIn();
      }
      
      print('Sign-in attempt completed');

      if (user == null) {
        // User cancelled the sign-in
        print('User cancelled sign-in');
        Fluttertoast.showToast(
          msg: "Sign-in cancelled",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
        setState(() {
          isLoading = false;
        });
        return;
      }

      print('User signed in: ${user.email}');
      print('User ID: ${user.id}');
      print('User Email: ${user.email}');
      print('Getting authentication details...');

      final GoogleSignInAuthentication googleSignInAuthentication =
          await user.authentication;
      final idToken = googleSignInAuthentication.idToken;
      final accessToken = googleSignInAuthentication.accessToken;

      print('ID Token: ${idToken != null ? "Present" : "NULL"}');
      print('Access Token: ${accessToken != null ? "Present" : "NULL"}');
      
      // If ID token is null but access token is present, we can use the access token for web
      final String tokenToUse = idToken ?? accessToken ?? '';
      if (tokenToUse.isEmpty) {
        throw Exception('Failed to get authentication token from Google Sign-In');
      }

      if (!mounted) return;

      bool result = await loginService.makeLoginRequest(
          tokenToUse, user.id, context);
      print("Login result: $result");
      
      if (result == true) {
        if (!mounted) return;

        final SharedPreferences prefs = await SharedPreferences.getInstance();
        var role = prefs.getString('ROLEUSERAPI');

        if (role == null) {
          context.pushAndRemoveWidget(
              (context) => OnBoarding(), (route) => false);
        } else {
          context.pushAndRemoveWidget(
              (context) => MyBottomBar(), (route) => false);
        }
      } else {
        if (!mounted) return;

        Fluttertoast.showToast(
            msg: "Error during Google Sign-In. Please try again.",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
      }
    } catch (error) {
      print('Google Sign-In Error: $error');

      if (!mounted) return;

      Fluttertoast.showToast(
          msg: "Error during Google Sign-In: ${error.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
