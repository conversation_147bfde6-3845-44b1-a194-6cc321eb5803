import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/new_ui/onboarding/custombutton.dart';
import 'package:mla_connect/new_ui/onboarding/customtextfield.dart';
import 'package:mla_connect/screens/onBoarding_page.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

 
 

class LoginPageByIdPass extends StatelessWidget {
  LoginPageByIdPass({Key? key}) : super(key: key);
  TextEditingController _emailTextEditingController = TextEditingController();
  TextEditingController _passwordTextEditingController = TextEditingController();
  
  LoginService loginService =LoginService() ;
 
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Log In',style: TextStyle(fontWeight: FontWeight.bold), ),
              SizedBox(height: 24,),
              CustomTextField(label: 'Enter Email', controller: _emailTextEditingController),
              SizedBox(height: 16),
              CustomTextField(label: 'Enter Password', controller: _passwordTextEditingController),
              SizedBox(height: 16),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                  child: CustomButton(
                      showIcon: false ,
                      leadingIcon: '',
                      isLoading: false,
                      isEnabled: true,
                      onPressed: () async{
        
                  },
                      buttonText: 'SUBMIT')
                        )
            ],
          ),
        )
    );
  }
  // Future<UserCredential?> signInWithPredefinedCredentials() async {
  //   try {
  //     UserCredential userCredential = await _auth.signInWithEmailAndPassword(
  //       email: _emailTextEditingController.text,
  //       password: _passwordTextEditingController.text,
  //     );
  //     return userCredential;
  //   } catch (e) {
  //      //print('Error signing in: $e');
       
  //   }
  // }
 
  }
