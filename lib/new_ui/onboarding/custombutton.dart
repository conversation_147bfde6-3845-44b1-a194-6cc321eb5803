import 'package:flutter/material.dart';

 
 

class CustomButton extends StatefulWidget {
  final String leadingIcon;
  final VoidCallback onPressed;
  final String buttonText;
  final bool showIcon;
  final bool isEnabled;
  final bool isLoading;

  const CustomButton({
    Key? key,
    required this.leadingIcon,
    required this.onPressed,
    required this.buttonText,
    required this.isLoading,
    this.isEnabled = false,
    this.showIcon = true,

  }) : super(key: key);

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      width: MediaQuery.of(context).size.width,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            elevation: 0,
            backgroundColor: this.widget.isEnabled ? Color.fromARGB(255, 239, 133, 126) : Colors.red,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(13)
            )
        ),
        onPressed: widget.onPressed,
        child: !(widget.isLoading) ? Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // if(widget.showIcon) SvgPicture.asset(widget.leadingIcon, width: 28, height: 28, color: Color.fromARGB(255, 239, 133, 126)),
            SizedBox(width: widget.showIcon ? 10 : 0),
            Text(
                widget.buttonText,
                style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 18,
                    
                    fontWeight: FontWeight.w800,
                    color: Colors.white
                )
            ),
          ],
        ) : CircularProgressIndicator(color: Colors.white,),
      ),
    );
  }
}
