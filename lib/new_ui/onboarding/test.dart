import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/new_ui/onboarding/custombutton.dart';
import 'package:mla_connect/new_ui/onboarding/customtextfield.dart';
import 'package:mla_connect/screens/onBoarding_page.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../home/<USER>';
 

class SignInTesting extends StatelessWidget {
  SignInTesting({Key? key}) : super(key: key);
  TextEditingController _emailTextEditingController = TextEditingController();
  TextEditingController _passwordTextEditingController = TextEditingController();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  LoginService loginService =LoginService() ;
 
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('TEST ACCOUNT', ),
              SizedBox(height: 24,),
              CustomTextField(label: 'Enter Email', controller: _emailTextEditingController),
              SizedBox(height: 16),
              CustomTextField(label: 'Enter Password', controller: _passwordTextEditingController),
              SizedBox(height: 16),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                  child: CustomButton(
                      showIcon: false ,
                      leadingIcon: '',
                      isLoading: false,
                      isEnabled: true,
                      onPressed: () async{
        var userCredential = await   loginService.testLoginRequest(_emailTextEditingController.text , _passwordTextEditingController.text , context);
                 //print(userCredential) ; 

                if(userCredential == true) {
 context.pushAndRemoveWidget(
          (context) => OnBoarding(),
          // LandingPage(),
          (route) => false);
                }else{
                  context.snackBar("Enter valid Id");
                }
                // if (userCredential != null) {
                //    //print('User signed in: ${userCredential.user?.email}');
                // } else {
                //    //print('Sign-in failed. Incorrect credentials.');
                // }
  
               
    //                      try {
    //   await FirebaseAuth.instance.signInWithEmailAndPassword(
    //       email: _emailTextEditingController.text.trim(),
    //       password:  _passwordTextEditingController.text
    //   );
    // } on FirebaseAuthException catch (e) {
    //   if (e.code == 'user-not-found') {
    //      //print('No user found for that email.');
    //   } else if (e.code == 'wrong-password') {
    //      //print('Wrong password provided for that user.');
    //   }
    // }
   
                    // await GoogleSignInProvider().autoLogInTestUser(_emailTextEditingController.text.trim(), _passwordTextEditingController.text);
                    // UserDatabase(userName: "Governance Matter", userEmail: "<EMAIL>").createUserDatabase();
                    // Navigator.push(
                    //     context,
                    //     MaterialPageRoute(builder: (BuildContext context) => OnboardingDetails(isTestUser: true,))
                    // );
                  },
                      buttonText: 'SUBMIT')
                        )
            ],
          ),
        )
    );
  }
  Future<UserCredential?> signInWithPredefinedCredentials() async {
    try {
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: _emailTextEditingController.text,
        password: _passwordTextEditingController.text,
      );
      return userCredential;
    } catch (e) {
       //print('Error signing in: $e');
       
    }
  }
 
  }
