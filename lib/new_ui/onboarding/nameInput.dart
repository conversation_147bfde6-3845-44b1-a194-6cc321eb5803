import 'package:mla_connect/controller/namecontroller.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/new_ui/home/<USER>';
import 'package:mla_connect/new_ui/onboarding/onBoardinghHeader.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';

class NameInput extends StatefulWidget {
  const NameInput({Key? key}) : super(key: key);

  @override
  State<NameInput> createState() => _NameInputState();
}

class _NameInputState extends State<NameInput> {
  bool _nameValidate = false;
  late TextEditingController _nameController;
  bool isLoading = false;
  NameController namecontroller = Get.put(NameController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _nameController = new TextEditingController();
  }

  bool validate() {
    bool valid = true;
    if ((!(_nameController.text.length > 3) &&
            _nameController.text.isNotEmpty) ||
        _nameController.text.isEmpty) {
      _nameValidate = true;
      valid = false;
    } else {
      _nameValidate = false;
    }

    setState(() {});
    return valid;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
            child:
                Column(mainAxisAlignment: MainAxisAlignment.start, children: [
      OnBoardingheader(),
      Container(
          width: MediaQuery.of(context).size.width * .8,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              " Name",
              style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16, fontWeight: FontWeight.normal),
            ),
          )),
      SizedBox(
        height: 3,
      ),
      Container(
          decoration: BoxDecoration(
            border: Border.all(),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          height: 50,
          width: MediaQuery.of(context).size.width * .8,
          child: Padding(
              padding: EdgeInsets.only(left: 15),
              child: TextField(
                controller: _nameController,
                style:  TextStyle(fontFamily: 'Gilroy',
                  fontSize: 18,
                ),
                decoration: InputDecoration(
                    // labelText: 'Enter Name',
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    disabledBorder: InputBorder.none),
              ))),
      _nameValidate
          ? Container(
              width: MediaQuery.of(context).size.width * .8,
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    " Enter a valid name",
                    style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
                  )))
          : Container(),
      SizedBox(
        height: 60,
      ),
      isLoading
          ? PI1()
          : ElevatedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                minimumSize: MaterialStateProperty.all(const Size(300, 45)),
                backgroundColor: MaterialStateProperty.all(Colors.black),
              ),
              onPressed: () async {
                setState(() {
                  namecontroller.name = _nameController.text;
                });
                if (validate()) {
                  DashboardResponse dp =
                      await FirebaseCloud.instance.getDashboardData();

                  final editProfile =
                      dp.profile.copyWith(name: _nameController.text);
 await FacebookAppEvents().logEvent(name: "Login");
                  // await FirebaseCloud.instance.updateSelfProfile(editProfile);
                  // await FirebaseCloud.instance
                  //     .getDashboardData(forceNetwork: true);
                  context.pushAndRemoveWidget(
                      (context) => MyBottomBar(showBottomCard: true),
                      (route) => false);
                }
              },
              child: Text(
                "Continue",
                style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),
              ))
    ])));
  }
}
