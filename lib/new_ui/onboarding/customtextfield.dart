import 'package:flutter/material.dart';
 

 
 

class CustomTextField extends StatefulWidget {
  final String label;
  final String hint;
  final TextEditingController? controller;
  final String? initialValue;
  final bool enabled;
  final bool obscureText;
  final TextInputAction textInputAction;
  final TextInputType textInputType;
  final FocusNode? focusNode;
  final void Function(String)? onChanged;

  CustomTextField({
    Key? key,
    required this.label,
    this.initialValue,
    this.controller,
    this.hint = '',
    this.enabled = true,
    this.obscureText = false,
    this.onChanged,
    this.focusNode,
    this.textInputAction = TextInputAction.done,
    this.textInputType = TextInputType.text
  }) : super(key: key);

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  FocusNode _myFocusNode = FocusNode();

  @override
  void initState() {
    _myFocusNode = widget.focusNode ?? FocusNode();
    _myFocusNode.attach(context);
    super.initState();

  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Color unselectedBorderColor = Colors.red;
    Color selectedBorderColor = Color.fromARGB(255, 239, 133, 126);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: TextFormField(
        onTap: _requestFocus,
        focusNode: _myFocusNode,
        controller: widget.controller,
        keyboardType: widget.textInputType,
        cursorColor: Color.fromARGB(255, 239, 133, 126),
        initialValue: widget.initialValue,
        cursorRadius: const Radius.circular(20),
        cursorWidth: 2.0,
        scrollPadding:
        EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 200),
        enabled: widget.enabled,
        onChanged: widget.onChanged,
        decoration: InputDecoration(
          hintText: widget.hint,
          errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(width: 2, color: Colors.red)),
          focusColor: Color.fromARGB(255, 239, 133, 126),
          focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(width: 1, color: selectedBorderColor)),
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(width: 1, color: unselectedBorderColor)),
          border: OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: Color.fromARGB(255, 239, 133, 126)),
              borderRadius: const BorderRadius.all(Radius.circular(15))),
          focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: Color.fromARGB(255, 239, 133, 126)),
              borderRadius: const BorderRadius.all(Radius.circular(15))),
          labelText: widget.label,
         
        ),
        onFieldSubmitted: (value){
          widget.controller?.text = value;
        },
      ),
    );
  }

  void _requestFocus() {
    setState(() {
      FocusScope.of(context).requestFocus(_myFocusNode);
    });
  }
}
