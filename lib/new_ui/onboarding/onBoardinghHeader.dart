import 'package:flutter_svg/svg.dart';
import 'package:mla_connect/new_ui/onboarding/test.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class OnBoardingheader extends StatelessWidget {
    OnBoardingheader({Key? key}) : super(key: key);
int count = 0;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
          onTap: () {
             count++;
                  if(count > 12){
                    Navigator.push(context, MaterialPageRoute(builder: (context) => SignInTesting()));
                  }
          },
          child: Column(
       crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
      children: [
          Center(
            child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 14),
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(15),color: Colors.yellow,border: Border.all(color: Colors.black)),
                    child:Image.asset('assets/MONIKA TAI.jpg')  
//                    SvgPicture.asset(
//   'assets/jaimage.svg',
//   height: MediaQuery.of(context).size.height *0.5 ,
//     width: MediaQuery.of(context).size.width*0.1,
//   // semanticsLabel: 'Acme Logo'
// ),
                  ),
          ) ,
        SizedBox(height: MediaQuery.of(context).size.height*0.04,),
          Padding(
            padding: const EdgeInsets.only(left: 20 ,top: 10) ,
            child: RichText(
                text: TextSpan(
                    text: "Welcome to \n",
                    style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 30, color: Color(0xff4C3AB4) ,fontWeight: FontWeight.bold),
                    children: [
              TextSpan(
                  text: "Monika Tai App",
                  style:  TextStyle(fontFamily: 'Gilroy-Bold',fontWeight: FontWeight.w800,color: Color(0xff4C3AB4)))
            ])),
          ),
       ]  ),
        
     
    );
  }
}
