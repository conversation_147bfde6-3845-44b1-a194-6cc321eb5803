
import 'dart:io';

import 'package:flutter_svg/svg.dart';
import 'package:mla_connect/controller/bottomBarController.dart';

import 'package:mla_connect/new_ui/create/createOptions.dart';
import 'package:mla_connect/new_ui/home/<USER>';
import 'package:mla_connect/new_ui/karyakarta/KaryakartaHome.dart';

import 'package:mla_connect/screens/profile/profile_page.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/utils/constants.dart';

import 'package:flutter/material.dart';

import 'package:get/instance_manager.dart';
import 'package:mla_connect/whatsapp.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../controller/service/login.dart';
import '../analytics/analytics.dart';
import '../karyakarta/karyakarta_registration.dart';

class MyBottomBar extends StatefulWidget {
  MyBottomBar({Key? key, this.showBottomCard}) : super(key: key);
  bool? showBottomCard;
  @override
  State<MyBottomBar> createState() => _MyBottomBarState();
}

class _MyBottomBarState extends State<MyBottomBar> {
  late BottomBarController _bottomBarController =
      Get.put(BottomBarController());
  bool checkInitialLoading = false;
  var role;
  LoginService login =LoginService();
  Map<String ,dynamic>  homeData ={};
  Map<String ,dynamic>  config ={};

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchHomeData() ;
    getDashboardData();
  }

  fetchHomeData()async {
  //  homeData =  await login.fetchHomeData(context) ;
    config =  await login.fetchConfig(context) ;
    //print("config $config");
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    role =  prefs.getString('role');

    setState(() {

    });

     //print("home screen check $role");
   setState(() {

   });
  }

  Widget showMandatoryUpdateDialog(BuildContext context) {
    return Scaffold(
        body: Center(
            child: Container(
      decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: BorderRadius.all(
            Radius.circular(20),
          )),
      margin: EdgeInsets.all(30),
      padding: EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
              "A new version of the app is available. Please update to continue using the app.",
              textAlign: TextAlign.center),
          SizedBox(
            height: 20,
          ),
          ElevatedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                minimumSize: MaterialStateProperty.all(const Size(300, 45)),
                backgroundColor: MaterialStateProperty.all(Colors.black),
              ),
              onPressed: () {
                final appId =
                    Platform.isAndroid ? PACKAGE_NAME : 'YOUR_IOS_APP_ID';
                final url = Uri.parse(
                  Platform.isAndroid
                      ? "market://details?id=$appId"
                      : "https://apps.apple.com/app/id$appId",
                );
                launchUrl(
                  url,
                  mode: LaunchMode.externalApplication,
                );
              },
              child: Text("Update"))
        ],
      ),
    )));
  }

  // DashboardResponse? dp;
  bool showMinimumVersionDialog = false;
  checkMinimumVersion() async {
    final minimum_version = await FirebaseCloud.instance.getMinimumVersion();
    if (APP_VERSION < minimum_version) {
      if (mounted) {
        setState(() {
          showMinimumVersionDialog = true;
        });
      }
    }
  }

  getDashboardData() async {

    List< dynamic> futures1 = [
      FirebaseCloud.instance.getDashboardData(),
      FirebaseCloud.instance.getMinimumVersion()
    ];
      // final results = await Future (futures1);
 final results= [] ;
    // dp = results[0] as DashboardResponse?;
    if (APP_VERSION < results[1]) {
      //show Dialog to update
      setState(() {
        showMinimumVersionDialog = true;
        checkInitialLoading = true;
      });
      return;
    }
    checkInitialLoading = true;

    setState(() {});
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PersistentTabView(
      context,
      controller: _bottomBarController.controller.value, //_controller,
      // confineInSafeArea: true,
      handleAndroidBackButtonPress: true,
      screens: buildScreens(),

      navBarHeight: 60,
      decoration:
          NavBarDecoration(borderRadius: BorderRadius.all(Radius.circular(4)),border: Border.all(color: Colors.black)),
      items: _buildItems(),
      // bottomScreenMargin: MediaQuery.of(context).viewInsets.bottom > 0 ? 0 : 70,
      resizeToAvoidBottomInset: false,
      onItemSelected: (value) => {_bottomBarController.setCurrentPage(value)},

      navBarStyle: NavBarStyle.style1,

      backgroundColor: Colors.white,

    );
  }
  Widget karyakarta(){
 if(role=="USER"){
    return   const KaryakartaRegistration() ;
       }else if(role=="NETA" || role=="MANAGER" ){
        return const NetaKaryakartaHome();
       }
        else {
       return  const KaryakartaHome() ;
       }
  }

  List<Widget> buildScreens() {
    List<Widget> screens = [
      HomePage(showBottomCard: true ),
      karyakarta() ,
      ProfilePage()
    ];
   if (role == "NETA"|| role=="MANAGER" ) {
      screens.add(CreateOptionsList());
    }
    if (role == "NETA"|| role=="MANAGER" ) {
      screens.add(Analytics());
       screens.add(WhatsAppBulkMessagingScreen());
    }else if(role=="KARYAKARTA") {
      screens.add(KaryakartaAna());
    }

  //   //print("screen");
  //   //print(screens.length);
  return screens;
  }

  List<PersistentBottomNavBarItem> _buildItems() {

    List<PersistentBottomNavBarItem> items = [
      PersistentBottomNavBarItem(
          icon:  SvgPicture.asset('assets/homeactive.svg'),
          contentPadding: 2,
          inactiveIcon:SvgPicture.asset('assets/Default.svg') ,
          //  ImageIcon(
          //   AssetImage('assets/icons/bottombar/Home.png'),
          // ),
          iconSize: 10,
          title: "Home",
          textStyle:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 12),
          activeColorPrimary: BOTTOMBAR_ICON_COLOR,
          inactiveColorPrimary:  Color(0xff231864).withOpacity(0.4)),



           PersistentBottomNavBarItem( iconSize: 20,
              icon:  SvgPicture.asset('assets/a.svg'),
              inactiveIcon:SvgPicture.asset('assets/Note_Edit.svg'),
              title: "Booth Work",
              textStyle:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 12),
              activeColorPrimary: BOTTOMBAR_ICON_COLOR,
              inactiveColorPrimary:Color(0xff231864).withOpacity(0.4)),
      PersistentBottomNavBarItem( iconSize: 20,
          icon: SvgPicture.asset('assets/background/UserCircle.svg'),
           inactiveIcon: SvgPicture.asset('assets/UserCircle.svg'),

          //  ImageIcon(
          //   AssetImage('assets/icons/bottombar/Profile.png'),
          // ),
          title: "Profile",
          textStyle:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 12),
          activeColorPrimary: BOTTOMBAR_ICON_COLOR,
          inactiveColorPrimary:  Color(0xff231864).withOpacity(0.4))
    ];



    if (role == "NETA"|| role=="MANAGER" ) {

      items.add(

          PersistentBottomNavBarItem(

              icon: ImageIcon(
                AssetImage('assets/icons/bottombar/Create.png'),
              ),
              iconSize: 20,
              title: "Create",
              textStyle:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 12),
              activeColorPrimary: BOTTOMBAR_ICON_COLOR,
              // inactiveColorSecondary: Colors.black,
              // activeColorSecondary: Colors.white,
              inactiveColorPrimary:Color(0xff231864).withOpacity(0.4)));
    }

    if (role == "NETA" || role=="KARYAKARTA" || role == "MANAGER") {
       items.add(

          PersistentBottomNavBarItem( iconSize: 20,
              icon:  SvgPicture.asset('assets/background/whatsapp.svg'),
              inactiveIcon:SvgPicture.asset('assets/background/whatsapp.svg') ,
              title: "Whatsapp",
              textStyle:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 12),
              activeColorPrimary: BOTTOMBAR_ICON_COLOR,));

      items.add(

          PersistentBottomNavBarItem( iconSize: 20,
               inactiveIcon:  SvgPicture.asset('assets/background/finalana.svg') ,
               icon: SvgPicture.asset('assets/background/activeana.svg') ,
              //  ImageIcon(
              //   AssetImage("assets/background/finalana.svg"),
              // ),
              title: "Analytics",
              textStyle:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 12),
              activeColorPrimary: BOTTOMBAR_ICON_COLOR,
              inactiveColorPrimary: Color(0xff231864).withOpacity(0.4)));
    }
     //print(items.length);
    return items;
  }

}
