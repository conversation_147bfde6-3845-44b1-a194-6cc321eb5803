import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mla_connect/controller/bottomBarController.dart';

import 'package:mla_connect/controller/reward_controller.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/models/Dashboard.dart';

import 'package:mla_connect/new_ui/poster/posterView.dart';

import 'package:mla_connect/screens/poster/home_poster_carousel.dart';

import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/services/payment.dart';

import 'package:mla_connect/utils/textConstants.dart';
import 'package:mla_connect/widgets/bottomSheetIDCard.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:mla_connect/widgets/user_leaderboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/instance_manager.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../controller/partyname_controller.dart';
import '../../widgets/progress.dart';
import '../../widgets/text_field.dart';
import '../poster/presets/preset1.dart';

// ignore: must_be_immutable
class HomePage extends StatefulWidget {
  HomePage({Key? key, this.showBottomCard}) : super(key: key);
  bool? showBottomCard;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with AutomaticKeepAliveClientMixin {

      // final FirebaseAuth _auth = FirebaseAuth.instance;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _rewardController = Get.put(RewardController());
// _checkPasswordStatus();
    getInitalData();
  }


// Future<void> _checkPasswordStatus() async {
//     User? user = _auth.currentUser;

//     if (user != null && !user.emailVerified) {
//       // Check if the user has a password set
//       // Google users generally don't have a password, so prompt for setting one
//       _showSetPasswordDialog();
//     }
//   }

//   void _showSetPasswordDialog() {
//     final TextEditingController _passwordController = TextEditingController();

//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: Text("Set Your Password"),
//         content: TextField(
//           controller: _passwordController,
//           obscureText: true,
//           decoration: InputDecoration(labelText: "Enter new password"),
//         ),
//         actions: [
//           TextButton(
//             onPressed: () async {
//               if (_passwordController.text.isNotEmpty) {
//                 await _setPassword(_passwordController.text);
//                 Navigator.pop(context);
//               } else {
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   SnackBar(content: Text("Password cannot be empty")),
//                 );
//               }
//             },
//             child: Text("Set Password"),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> _setPassword(String password) async {
//     User? user = _auth.currentUser;

//     if (user != null) {
//       try {
//         await user.updatePassword(password);
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text("Password set successfully")),
//         );
//       } catch (e) {
//         print("Error updating password: $e");
//       }
//     }
  // }
  bool get wantKeepAlive => true;

  

  LoginService login = LoginService();
  var homeData;
  DashboardResponse? _dashboardResponse;
  RewardController? _rewardController;
  bool showReward = false;
  BottomBarController bottomBarController = Get.put(BottomBarController());
  var result ;
  var config;
  var role;
  var data;
  PartyName partyName = Get.put(PartyName()) ;
  String capitalizeWords(String name) {
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');} 
  fetchHomeData() async {
    result = await login.fetchHomeData(context);
     //print(result);
    homeData = result['response'];
    config = await login.fetchConfig(context);
    //  //print(config);
    //  //print(config['partyList'].runtimeType);
    // partyName.paryname = config['partyList'];
     
    if(result['status']== 403 ) {
   showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
    }else{
    
data=homeData["posterLeaderBoard"] ;
 //print(data.runtimeType);
    
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    role = prefs.getString('role');
    
    role == "NETA" ||   role=="MANGER"
        ? bottomBarController.userLeaderBoard = homeData["posterLeaderBoard"]
        : null;
    setState(() {});
  }
  }

  getInitalData() async {
   await fetchHomeData();
     //print("object" + widget.showBottomCard.toString());
    if (widget.showBottomCard == true)  //print("yaha aayaa");
    bottomSheetIdCard(
      context,
    );
    if (mounted)
      setState(() {
        showReward = true;
      });
    

    final SharedPreferences prefs = await SharedPreferences.getInstance();

    var position = await prefs.getString("ROLEUSERAPI");
  }

  final List _homeWidgets = [
    SizedBox(
      height: 10,
    ),
    // StreakStepper(),
    HomePosterCarousel(),
    SizedBox(
      height: 10,
    ),
    Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  CHECK_TODAYS_POST_MA,
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 16,
                  ),
                )),
            Divider(
              thickness: 1,
            )
          ],
        )),
 SizedBox(
      height: 50,
    )
  ];
String formatName(String name) {
  List<String> parts = name.split(" ");
  if (parts.length > 1) {
    // Ensure the second part is uppercase
    parts[1] = parts[1].toUpperCase();
  }
  // Capitalize the first part
  parts[0] = capitalize(parts[0]);
  return parts.join(" ");
}

String capitalize(String s) {
  if (s.isEmpty) return s;
  return s[0].toUpperCase() + s.substring(1).toLowerCase();
}
 

  @override
  Widget build(BuildContext context) {
    // fetchHomeData();
    
    return   WillPopScope(
        onWillPop: () {
        
          SystemNavigator.pop();
          BottomBarController _bottomBarController =
              Get.find<BottomBarController>();
          _bottomBarController.setCurrentPage(0);
          return Future.value(false);
        },
        child: Scaffold(
          //  backgroundColor: Color.fromARGB(255, 198, 196, 196),
            appBar: AppBar(
             backgroundColor:   Color(0xff4C3AB4),
              elevation: 0,
              leadingWidth: 0,
              title: homeData == null
                  ? CircularProgressIndicator()
                  : Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                            border: Border.all(width: 2, color: Colors.white),
                            image: DecorationImage(
                              image: CachedNetworkImageProvider(
                                  homeData["netaInfo"]["profileImgUrl"]),
                              fit: BoxFit.fitHeight,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                             formatName(homeData["netaInfo"]["name"] ) ,style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.white),
                          ),
                        )
                      ],
                    ),
                
            ),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(
                    height: 2,
                  ),
                  //  StreakStepper(),
                  homeData == null
                      ? CircularProgressIndicator()
                      : Column(
                          children: [
                            
                            homeData["posterLeaderBoard"] != null
                                ? Padding(
                                    padding:
                                        EdgeInsets.only(left: 20, right: 20),
                                    child: data.isEmpty? Text(""):  UserLeaderboard(homeData: homeData , isfirst: true,),
                                  )
                                : SizedBox(),
                             SizedBox(height: 5,),
                            SizedBox(
                              // height:
                              // 500,
                              // MediaQuery.of(context).size.height * 0.77,
                              child: ListView.builder(
                                  physics: NeverScrollableScrollPhysics(),
                                  // cacheExtent: 100,
                                  shrinkWrap: true,
                                  itemCount: homeData['posters'].length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    var posterDetails = homeData['posters'];
                                    var userDetails = homeData['userInfo'];

                                    //  //print("home data ");
                                    //  //print(
                                    //     "home data here ${posterDetails.length}");
                                    //  //print(posterDetails.length == null);
                                    //  //print(posterDetails);
                                    //  //print(userDetails);

                                    return Container( 
                                       color: Color.fromARGB(255, 198, 196, 196),
                                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                                      // decoration: BoxDecoration(border: Border.all(color: Color.fromARGB(255, 189, 190, 190)) , borderRadius: BorderRadius.circular(5)),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          
                                          // SizedBox(
                                          //   // height: MediaQuery.of(context).size.height,
                                          //   width: MediaQuery.of(context).size.width*1,
                                          //   child: Align(
                                          //       alignment: Alignment.topLeft,
                                          //       child: Text(
                                          //         "" +
                                          //             " ${posterDetails[index]["caption"]}",
                                          //          maxLines: 2,
                                          //         style:  TextStyle(fontFamily: 'Gilroy',fontSize: MediaQuery.of(context).size.height*0.023),
                                          //         overflow: TextOverflow.ellipsis,
                                          //       )),
                                          // ),
                                          GestureDetector(
                                            onTap: () {
                                              // currentPosterIndex = realIndex;
                                              // _controller.animateToItem(realIndex);
                                            },
                                            child: Container(
                                              height: MediaQuery.of(context).size.height*0.47,
                                              
                                              color: Colors.grey,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              child: posterDetails == null
                                                  ? Container(
                                                      child: PI1(),
                                                      height: 50,
                                                      width: 50,
                                                    )
                                                  : GestureDetector(
                                                      onTap: () {
                                                         //print("pressed");
                                                      },
                                                      child: Container(
                                                          child: Stack(
                                                        children: [
                                                          Container(
                                                          decoration: BoxDecoration(border: Border.all(color: Colors.black ,width: 2) , color: Colors.white),

                                                            child: CachedNetworkImage(
                                                                height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height,
                                                                //  width: double.infinity,
                                                                fit:
                                                                    BoxFit.fill,
                                                                imageUrl:
                                                                    posterDetails[
                                                                            index]
                                                                        [
                                                                        "url"]),
                                                          ),
                                                          Center(
                                                              child: userDetails[
                                                                          "profileImgUrl"] ==
                                                                      null
                                                                  ? SizedBox(
                                                                      child: Preset1(
                                                                          minimize:
                                                                              true,
                                                                          name:
                                                                             userDetails["name"]),
                                                                    )
                                                                  : SizedBox(
                                                                      child: Preset1(
                                                                          minimize:
                                                                              true,
                                                                          profileUrl:
                                                                              userDetails["profileImgUrl"],
                                                                          name:capitalizeWords(userDetails["name"]) , netaName: formatName(homeData["netaInfo"]["name"]),),
                                                                    )),
                                                        ],
                                                      ))),
                                            ),
                                          ),
                                          
                                        SizedBox(width: MediaQuery.of(context).size.width,
                                          child:   ElevatedButton(
                                                style: ButtonStyle(
                                                    backgroundColor:
                                                        WidgetStateProperty
                                                            .all<Color>(
                                                   Color(0xff4C3AB4),
                                                )),
                                                onPressed: () async {
                                                  _sendToPosterPage(
                                                      index,
                                                      context,
                                                      userDetails["name"],
                                                      posterDetails[index]
                                                          ["url"],
                                                      userDetails[
                                                          "profileImgUrl"],
                                                      posterDetails[index]
                                                          ["caption"],
                                                      posterDetails[index]
                                                          ["posterId"]);
                                                },
                                                child: Row(
                                                 mainAxisSize:    MainAxisSize.min,
                                                 
                                                  children: [
                                                    Icon(Icons.share,color: Colors.white,),
                                                    Text(
                                                      " Share Poster And Earn 5 Points",
                                                      style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.white,
                                                        fontSize:MediaQuery.of(context).size.height*0.02 ,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )) ,
                                        ],
                                      ),
                                    );
                                  }),
                            ),
                          ],
                        ),
                ],
              ),
            )));
  }

  _sendToPosterPage(int itemIndex, BuildContext con, String username,
      String posterImage, String userImage, String captiontext, String posterID
      //  Map<String,dynamic> posterDetails , Map<String,dynamic> userDetails
      ) {
     //print("pahucha");
    // if (profile?.profileUrl == null) {
    //   AlertDialog alert = AlertDialog(
    //     title: Text("Upload profile picture"),
    //     content: Text("Please upload your profile picture to get your poster"),
    //     actions: [
    //       TextButton(
    //         child: Text("Upload"),
    //         onPressed: () {
    //           Navigator.of(context).pop();
    //           BottomBarController _bottomBarController =
    //               Get.find<BottomBarController>();
    //           _bottomBarController.setCurrentPage(4);
    //         },
    //       ),
    //     ],
    //   );
    //   showDialog(
    //     context: context,
    //     useRootNavigator: false,
    //     builder: (BuildContext context) {
    //       return alert;
    //     },
    //   );
    // } else {
    context.pushWidget((context) => PosterView(
          posterID: posterID,
          name: username,
          profileURl: userImage,
          posterURl: posterImage,
          captionText: captiontext,
        ));
    // }
  }
}

class DetailForm extends StatefulWidget {
  @override
  State<DetailForm> createState() => _DetailFormState();
}

class _DetailFormState extends State<DetailForm> {
  TextEditingController namecontroller = TextEditingController();

  TextEditingController phonecontroller = TextEditingController();

  TextEditingController designationcontroller = TextEditingController();

  final _formkey = GlobalKey<FormState>();
  bool designation = false;
  bool name = false;
  bool number = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.0),
      child: Form(
        key: _formkey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              'ENTER YOUR DETAILS',
              style:  TextStyle(fontFamily: 'Gilroy',
                  fontSize: 15,
                  color: Colors.black,
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 15.0),
            MyTextfield(
              lable: 'ENTER YOUR NAME',
              hint: 'Full Name',
              controller: namecontroller,
            ),
            SizedBox(height: 10.0),
            MyTextfield(
                lable: 'ENTER YOUR NUMBER',
                hint: 'Mobile Number',
                controller: phonecontroller),
            number == true
                ? Text(
                    "enter valid number ",
                    style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
                  )
                : SizedBox(),
            SizedBox(height: 10.0),
            MyTextfield(
              lable: 'ENTER YOUR DESIGNATION',
              hint: 'Designation',
              controller: designationcontroller,
            ),
            SizedBox(height: 10.0),
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: ElevatedButton(
                child: Text('Next'),
                onPressed: () async {
                  if (namecontroller.text == null ||
                      phonecontroller.text.length != 10 ||
                      designationcontroller.text == null) {
                     //print("error");
                    Fluttertoast.showToast(
                        msg: "All field required",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.CENTER,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  } else {
                    bool result = await FirebaseCloud.instance.processData(
                        namecontroller.text,
                        phonecontroller.text,
                        designationcontroller.text);

                    if (result) {
                      Navigator.pop(context);
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          // Return the AlertDialog
                          return AlertDialog(
                            // title: Text('अपना कार्यकर्ता एप्प बनवाने के/nलिए टोकन मनी जमा करे '),
                            // content: UserInformationForm(),
                            actions: [
                              PayCard(
                                name: namecontroller.text,
                                number: phonecontroller.text,
                                designation: designationcontroller.text,
                              )
                            ],
                          );
                        },
                      );
                    } else {
                      Fluttertoast.showToast(
                          msg: "Something went wrong",
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.CENTER,
                          timeInSecForIosWeb: 1,
                          backgroundColor: Colors.red,
                          textColor: Colors.white,
                          fontSize: 16.0);
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xff4C3AB4),
                  // padding: EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                  textStyle:  TextStyle(fontFamily: 'Gilroy',fontSize: 15),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PayCard extends StatelessWidget {
  final String name;
  final String number;
  final String designation;

  TextEditingController textcon = TextEditingController();

  PayCard(
      {Key? key,
      required this.name,
      required this.number,
      required this.designation})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    textcon.text = "₹100";
    return Container(
      padding: EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            'अपना कार्यकर्ता एप बनवाने के',
            style:  TextStyle(fontFamily: 'Gilroy',
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          Text(
            'लिए टोकन मनी जमा करे',
            style:  TextStyle(fontFamily: 'Gilroy',
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 10),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.07,
            child: TextFormField(
              controller: textcon,
              // controller: '₹100',
              decoration: InputDecoration(
                labelText: "PAY",
                labelStyle:  TextStyle(fontFamily: 'Gilroy',
                    color: Colors.black,
                    fontSize: 12,
                    fontWeight: FontWeight.bold // Set label text color to black
                    ),
                filled: true,
                enabled: true,
                fillColor: Colors.white,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                      color: Color(0xffE8E8E8)), // Black border color
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                      color: Color(
                          0xffE8E8E8)), // Black border color when the field is focused
                ),
              ),
            ),
          ),
          SizedBox(height: 10),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Color(0xff4C3AB4)
                  // backgroundColor: Color(0xff4C3AB4), // foreground
                  ),
              onPressed: () {
                 //print("payment object");
                var rp = RazorpayPayment(
                    context: context,
                    amount: 100,
                    name: name,
                    phoneNumber: number,
                    designation: designation);
                rp.initRazorpay();
                rp.openCheckout();
              },
              child: Text('Pay Now'),
            ),
          )
        ],
      ),
    );
  }
}
