import 'package:mla_connect/controller/downloadingDataController.dart';
import 'package:mla_connect/models/karyakarta.dart';
import 'package:mla_connect/new_ui/karyakarta/KaryakartaHome.dart';
import 'package:mla_connect/screens/voters/voter_search.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';
import 'package:get/state_manager.dart';

class CadreOptions extends StatefulWidget {
  const CadreOptions({Key? key}) : super(key: key);

  @override
  State<CadreOptions> createState() => _CadreOptionsState();
}

class _CadreOptionsState extends State<CadreOptions> {
  late final DownloadingDataController _downloadingDataController;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _downloadingDataController = Get.isRegistered<DownloadingDataController>()
        ? Get.find<DownloadingDataController>()
        : Get.put(DownloadingDataController());
    ever(_downloadingDataController.dataDownloaded, (value) {
      if (value == true)
        Navigator.pushReplacement(context,
            MaterialPageRoute(builder: (BuildContext context) {
          return KaryakartaHome();
        }));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Obx(() => Padding(
            padding: EdgeInsets.all(20),
            child: _downloadingDataController.dataDownloaded.value
                ? Center(child: Text("You have all the data here"))
                : Center(
                    child: _downloadingDataController.downloading.value
                        ? Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                                LinearProgressIndicator(
                                  value: _downloadingDataController
                                          .getCurrentProgress /
                                      100,
                                  minHeight: 8,
                                  backgroundColor: Colors.grey,
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Text(
                                    "Downloading  ${_downloadingDataController.getCurrentProgress} %")
                              ])
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                                Text(
                                    "No Data Available, you need to download data this may take time."),
                                ElevatedButton(
                                    onPressed: () {
                                      FirebaseCloud.instance.downloadVoters();
                                      _downloadingDataController
                                          .downloading.value = true;
                                    },
                                    child: Text("Download All Data"))
                              ]),
                  ))));
  }
}
