import 'dart:ui';

import 'package:mla_connect/controller/service/login.dart';

import 'package:flutter/material.dart';
import 'package:mla_connect/new_ui/onboarding/customtextfield.dart';
import 'package:mla_connect/widgets/custom_textfield.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../../widgets/session_expired.dart';

class KaryakartaRegistration extends StatefulWidget {
  const KaryakartaRegistration({Key? key}) : super(key: key);

  @override
  State<KaryakartaRegistration> createState() => _KaryakartaRegistrationState();
}

class _KaryakartaRegistrationState extends State<KaryakartaRegistration> {
  final nameController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final epicController = TextEditingController();
  final refrenceController = TextEditingController();
  var role;
  LoginService login = LoginService();
  final _formKey = GlobalKey<FormState>();
  String isrequested = "false";
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchkkstaus();
  }

  fetchkkstaus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    isrequested = prefs.getString('kkrequest')!;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title:   Text("Register yourself",
                    style:  TextStyle(fontFamily: 'Gilroy',
                      color: Colors.white,
                     
                      fontWeight: FontWeight.w600,
                      fontSize: 26,
                    )),centerTitle: true, backgroundColor: Color(0xff4C3AB4),),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            children: [
              
              // Text("Register yourself",
              //     style:  TextStyle(fontFamily: 'Gilroy',
              //       color: Colors.black,
              //       fontFamily: 'Gilroy',
              //       fontWeight: FontWeight.bold,
              //       fontSize: 20,
              //     )),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.04,
              ),
            CustomTextFormField( label: "ENTER NAME",hint: "e.g. Amit Kumar", controller:nameController ,  validator: (text) {
                    if (text != null && text.length < 2) {
                      return "Enter Valid Name";
                    }
                  } ,),
              // TextFormField(
              //     validator: (text) {
              //       if (text != null && text.length < 2) {
              //         return "Enter Valid Name";
              //       }
              //     },
              //     controller: nameController,
              //     decoration: InputDecoration(
              //       border: OutlineInputBorder(
              //         borderSide: const BorderSide(
              //           color: Colors.black54,
              //           width: 2.0,
              //         ),
              //         borderRadius: BorderRadius.circular(25.0),
              //       ),
              //       hintText: "Name",
              //       prefixIcon: Icon(
              //         Icons.person,
              //       ),
              //     )),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.03,
              ),
              CustomTextFormField( label: "ENTER MOBILE NO.",hint:" e.g. 8264849728", controller:phoneNumberController , validator: (text) {
                  if (text != null && text.length == 10) {
                    final digits = int.tryParse(text);
                    if (digits == null)
                      return "Phone number can only have digits";
                    return null;
                  } else
                    return "Enter Valid Phone Number";
                },),
              // TextFormField(
              //   validator: (text) {
              //     if (text != null && text.length == 10) {
              //       final digits = int.tryParse(text);
              //       if (digits == null)
              //         return "Phone number can only have digits";
              //       return null;
              //     } else
              //       return "Enter Valid Phone Number";
              //   },
              //   controller: phoneNumberController,
              //   decoration: InputDecoration(
              //     border: OutlineInputBorder(
              //       borderSide: const BorderSide(
              //         color: Colors.black54,
              //         width: 2.0,
              //       ),
              //       borderRadius: BorderRadius.circular(25.0),
              //     ),
              //     hintText: "Phone Number",
              //     prefixIcon: Icon(
              //       Icons.phone,
              //     ),
              //   ),
              //   keyboardType: TextInputType.phone,
              // ),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.03,
              ),
              CustomTextFormField( label: "VOTER ID",hint:"e.g. 1234657", controller:epicController ,validator: (text) {
                  if (text != null && text.length < 2) {
                    return "Enter Valid Name";
                  }
                },),
              // TextFormField(
              //   validator: (text) {
              //     if (text != null && text.length < 2) {
              //       return "Enter Valid Name";
              //     }
              //   },
              //   controller: epicController,
              //   decoration: InputDecoration(
              //     border: OutlineInputBorder(
              //       borderSide: const BorderSide(
              //         color: Colors.black54,
              //         width: 2.0,
              //       ),
              //       borderRadius: BorderRadius.circular(25.0),
              //     ),
              //     hintText: "Epic Id",
              //     prefixIcon: Icon(
              //       Icons.info,
              //     ),
              //   ),
              //   // keyboardType: TextInputType.phone,
              // ),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.03,
              ),
               CustomTextFormField( label: "REFRENCE NAME",hint:"ee.g. male", controller:refrenceController ,  validator: (text) {
                  if (text != null && text.length < 2) {
                    return "Enter Valid Name";
                  }
                },),
              // TextFormField(
              //   validator: (text) {
              //     if (text != null && text.length < 2) {
              //       return "Enter Valid Name";
              //     }
              //   },
              //   controller: refrenceController,
              //   decoration: InputDecoration(
              //     border: OutlineInputBorder(
              //       borderSide: const BorderSide(
              //         color: Colors.black54,
              //         width: 2.0,
              //       ),
              //       borderRadius: BorderRadius.circular(25.0),
              //     ),
              //     hintText: "Reference Name",
              //     prefixIcon: Icon(
              //       Icons.person,
              //     ),
              //   ),
              //   // keyboardType: TextInputType.phone,
              // ),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.03,
              ),
              isrequested == "true"
                  ? Column(
                    children: [
                      Text("Registration Completed!" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 20 , fontWeight: FontWeight.bold),),
                  SizedBox(height: 10,) ,   Text("Please wait for approval",  style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),),
                   SizedBox(height: 10,) ,   Text("Please close the app and reopen to check for approval ",  style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),),
                    ],
                  )
                  : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: SizedBox(
                      height: 50,
                      width: double.infinity,
                      child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
    backgroundColor: Color(0xff4C3AB4) , // Change the color here
  ),
                        onPressed: () async {
                          if (_formKey.currentState!.validate()) {
                            // Form is valid, proceed with submission
                            String epicId = epicController.text.isEmpty
                                ? ""
                                : epicController.text;
                            var res = await login.karyakartaRequest(
                                context,
                                nameController.text,
                                phoneNumberController.text,
                                epicId,
                                refrenceController.text);
                             //print("res karyakarta screen  $res");
                            if (res["status"] == 200) {
                              SharedPreferences prefs =
                                  await SharedPreferences.getInstance();
                              await prefs.setString('kkrequest', "true");
                              setState(() {
                                isrequested = "true";
                              });

                              final snackBar = SnackBar(
                                content: const Text(
                                    'Request sent, Wait for approval '),
                              );
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);

                              // Navigator.pop(context);
                            } else if (res["status"] == 403) {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return SessionExpiredDialog();
                                },
                              );
                            }
                          } else {
                             //print("error");
                            final snackBar = SnackBar(
                              content: const Text(
                                  'Name, PhoneNo, ReferenceName required'),
                            );
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                          }
                        },
                        child: Text('REGISTER' , style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 16  , color: Colors.white),),
                      ),
                    ),
                  ),
            ],
          ),
        ),
      ),
    );
  }
}
