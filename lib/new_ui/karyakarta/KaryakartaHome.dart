 
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/new_ui/karyakarta/neta_voter_search.dart';
import 'package:mla_connect/new_ui/onboarding/customtextfield.dart';
import 'package:mla_connect/screens/voters/voter_search.dart';
import 'package:mla_connect/widgets/session_expired.dart';
 
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
 
import 'package:shared_preferences/shared_preferences.dart';

import '../../screens/voters/voter_card.dart';
import '../../screens/voters/voter_model.dart';
import '../../widgets/custom_textfield.dart';
import '../create/approveKaryakarta.dart';

class KaryakartaHome extends StatefulWidget {
  const KaryakartaHome({Key? key}) : super(key: key);

  @override
  State<KaryakartaHome> createState() => _KaryakartaHomeState();
}

class _KaryakartaHomeState extends State<KaryakartaHome> {
  bool loading = true;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchRole() ;
    // getData();
  }
  var role ;
   
  

  fetchRole()async{
    SharedPreferences prefs = await SharedPreferences.getInstance();
    
     role = prefs.getString('role');
     
  }
  bool isMatDataSelected = true;
 
  Widget build(BuildContext context) {
  fetchRole();
 
    return
    
        DefaultTabController(
            length: 2,
            child: Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                backgroundColor: Color(0xff4C3AB4),
                title: Text("MAP VOTERS OF YOUR BOOTH"),
                centerTitle: true,
          //       bottom:PreferredSize(
          // preferredSize: Size.fromHeight(kToolbarHeight), 
          // child: Material(
          // color: Colors.green,
          //         child: TabBar( 
          //            tabs: [
          //              Tab(
          //               text:   "Matdata",
          //             ),
          //               Tab(text: "Team"),
          //           ],
          //         ),
          //       )),
              ),
              body: SingleChildScrollView(
                child: Column(
                  children: [
              Container(
                    margin: EdgeInsets.all(16.0),
                    padding:  EdgeInsets.symmetric(horizontal: 10) ,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.0),
                      border: Border.all(color:Color(0xffD7D9E4)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: isMatDataSelected ?Color(0xff231864): Colors.white,
                  // onPrimary: isMatDataSelected ? Colors.white : Colors.black,
                  
                ),
                onPressed: () {
                  setState(() {
                    isMatDataSelected = true;
                  });
                },
                child: Text('MatData' , style: TextStyle(color: isMatDataSelected ? Colors.white: Color(0xff696F8C)),),
                          ),
                        ),
                        Container(
                          width: 1.0,
                          height: 40.0,
                          color: Colors.grey,
                           margin: EdgeInsets.symmetric(horizontal: 5),
                        ),
                        Expanded(
                          child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: isMatDataSelected ? Colors.white : Color(0xff231864),
                  // onPrimary: isMatDataSelected ? Colors.black : Colors.white,
                 
                 
                ),
                onPressed: () {
                  setState(() {
                    isMatDataSelected = false;
                  });
                },
                child: Text('Team' , style: TextStyle(color: !isMatDataSelected ? Colors.white: Color(0xff696F8C)),),
                          ),
                        ),
                      ],
                    ),
                  ) ,
                   isMatDataSelected ? SingleChildScrollView(
                     child: Container(
                      height:MediaQuery.of(context).size.height*0.7,
                       child:  
                         VoterSearch(),
                      
                     ),
                   ) :KaryakartaTeam()
               
                  ],
                ),
              )
              
                // TabBarView(
                //       children: [
                       
                //     VoterSearch() ,
                //     KaryakartaTeam()

                //   ],
                //     ),
            ),
          );
  }
} 


class NetaKaryakartaHome extends StatefulWidget {
  const NetaKaryakartaHome({Key? key}) : super(key: key);

  @override
  State<NetaKaryakartaHome> createState() => _NetaKaryakartaHomeState();
}

class _NetaKaryakartaHomeState extends State<NetaKaryakartaHome> {
  bool loading = true;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchRole() ;
    // getData();
  }
  var role ;
  fetchRole()async{
    SharedPreferences prefs = await SharedPreferences.getInstance();
    role = prefs.getString('role');
  }
 
  Widget build(BuildContext context) {
  fetchRole();
 
    return
    
        DefaultTabController(
            length: 3,
            child: Scaffold(
              appBar: AppBar(
                backgroundColor: Color(0xff4C3AB4),
                title: Text("Karyakarta",style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.white , fontSize: 26),),
                centerTitle: true,
                bottom:PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight), 
          child: Material(
          color: Colors.grey ,
                  child: TabBar( 
                    labelColor: Color(0xff4C3AB4),

                    indicatorColor: Color(0xff4C3AB4),
                     tabs: [
                      Tab(
                        text:   "Matdata",
                      ) ,
                        Tab(
                        text:   "KaryaKarta Request",
                      )   ,
                        Tab(text: "Team"),
                    ],
                  ),
                )),
              ),
              body: 
              
                  TabBarView(
                  children: [
                     NetaVoterSearch(),
                    ApproveKaryakartaRequest()  ,
                      KaryakartaTeam()

                      ]  
                    ),
            ),
          );
  }
} 

class VoterSearch extends StatefulWidget {
 
    VoterSearch({Key? key  }) : super(key: key);

  @override
  State<VoterSearch> createState() => _VoterSearchState();
}

class _VoterSearchState extends State<VoterSearch> {
 LoginService login =LoginService() ;
   String ? query =" "  ;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchVoterDetails();
  }
 var voterdata ;
  fetchVoterDetails() async{
var result= await login.fetchVoterDetails(context) ;
 voterdata = result['res'] ;
 
 if(result['status'] ==403) {
 showDialog(
    context: context,
    builder: (BuildContext context) {
    return SessionExpiredDialog();
                },
              );
 }
 
 setState(() {
   
 });
  }
   
  
  List<dynamic> getFilteredData()  {
  //  if (query!.isEmpty) {
    
  //     return List.from(voterdata['voters']);
  //   }
    List<dynamic> filteredData = [];
    final queryLower = query!.toLowerCase();

    for (var entry in voterdata['voters']) {
      final nameLower = entry['name'].toString().toLowerCase();

      if (nameLower.startsWith(queryLower)) {
        filteredData.add(entry);
      }
    }

    return filteredData;
  }
     
  @override
  Widget build(BuildContext context) {
     fetchVoterDetails();
     
   final  filteredData =  getFilteredData(); 
   
   
    return  
    voterdata.isEmpty ? CircularProgressIndicator() : 
       Column(
         children: [
          CustomTextFormField(hint: "Search by voter name",label: "Search by name", onchanged:(value) {
                 setState(() {
                    query = value;
                 });
               },  ),
          //  Padding(
          //    padding:   EdgeInsets.symmetric(horizontal: 10),
          //    child: TextField(
          //      decoration: InputDecoration(
          //        labelText: 'Search by name',
          //      ),
          //      onChanged: (value) {
          //        setState(() {
          //           query = value;
          //        });
          //      },
          //    ),
          //  ),
           SizedBox(height: 10,),
         filteredData.isNotEmpty?  Expanded(
             child: 
                  ListView.builder(
            // controller: _controller,
             //  physics: const AlwaysScrollableScrollPhysics(),
            itemCount:filteredData.length ,
            itemBuilder: (context, index) {
          
                return  
                VoterCard(
                   voterDetails: filteredData[index] 
                 );
              // }
            },
        ) 
           ):Expanded(
             child: 
                  ListView.builder(
            // controller: _controller,
             //  physics: const AlwaysScrollableScrollPhysics(),
            itemCount:voterdata['voters'].length ,
            itemBuilder: (context, index) {
          
                return  
                VoterCard(
                   voterDetails: voterdata['voters'][index] 
                 );
              // }
            },
        ) 
           ),
         
           //  ListView.builder(
       //      // controller: _controller,
       //       //  physics: const AlwaysScrollableScrollPhysics(),
       //      itemCount: voterdata['voters'].length ,
       //      itemBuilder: (context, index) {
          
       //          return  
       //          VoterCard(
       //             voterDetails: voterdata['voters'][index] 
       //           );
       //        // }
       //      },
       //  ) ;
         ],
       );
    
     
        
  }
  

}

class _FilterScreen extends StatefulWidget {
  // final SearchState current;
  const _FilterScreen({Key? key,
  // } required this.current
   }) : super(key: key);

  @override
  __FilterScreenState createState() => __FilterScreenState();
}

class __FilterScreenState extends State<_FilterScreen> {
  // late SearchState state;
   TextEditingController boothController =TextEditingController();
  String? fieldError;

   
  @override
  Widget build(BuildContext context) {
     

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Text(
          //   'Search By',
          //   
          // ),
          // if (fieldError != null)
          //   Text(
          //     fieldError!,
          //     style: tt.subtitle2?.copyWith(color: Colors.red),
          //   ),
          // Row(
          //   children: [
          //     Text('Name: ', style: tt.subtitle1),
          //     SizedBox(
          //       width: 16,
          //     ),
          //     Checkbox(
          //         value: state.field.name,
          //         onChanged: (value) {
          //           if (value != null) {
          //             setState(() {
          //               state = state.copyWith(
          //                   field: state.field.copyWith(name: value));
          //               checkFieldError();
          //             });
          //           }
          //         }),
          //   ],
          // ),
          // Row(
          //   children: [
          //     Text('Booth: ', style: tt.subtitle1),
          //     SizedBox(
          //       width: 16,
          //     ),
          //     Checkbox(
          //         value: state.field.booth,
          //         onChanged: (value) {
          //           if (value != null) {
          //             setState(() {
          //               state = state.copyWith(
          //                   field: state.field.copyWith(booth: value));
          //               checkFieldError();
          //             });
          //           }
          //         }),
          //   ],
          // ),
          // Divider(),
          Text(
            'Filters',
             
          ),
          Row(
            children: [
              Text('Gender: ', ),
              SizedBox(
                width: 16,
              ),
              DropdownButton<SearchGender>(
                  value: SearchGender.Male,
                  onChanged: (gender) {
                     
                  },
                  items: [
                    DropdownMenuItem(
                        child: Text("Male"), value: SearchGender.Male),
                    DropdownMenuItem(
                        child: Text("Female"), value: SearchGender.Female)
                  ]),
              
                Container(
                  margin: const EdgeInsets.only(left: 24),
                  child: TextButton(
                      onPressed: () {
                         
                      },
                      child: Text('clear')),
                )
            ],
          ),
          Row(
            children: [
              Text('Booth No: ' ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                child: TextField(
                    controller: boothController,
                    maxLength: 6,
                    maxLengthEnforcement: MaxLengthEnforcement.enforced,
                    keyboardType: TextInputType.number),
              )
            ],
          ),
          Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Cancel')),
              TextButton(
                  onPressed:() {
                    
                  }, 
                  child: Text('Apply')),
            ],
          )
        ],
      ),
    );
  }}