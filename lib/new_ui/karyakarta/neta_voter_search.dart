import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../screens/voters/voter_card.dart';
 
class NetaVoterSearch extends StatefulWidget {
  const NetaVoterSearch({Key? key}) : super(key: key);

  @override
  State<NetaVoterSearch> createState() => _NetaVoterSearchState();
}

class _NetaVoterSearchState extends State<NetaVoterSearch> {
 LoginService login =LoginService() ;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    
    // fetchVoterDetails();
  }
 var voterdata ;
//   fetchVoterDetails() async{
//  voterdata = await login.fetchVoterDetails(context) ;
//   //print(voterdata);
//  setState(() {
   
//  });
//   }
   TextEditingController boothController =TextEditingController();
    TextEditingController blockController =TextEditingController();
  @override
  Widget build(BuildContext context) {
     
   
    return  
      Column(
          children: [
      
            Padding(
              padding: const EdgeInsets.all(20),
              child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.07,
                      child: GestureDetector(
                        onTap: () {
                             showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                            title: Text('Filters Applied'),
                            content:
                                 Container(
                          // margin:  const EdgeInsets.symmetric(horizontal: 24 ,vertical: 20 ),
                           padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(20) ,border: Border.all(color: Colors.grey)),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                              Text(
                                    'Filters' 
                                 ),
                                 Row(
                                    children: [
                                      Text('Booth No: ' ),
                                      SizedBox(
                                        width: 16,
                                      ),
                                      Expanded(
                                        child: TextField(
                          controller: boothController,
                          maxLength: 6,
                          maxLengthEnforcement: MaxLengthEnforcement.enforced,
                          keyboardType: TextInputType.number),
                                      )
                                    ],
                                ),
                                Divider(),
                                Row(
                                    children: [
                                      Text('Block No: ' ),
                                      SizedBox(
                                        width: 16,
                                      ),
                                      Expanded(
                                        child: TextField(
                          controller: blockController,
                          maxLength: 6,
                          maxLengthEnforcement: MaxLengthEnforcement.enforced,
                          keyboardType: TextInputType.number),
                                      )
                                    ],
                                ),
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      TextButton(
                                          onPressed: () => Navigator.pop(context),
                                          child: Text('Cancel')),
                                      TextButton(
                                          onPressed: ()async {
                                      var   result  = await login.fetchNetaVoterDetails(context ,boothController.text ,blockController.text) ;
                                       voterdata =result['res'];
                                      print(voterdata);
                                       if(result['status'] ==403) {
 showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
 }  
                                         setState(() {
                                           
                                         });
                                          Navigator.pop(context) ;
                                          }, 
                                          child: Text('Apply')),
                                    ],
                                )
                              ],
                            ),
                              )) ; 
                              
                              }) ; 
                            },
                        child: Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(10)),
                          child:Row(children: [
                            Icon(Icons.search), 
                            SizedBox(width: 30,),
                             Text("Search"  ,style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 20),)
                          ],)
                        ),
                      ),
                    ),
            ),
                  
      
             voterdata ==null ? Text("No Data yet") :  Expanded(
              //  height: MediaQuery.of(context).size.height,
              child :  ListView.builder(
                    // controller: _controller,
                    physics: AlwaysScrollableScrollPhysics(),
                    itemCount: voterdata['voters'].length ,
                    itemBuilder: (context, index) {
                   return  
                        VoterCard(
                           voterDetails: voterdata['voters'][index] 
                         );
                      // }
                    },
                ))
       ]   );
  }
   

 }

  

 