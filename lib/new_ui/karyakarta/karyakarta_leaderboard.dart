import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class <PERSON><PERSON>kartaLeaderBoard extends StatefulWidget {
  var kkleaderboard ;
    KaryakartaLeaderBoard({Key? key ,required this.kkleaderboard}) : super(key: key);

  @override
  State<KaryakartaLeaderBoard> createState() => _KaryakartaLeaderBoardState();
}

class _KaryakartaLeaderBoardState extends State<KaryakartaLeaderBoard> {
  @override
  Widget build(BuildContext context) {
    return  SafeArea(child: 
    Scaffold(  
      appBar: AppBar(title: Text("Analytics"),centerTitle: true,elevation: 0, backgroundColor: Color(0xff4C3AB4),),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
        
          SizedBox(height: 20,),
         
         SizedBox(
         height: (MediaQuery.of(context).size.height*0.18)* widget.kkleaderboard.length,
         child: ListView.builder(
           physics: NeverScrollableScrollPhysics() ,
          itemCount: widget.kkleaderboard.length,
          itemBuilder: (context, index) {
       
            return ListTile(
              title: Container( 
                padding: EdgeInsets.all(20),
                // alignment: Alignment.topLeft,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.grey, // Border color
                    width: 1.0, // Border width
                  ),
                  borderRadius:
                      BorderRadius.circular(5), // Optional: border radius
                ),
               
                child: Row( 
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
             Container(
                    width:  MediaQuery.of(context).size.width*0.2   ,
                    height:MediaQuery.of(context).size.width*0.2  ,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(width: 2, color: Color(0xff4C3AB4)),
                      image: DecorationImage(
                        image: CachedNetworkImageProvider( widget.kkleaderboard[index]['profileImgUrl']  ),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                    // CachedNetworkImage(
                    //   imageUrl: widget.kkleaderboard[index]['profileImgUrl'],
                    //   // imageBuilder: (context, imageProvider) => Container(child: ,),
                    //   // progressIndicatorBuilder: (context, url, downloadProgress) =>
                    //   //     CircularProgressIndicator(value: downloadProgress.progress),
                    //   errorWidget: (context, url, error) =>
                    //       Icon(Icons.error),
                    //   width: 50,
                    //   fit: BoxFit.fill,
                    // ),
                    // SizedBox(
                    //   width: 30,
                    // ),
                SizedBox(width: 20,),   
                     Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                      children: [  Text('Name: ${widget.kkleaderboard[index]['name'].toString()}'),
                    SizedBox(
                      height: 3,
                    ),
                    // Text('Number: ${kk['users'][index]['number']}'),
                    SizedBox(
                      height: 3,
                    ),
                     Container( 
                        height:20,
                        decoration: BoxDecoration( color: Color.fromARGB(255, 74, 73, 73), borderRadius: BorderRadius.circular(10)),
                       padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Row(
                          children: [
                            Stack(
                              children: [
                                 Icon(Icons.circle_outlined ,color:  Colors.grey, size:15,),SizedBox(width: 10,),
                        Positioned(right:0,left: 5,
                        child: Icon(Icons.circle_outlined ,color:  Colors.grey, size: 15,)),SizedBox(width: 30,),
                                
                              ],
                            ) ,
                           
                            Text('${widget.kkleaderboard[index]['count'].toString()}  ' ,style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),),
                          ],
                        ),
                      ),
                      // Row(
                      //   children: [
                      //      Icon(Icons.star ,color: Colors.yellow, size: 20,),SizedBox(width: 10,),
                      //     Text('Total Points : ${widget.kkleaderboard[index]['count'].toString()}'),
                      //   ],
                      // ),
                       ],
                     )
                   
                       
                     
                            
                  ],
                ),
              ),
            );
          },
          ),
         ),
         
        
         
            ],
        ),
      )  
    ));
  }
}