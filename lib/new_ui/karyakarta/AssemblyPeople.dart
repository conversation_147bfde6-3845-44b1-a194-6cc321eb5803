import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/models/karyakarta.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class AssemblyPeople extends StatefulWidget {
  List<KaryakartaData> data;
  AssemblyPeople({Key? key, required this.data}) : super(key: key);

  @override
  State<AssemblyPeople> createState() => _AssemblyPeopleState();
}

class _AssemblyPeopleState extends State<AssemblyPeople> {
  final TextEditingController controller = TextEditingController();
  String? phoneNumberError;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        floatingActionButton: null,
        //  FloatingActionButton(
        //   onPressed: () => showPopUp(),
        //   child: Icon(
        //     Icons.add,
        //     color: Colors.white,
        //   ),
        // ),
        body: widget.data.length == 0
            ? Center(
                child: Text("No person added"),
              )
            : ListView.separated(
                itemBuilder: ((context, index) => Container(
                    margin: EdgeInsets.only(top: 10),
                    child: ListTile(
                      title: Text(widget.data[index].name ?? ""),
                      leading: ClipOval(
                          child: widget.data[index].profileUrl == null
                              ? Image.asset('assets/userImage.png')
                              : CachedNetworkImage(
                                  placeholder: (context, url) =>
                                      CircularProgressIndicator(),
                                  imageUrl: widget.data[index].profileUrl!)),
                      subtitle: Text(widget.data[index].phoneNumber ?? ""),
                    ))),
                separatorBuilder: ((_, __) => Divider()),
                itemCount: widget.data.length));
  }

  showPopUp() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: Text('Add assembly person'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: controller,
                    keyboardType: TextInputType.phone,
                    maxLength: 10,
                    decoration: InputDecoration(
                      hintText: '10 digit phone number',
                      errorText: phoneNumberError,
                    ),
                  ),
                ],
              ),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    if (controller.text.length == 10) {
                       //print('Phone Number: ${controller.text}');
                      Navigator.of(context).pop();
                    } else {
                      setState(() {
                        phoneNumberError = 'Enter a valid phone number';
                      });
                    }
                  },
                  child: Text('Submit'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
//to do remove all brandings , rename app , add dropdowns , add people search and memebers tab , modify tab placement
