import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/bottomBarController.dart';
import 'package:mla_connect/controller/karyakartaListController.dart';
import 'package:mla_connect/models/karyakarta.dart';
import 'package:mla_connect/new_ui/karyakarta/KaryakartaHome.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';

class BoothPeople extends StatefulWidget {
  final Map<String, List<int>> mapingData;

  final bool showAdd;

  BoothPeople({Key? key, required this.mapingData, required this.showAdd})
      : super(key: key);

  @override
  State<BoothPeople> createState() => _BoothPeopleState();
}

class _BoothPeopleState extends State<BoothPeople> {
  final TextEditingController controller = TextEditingController();
  String? phoneNumberError;

  // late String boothDropdownValue;
  // late List<int> boothDropdownValues;
  //   late String blockDropdownValue;
  // late List<int> blockDropdownValues;

  String? dropdownValue1;
  int? dropdownValue2;
  late List<KaryakartaData> mydata;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  var d = Get.find<KaryakartaListController>();
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        floatingActionButton:
        //  widget.showAdd
        //     ? 
            FloatingActionButton(
                onPressed: () => showPopUp(),
                child: Icon(
                  Icons.add,
                  color: Colors.white,
                ),
              ) ,
            // : null,
        body: d.karyakartaList!.value.booth.length == 0
            ? Center(
                child: Text("No person added"),
              )
            : ListView.separated(
                itemBuilder: ((context, index) => Container(
                    margin: EdgeInsets.only(top: 10),
                    child: ListTile(
                      title:
                          Text(d.karyakartaList!.value.booth[index].name ?? ""),
                      leading: ClipOval(
                          child:
                              d.karyakartaList!.value.booth[index].profileUrl ==
                                      null
                                  ? Image.asset('assets/userImage.png')
                                  : CachedNetworkImage(
                                      placeholder: (context, url) =>
                                          CircularProgressIndicator(),
                                      imageUrl: d.karyakartaList!.value
                                          .booth[index].profileUrl!)),
                      subtitle: Text(
                          d.karyakartaList!.value.booth[index].phoneNumber ??
                              ""),
                    ))),
                separatorBuilder: ((_, __) => Divider()),
                itemCount: d.karyakartaList!.value.booth.length)));
  }

  showPopUp() {
    showDialog(
      context: context,
      builder: (context) {
        bool loader = false;
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: Text('Add booth person'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: controller,
                    keyboardType: TextInputType.phone,
                    maxLength: 10,
                    decoration: InputDecoration(
                      hintText: '10 digit phone number',
                      errorText: phoneNumberError,
                    ),
                  ),
                  Text(
                    "Select Booth Number",
                  ),
                  DropdownButton<String>(
                    value: dropdownValue1,
                    onChanged: (String? newValue) {
                      setState(() {
                        dropdownValue1 = newValue;
                        dropdownValue2 = null;
                      });
                    },
                    items: widget.mapingData.keys
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                  Text(
                    "Select Block Number",
                  ),
                  if (dropdownValue1 != null)
                    DropdownButton<int>(
                      value: dropdownValue2,
                      onChanged: (int? newValue) {
                        setState(() {
                          dropdownValue2 = newValue;
                        });
                      },
                      items: widget.mapingData[dropdownValue1]!
                          .map<DropdownMenuItem<int>>((int value) {
                        return DropdownMenuItem<int>(
                          value: value,
                          child: Text(value.toString()),
                        );
                      }).toList(),
                    ),
                ],
              ),
              actions: [
                loader
                    ? PI1()
                    : ElevatedButton(
                        onPressed: () async {
                          if (controller.text.length == 10) {
                             //print('Phone Number: ${controller.text}');
                            setState(() {
                              loader = true;
                            });
                            String result = await FirebaseCloud.instance
                                .createKaryakarta("booth", controller.text);
                            Fluttertoast.showToast(msg: result);
                            if (result == "Added Successfully") {
                              var d = Get.find<KaryakartaListController>();
                              d.addPerson();
                            }
                            Navigator.of(context).pop();
                          } else {
                            setState(() {
                              phoneNumberError = 'Enter a valid phone number';
                            });
                          }
                        },
                        child: Text('Submit'),
                      ),
              ],
            );
          },
        );
      },
    ).then((_) {
      setState(() {
        // Your state update code here
      });
    });
  }
}
