import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/karyakartaListController.dart';
import 'package:mla_connect/models/karyakarta.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';

class BlockPeople extends StatefulWidget {
  final Map<String, List<int>> mapingData;
  final bool showAdd;
  BlockPeople({Key? key, required this.mapingData, required this.showAdd})
      : super(key: key);

  @override
  State<BlockPeople> createState() => _BlockPeopleState();
}

class _BlockPeopleState extends State<BlockPeople> {
  final TextEditingController controller = TextEditingController();
  String? phoneNumberError;

  late String dropdownValue;
  late List<String> dropdownValues;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    dropdownValues = widget.mapingData.keys.toList();
    dropdownValue = dropdownValues.first;
  }

  var d = Get.find<KaryakartaListController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        floatingActionButton:
        //  widget.showAdd
        //     ?
             FloatingActionButton(
                onPressed: () => showPopUp(),
                child: Icon(
                  Icons.add,
                  color: Colors.white,
                ),
              ) ,
            // : null,
        body: d.karyakartaList!.value.block.length == 0
            ? Center(
                child: Text("No person added"),
              )
            : ListView.separated(
                itemBuilder: ((context, index) => Container(
                    margin: EdgeInsets.only(top: 10),
                    child: ListTile(
                      title:
                          Text(d.karyakartaList!.value.block[index].name ?? ""),
                      leading: ClipOval(
                          child:
                              d.karyakartaList!.value.block[index].profileUrl ==
                                      null
                                  ? Image.asset('assets/userImage.png')
                                  : CachedNetworkImage(
                                      placeholder: (context, url) =>
                                          CircularProgressIndicator(),
                                      imageUrl: d.karyakartaList!.value
                                          .block[index].profileUrl!)),
                      subtitle: Text(
                          d.karyakartaList!.value.block[index].phoneNumber ??
                              ""),
                    ))),
                separatorBuilder: ((_, __) => Divider()),
                itemCount: d.karyakartaList!.value.block.length)));
  }

  showPopUp() {
    showDialog(
      context: context,
      builder: (context) {
        bool loader = false;

        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: Text('Add block person'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: controller,
                    keyboardType: TextInputType.phone,
                    maxLength: 10,
                    decoration: InputDecoration(
                      hintText: '10 digit phone number',
                      errorText: phoneNumberError,
                    ),
                  ),
                  Text(
                    "Select Block Number",
                  ),
                  DropdownButton<String>(
                    value: dropdownValue,
                    onChanged: (String? newValue) {
                      setState(() {
                        dropdownValue = newValue!;
                      });
                    },
                    items: dropdownValues
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ],
              ),
              actions: [
                loader
                    ? PI1()
                    : ElevatedButton(
                        onPressed: () async {
                          if (controller.text.length == 10) {
                             //print('Phone Number: ${controller.text}');
                            setState(() {
                              loader = true;
                            });
                            String result = await FirebaseCloud.instance
                                .createKaryakarta("block", controller.text);
                            Fluttertoast.showToast(msg: result);
                            if (result == "Added Successfully") {
                              var d = Get.find<KaryakartaListController>();
                              d.addPerson();
                            }
                            Navigator.of(context).pop();
                          } else {
                            setState(() {
                              phoneNumberError = 'Enter a valid phone number';
                            });
                          }
                        },
                        child: Text('Submit'),
                      ),
              ],
            ); 
          },
        );
      },
    );
  }
}
