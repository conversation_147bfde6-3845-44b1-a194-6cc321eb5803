import 'dart:io';

import 'package:mla_connect/new_ui/votermessaging.dart/birthdayupload.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:image_picker/image_picker.dart';

import '../../utils/imagePickerUtil.dart';
import '../create/createcontainer.dart';

class Festival extends StatefulWidget {
  const Festival({Key? key}) : super(key: key);

  @override
  State<Festival> createState() => _FestivalState();
}

class _FestivalState extends State<Festival> {
  TextEditingController messageController =TextEditingController();
  late bool defaultProfile;
  late File imageFile;
   
  void initState() {
    super.initState();
    defaultProfile = false;
    imageFile = File(
        "/data/user/0/com.campain.campaignappdemo/cache/image_picker2855218368341783188.jpg");
    // TODO: implement initState
  }
  @override
  Widget build(BuildContext context) {
    return SafeArea(child: Scaffold(
            appBar: AppBar(title: Text("FESTIVAL WISHES",style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18,letterSpacing: 2),),centerTitle: true,elevation: 0,),

      body:Column(
        
        children: [
           SizedBox(height: 80),
          FestivalContainer(labeltext: "Sms/Whatsapp to Karyakarta",ontap:  ()  => show(context), ),
                 SizedBox(height: 18.0),    FestivalContainer(labeltext: "Sms/Whatsapp to Voter",ontap: () => show(context),),
                   SizedBox(height: 18.0),            FestivalContainer(labeltext: "Sms/Whatsapp to Supporter",ontap: () => show(context),)
,         SizedBox(height: 18.0),  FestivalContainer(labeltext: "Sms/Whatsapp to Beneficiary",ontap: () => show(context),)
 

        ],
      ) ,
    ));
  }

  void show(BuildContext context) async {
      final newState = await showModalBottomSheet(
          isScrollControlled: true,
          context: context,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          builder: (context) {
            return Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: Container(
                height: MediaQuery.of(context).size.height*0.2,
                child: Column(children: [
                  SizedBox(height: 10,),
                  Text("Sending to 500 Karyakarta", style:  TextStyle(fontFamily: 'Gilroy', fontSize: 19 ,fontWeight: FontWeight.bold),
                  ),SizedBox(height: 10,),
                  FestivalCon(labeltext: "Sms to Karyakarta",ontap: () => 
                    showmesage(  context)
                  ,),SizedBox(height: 10,),
                   FestivalCon(labeltext: "Whatsapp to Karyakarta",ontap: () {
                    showWhatsmesage(context);
                  },)
                ]),
              ),
            );
          });
       
      }

     void showmesage(BuildContext context) async {
      final newState = await showModalBottomSheet(
          isScrollControlled: true,
          context: context,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          builder: (context) {
            return Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: Container(
                height: MediaQuery.of(context).size.height*0.45,
                padding: EdgeInsets.all(20),
                child: Column(children: [
                  SizedBox(height: 10,),
                    new TextFormField(
              controller: messageController,
              maxLines: 10,
              decoration: InputDecoration(
            labelText: 'Enter Message', // Set the label text here
            border: OutlineInputBorder( // Defines the border when the field is enabled
              borderSide: BorderSide(color: Colors.grey),
            ),
            enabledBorder: OutlineInputBorder( // Defines the border when the field is not focused
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder( // Defines the border when the field is focused
              borderSide: BorderSide(color: Colors.grey),
            ),
          ),
              validator: (value) {
                // if (value == null || value.isEmpty) {
                //   return 'Please enter some text';
                // }
                return null;
              },
              
            ),
             SizedBox(height: 40,),
              Center(
                child: Container( 
                  width: MediaQuery.of(context).size.width,
                  child: ElevatedButton(
                    onPressed: ()   {
                        showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        
                        return AlertDialog(
                           
                          actions: [BalanceAddMesaage() ],
                        );
                      },
                    );  
                    },
                    child: Text("Send to All")),
                ),
              )
                ]),
              ),
            );
          });
       
      }
      void showWhatsmesage(BuildContext context) async {
      final newState = await showModalBottomSheet(
          isScrollControlled: true,
          context: context,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          builder: (context) {
            return Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: Container(
                height: MediaQuery.of(context).size.height*0.55,
                padding: EdgeInsets.all(20),
                child: Column(children: [
                   Container(
              height: MediaQuery.of(context).size.height*0.2,
              width: MediaQuery.of(context).size.width,
             decoration: BoxDecoration(borderRadius: BorderRadius.circular(10) ,border: Border.all(color: Colors.grey), color: Colors.white,),
             
               child: GestureDetector(
                onTap: () async {
                  final XFile? image = await ImageUtil.pickImageFromFile();
                  if (image != null) {
                    final path = image.path;
                    File f = File(path);
        
                    setState(() {
                      imageFile = f;
                      defaultProfile = true;
                    });
                     //print(image.path);
                    
                  }
                },child: defaultProfile
              ? ClipRRect(
                  child: Image.file(
                    imageFile,
                    height: MediaQuery.of(context).size.width * 0.95,
                    width: MediaQuery.of(context).size.width * 0.95,
                    fit: BoxFit.cover,
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.blueAccent)),
                  height: MediaQuery.of(context).size.width * 0.95,
                  width: MediaQuery.of(context).size.width * 0.95,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                   mainAxisAlignment: MainAxisAlignment.center, 
                    children: [
                      Image.asset('assets/upload.png',height: 50,width: 50,),
                      Text("Upload your Creative")
                    ],
                  )),),
             ),
                  SizedBox(height: 10,),
                    new TextFormField(
              controller: messageController,
              maxLines: 5,
              decoration: InputDecoration(
            labelText: 'Enter Message', // Set the label text here
            border: OutlineInputBorder( // Defines the border when the field is enabled
              borderSide: BorderSide(color: Color.fromARGB(255, 61, 55, 55),),
            ),
            enabledBorder: OutlineInputBorder( // Defines the border when the field is not focused
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder( // Defines the border when the field is focused
              borderSide: BorderSide(color: Colors.grey),
            ),
          ),
              validator: (value) {
                // if (value == null || value.isEmpty) {
                //   return 'Please enter some text';
                // }
                return null;
              },
              
            ),
             SizedBox(height: 40,),
              Center(
                child: Container( 
                  width: MediaQuery.of(context).size.width,
                  child: ElevatedButton(
                    onPressed: ()   {
                         showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        
                        return AlertDialog(
                           
                          actions: [BalanceAddMesaage() ],
                        );
                      },
                    ); 
                    },
                    child: Text("Send to All")),
                ),
              )
                ]),
              ),
            );
          });
       
      }
}