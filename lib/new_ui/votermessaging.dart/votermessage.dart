import 'package:mla_connect/new_ui/create/createFacebookPost.dart';
 
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
 

 
import '../create/createcontainer.dart';
import 'birthdaywish.dart';
import 'festivalwish.dart';
 

class VoterMessage extends StatelessWidget {
  const VoterMessage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: 
    Scaffold(
      appBar: AppBar(title: Text("VOTER MESSAGING",style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18),),centerTitle: true,elevation: 0, iconTheme: IconThemeData(
            color: Colors.white,
          ),),
      body:Column(
         mainAxisAlignment: MainAxisAlignment.center,
         crossAxisAlignment: CrossAxisAlignment.center,
        children: [
                    CreateContainer(ontap: () => context.pushWidget((context) => BirthdayWish()),imageUrl:"assets/icons/mingcute_birthday-2-line.png" ,labeltext: "Send Birthday Wish") ,
        SizedBox(
            height: 30,
          ), CreateContainer(ontap:  () =>
                context.pushWidget((context) => Festival()),imageUrl:"assets/icons/material-symbols_festival.png" ,labeltext: "Send Festival Wish") ,

         SizedBox(
            height: 30,
          ), 
          //  CreateContainer(ontap:  () => context.pushWidget((context) => CreateTweet()),imageUrl:"assets/icons/tabler_message.png" ,labeltext: "Send Political Message") ,
         
         
      ],) ,
    ));
  }
}