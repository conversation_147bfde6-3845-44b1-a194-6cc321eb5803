import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
 
import 'package:image_picker/image_picker.dart';

import '../../utils/imagePickerUtil.dart';

class BirthdayUpload extends StatefulWidget {
  const BirthdayUpload({Key? key}) : super(key: key);

  @override
  State<BirthdayUpload> createState() => _BirthdayUploadState();
}

class _BirthdayUploadState extends State<BirthdayUpload> {
late bool defaultProfile;
  late File imageFile;
   TextEditingController captionController = new TextEditingController();
  void initState() {
    super.initState();
    defaultProfile = false;
    imageFile = File(
        "/data/user/0/com.JitendraJawah.mla_connect/cache/image_picker2855218368341783188.jpg");
    // TODO: implement initState
  }
  @override
  Widget build(BuildContext context) {
    return   Scaffold(
      appBar: AppBar(title: Text("BIRTHDAY WISHES",style:  TextStyle(fontFamily: '<PERSON><PERSON>',fontSize: 18,letterSpacing: 2),),centerTitle: true,elevation: 0,),
      body: Padding(
        padding: EdgeInsets.only(left: 20 ,right: 20),
        child: SingleChildScrollView(
          child: Column( 
            children: [
             Container(
              height: MediaQuery.of(context).size.height*0.4,
              width: MediaQuery.of(context).size.width,
             decoration: BoxDecoration(borderRadius: BorderRadius.circular(10) ,border: Border.all(color: Colors.grey), color: Colors.white,),
             
               child: GestureDetector(
                onTap: () async {
                  final XFile? image = await ImageUtil.pickImageFromFile();
                  if (image != null) {
                    final path = image.path;
                    File f = File(path);
        
                    setState(() {
                      imageFile = f;
                      defaultProfile = true;
                    });
                     //print(image.path);
                    
                  }
                },child: defaultProfile
              ? ClipRRect(
                  child: Image.file(
                    imageFile,
                    height: MediaQuery.of(context).size.width * 0.95,
                    width: MediaQuery.of(context).size.width * 0.95,
                    fit: BoxFit.cover,
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.blueAccent)),
                  height: MediaQuery.of(context).size.width * 0.95,
                  width: MediaQuery.of(context).size.width * 0.95,
                  child: Image.asset('assets/upload.png')),),
             ),
             SizedBox(height: 20,),
              new TextFormField(
              controller: captionController,
              maxLines: 10,
              decoration: InputDecoration(
            labelText: 'Enter Message', // Set the label text here
            border: OutlineInputBorder( // Defines the border when the field is enabled
              borderSide: BorderSide(color: Colors.grey),
            ),
            enabledBorder: OutlineInputBorder( // Defines the border when the field is not focused
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder( // Defines the border when the field is focused
              borderSide: BorderSide(color: Colors.grey),
            ),
          ),
              validator: (value) {
                // if (value == null || value.isEmpty) {
                //   return 'Please enter some text';
                // }
                return null;
              },
              
            ),
             SizedBox(height: 40,),
              Center(
                child: Container( 
                  width: MediaQuery.of(context).size.width,
                  child: ElevatedButton(
                    onPressed: ()   {
                      showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        
                        return AlertDialog(
                           
                          actions: [BalanceAddMesaage() ],
                        );
                      },
                    );  
                    },
                    child: Text("Send to All")),
                ),
              )
          ],),
        ),
      ),);
  }
}

class BalanceAddMesaage extends StatelessWidget {
  const BalanceAddMesaage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  Padding(
      padding: const EdgeInsets.all(30),
      child: Text("आपका एसएमएस बैलेंस कम है - बैलेंस जोड़ें टीम से संपर्क करें"),
    );
  }
}