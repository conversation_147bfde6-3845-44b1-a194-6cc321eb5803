import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/new_ui/votermessaging.dart/birthdayupload.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class BirthdayWish extends StatefulWidget {
    BirthdayWish({Key? key}) : super(key: key);

  @override
  State<BirthdayWish> createState() => _BirthdayWishState();
}

class _BirthdayWishState extends State<BirthdayWish> {
 final List number= [1,2,4];
   
   late List<bool> checkedState;
   void initState() {
    super.initState();
    // Initialize the checkedState list here
    checkedState = List.generate(number.length, (index) => false);
  }

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      appBar: AppBar(title: Text("BIRTHDAY WISHES",style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18,letterSpacing: 2),),centerTitle: true,elevation: 0,),
      floatingActionButton: GestureDetector(
        onTap:() => context.pushWidget((context) => BirthdayUpload()) ,
        child: Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xff4C3AB4),
                    ),
                    child: Icon(
                      Icons.check,
                      size:
                          20, // This is a Material icon, adjust if you have a custom icon
                      color: Colors.white, // Icon color
                    ),
                  ),
      ),
      body:Column(children: [
        Container(
           height: MediaQuery.of(context).size.height*0.79,
                        width: 500,
                         
          child: ListView.separated(
                  itemBuilder: ((context, index) => Container(
                    height: 80,
                    width: 500,
                    // color: Colors.black,
                     child:
                     Row(
                      children: [
                        Expanded(
                          child: ListTile(
                            title:
                                Text("Kunal Maru"),
                            leading: Container( 
                              height: 40,
                              width: 40,
                              decoration: BoxDecoration(
                              image: DecorationImage(image: AssetImage("assets/Ellipse 261.png"))
                            ),),
                            subtitle: Text(
                                
                                    "+91 8249494033"), 
                                    
                          ),
                        ),
                       
                           Checkbox(
          value:checkedState[index],
          onChanged: (bool? value) {
            setState(() {
           checkedState[index] = value!;
        });
          },
          checkColor: Colors.white, // Color of tick Mark
          activeColor: Colors.blue, // Background color of checkbox
        ) 
                      ],
                    ),
                  )),
                  separatorBuilder: ((_, __) => Divider()),
                  itemCount:number.length ),
        )
      ],));
  }
}
 