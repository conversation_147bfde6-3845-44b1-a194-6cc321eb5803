<!DOCTYPE html>

<html>
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <head>
    <style>
      * {
        box-sizing: border-box;
        margin: 0px;
        padding: 0px;
      }
      #container {
        display: flex;
        justify-content: center;
        margin: 0 auto;
        max-width: 100%;
        max-height: 100%;
      }
    </style>
  </head>

  <body>
    <div id="container"></div>
    <script
      id="twitter-wjs"
      type="text/javascript"
      async
      defer
      src="https://platform.twitter.com/widgets.js"
      onload="createMyTweet()"
    ></script>

    <script>
      var isFlutterInAppWebViewReady = false;
      window.addEventListener(
        "flutterInAppWebViewPlatformReady",
        function (event) {
          isFlutterInAppWebViewReady = true;
        }
      );

      function createMyTweet() {
        var twtter = window.twttr;

        twttr.widgets
          .createTweet("$tweetId", document.getElementById("container"))
          .then(function (el) {
            if (isFlutterInAppWebViewReady) {
              const widget = document.getElementById("container");
              var offsetHeight =
                document.getElementById("container").offsetHeight;
              window.flutter_inappwebview.callHandler("Twitter", offsetHeight);
            }
          });
      }
    </script>
  </body>
</html>
