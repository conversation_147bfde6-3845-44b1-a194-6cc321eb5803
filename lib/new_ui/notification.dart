 

// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
 
// import 'package:permission_handler/permission_handler.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// import '../screens/onBoarding_page.dart';

// class PermissionAccess {
//   Future<bool> requestStoragePermission() async {
//     bool isPermissionGranted = false;

//     // Assume the minimum SDK version requiring permission is 23 (Android 6.0/Marshmallow)
//     final storageStatus = await Permission.storage.request();

//     if (storageStatus.isGranted) {
//       isPermissionGranted = true;
//     } else if (storageStatus.isPermanentlyDenied ||
//         storageStatus.isDenied ||
//         storageStatus.isRestricted) {
//       isPermissionGranted = false;
//     }

//     return isPermissionGranted;
//   }

//   Future<void> requestNotificationsPermissions() async {
//     FirebaseMessaging messaging = FirebaseMessaging.instance;
//     await messaging.requestPermission(
//       alert: true,
//       announcement: false,
//       badge: true,
//       carPlay: false,
//       criticalAlert: false,
//       provisional: true,
//       sound: true,
//     );

//     PermissionStatus permissionStatus = await Permission.notification.status;
//     SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

//     if (permissionStatus.isDenied) {
//       sharedPreferences.setBool('NotificationPermission', false);
//     } else if (permissionStatus.isGranted) {
//       sharedPreferences.setBool('NotificationPermission', true);
//     }
//   }
// }

 

 
// class NotificationView extends StatefulWidget {
   

//   @override
//   State<NotificationView> createState() => _NotificationViewState();
// }

// class _NotificationViewState extends State<NotificationView> {
//   @override
//   Widget build(BuildContext context) {
   
//     return  Scaffold(
//           body: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               // LottieBuilder.asset('Asset/Lottie/notificationBell.json',height: 160,),
//               SizedBox(height: 56,),
//               Text(
//                 'Allow permissions for best\ndaily designs',
//                 textAlign: TextAlign.center,
//                 style:  TextStyle(fontFamily: 'Gilroy',
//                     fontFamily: 'Work-Sans',
//                     fontSize: 24,
//                     fontWeight: FontWeight.w800,
//                     color: Colors.black
//                 )
//               ),
//               SizedBox(height: 12,),
//               Text('Press allow to give us proper permissions',
//                 textAlign: TextAlign.center,
//                 style:  TextStyle(fontFamily: 'Gilroy',
//                   fontFamily: 'Work-Sans',
//                   fontSize: 16,
//                   fontWeight: FontWeight.w500,
//                   color: Colors.black
//                 )
//  ),
//               SizedBox(height: 24,),
//               Container(
//                   padding: EdgeInsets.all(16),
//                   child: ElevatedButton(
//                     onPressed: () async{
//                       await ask(context);
//                     }, 
//                      child: Text('Allow permissions'),
//                     // height: 56,
//                     // isLoading: false,
//                     // isEnabled: true,
//                    
                    
//                   )),
//               SizedBox(height: 32,)
//             ],
//           ),
        
//     );
//   }

//   Future<void> ask(context) async{
//     await PermissionAccess().requestNotificationsPermissions();
//     await PermissionAccess().requestStoragePermission();
//     Navigator.push(context, MaterialPageRoute(builder:  (context) {
  
//       return OnBoarding();
//     },));
//  }
// }