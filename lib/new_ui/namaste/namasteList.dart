import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/models/greet.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/bottomSheetIDCard.dart';
import 'package:mla_connect/widgets/idCard.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class GreetList extends StatefulWidget {
  const GreetList({Key? key}) : super(key: key);

  @override
  State<GreetList> createState() => _GreetListState();
}

class _GreetListState extends State<GreetList> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
  }

  bool loading = true;
  GreetPeopleList? result;
  getData() async {
    result = await FirebaseCloud.instance.fetchGreetedMembers();
     //print(result);
    setState(() {
      loading = false;
    });
  }

  final cardKey = GlobalKey();
  showIdCard(GreetPeopleData data) {
    showDialog(
        context: context,
        builder: ((context) {
          return Material(
            type: MaterialType.transparency,
            elevation: 3,
            child: Center(
                child: Wrap(
              children: [
                Container(
                    color: Colors.white,
                    margin: EdgeInsets.all(20),
                    padding: EdgeInsets.all(10),
                    child: Column(children: [
                      IDCard(
                          cardKey: cardKey,
                          position: "Karyakarta",
                          profilePicUrl: data.profileUrl,
                          name: data.name ,dob: "",
                          // netaprofilePicUrl: "",
                          number: "",
                          ),
                      ElevatedButton(
                        onPressed: () async {
                          await shareIdCard(cardKey, context);
                        },
                        child: Text('Share'),
                      ),
                    ])),
              ],
            )),
          );
        }));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text("People Greeted You"),
        elevation: 0,
        
      ),
      body: loading
          ? Center(
              child: PI1(),
            )
          : ListView.separated(
              itemBuilder: (context, index) {
                return ListTile(
                  onTap: () {
                    showIdCard(result!.data[index]);
                  },
                  title: Text(result!.data[index].name),
                  subtitle: Text(
                      "Points - " + result!.data[index].totalPoints.toString()),
                  trailing: Text(
                    "🙏",
                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 30),
                  ),
                  leading: ClipOval(
                      child: CachedNetworkImage(
                          placeholder: (context, url) =>
                              CircularProgressIndicator(),
                          imageUrl: result!.data[index].profileUrl)),
                );
              },
              separatorBuilder: (context, index) => Divider(),
              itemCount: result!.data.length),
    );
  }
}
