import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/new_ui/analytics/analytics.dart';
import 'package:mla_connect/widgets/custom_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class PBCharts extends StatefulWidget {
  var aaggregatedDetails;
  PBCharts({Key? key, required this.aaggregatedDetails}) : super(key: key);

  @override
  State<PBCharts> createState() => _PBChartsState();
}

class _PBChartsState extends State<PBCharts> {
  LoginService login = LoginService();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchNetaAna();
  }

  var netaAna;
  Map partyBifurcation = {};
  Map lastpartyBifurcation = {};
  List<SalesData> chartData = [];
     var percentage ;
      var beneficiarypercentage ;
  fetchNetaAna() async {
    partyBifurcation = 
    // {
    //   "INC": 30,
    //   "BJP": 600,
    //   'NCP-Sharad Pawar': 20,
    //   "NCP- Ajit Pawar": 120,
    //   "Shiv Sena-Uddhav Thackeray": 1200,
    //   "Shiv Sena- Eknath Shinde": 99,
    //   "AIMIM": 100,
    //   "VBA": 200,
    //   "SP": 300,
    //   "BSP": 400,
    //   "Other": 20
    // };
    widget.aaggregatedDetails["partyBifurcation"] ;
    lastpartyBifurcation = widget.aaggregatedDetails["lastPartyBifurcation"] ;
    var totalPB;
   
    final Map<String, int> partyBifurcationConverted =
        Map<String, int>.fromEntries(
      partyBifurcation.entries
          .map((entry) => MapEntry(entry.key.toString(), entry.value as int)),
    );

// Convert the partyBifurcation map into a dynamic list of SalesData
    chartData = partyBifurcationConverted.entries
        .map((entry) => SalesData(entry.key, entry.value))
        .toList();

    int totalValues = 0;
    partyBifurcationConverted.forEach((key, value) {
      totalValues += value;
      chartData.add(SalesData(
          "Total", widget.aaggregatedDetails["totalCount"] - totalValues));
    });
       percentage =(widget.aaggregatedDetails["totalCount"] != 0) ? (widget.aaggregatedDetails["edited"] / widget.aaggregatedDetails["totalCount"]) * 100 : 0;
           beneficiarypercentage =(widget.aaggregatedDetails["totalCount"] != 0) ? (widget.aaggregatedDetails["beneficiary"] / widget.aaggregatedDetails["totalCount"]) * 100 : 0;

    //  //print(partyBifurcation);
    //  //print(chartData);
    //  //print("total number of PB ");
    //  //print(totalValues);
    //  //print(widget.aaggregatedDetails["totalCount"] - totalValues);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          appBar: AppBar(title: Text("Analytics", style: TextStyle(color: Colors.white),),centerTitle: true,elevation: 0, backgroundColor: Color(0xff4C3AB4),),
        body: widget.aaggregatedDetails == null
            ? Center(child: CircularProgressIndicator())
            : Padding(
                padding: const EdgeInsets.all(10),
                child: SingleChildScrollView(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // CustomProgressBar(progressNo:widget.aaggregatedDetails["totalCount"] ,totalNumber:widget.aaggregatedDetails["beneficiary"] ) ,
                        // SizedBox(height: 20,),Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.aaggregatedDetails["totalCount"], beneficiaryCount:widget.aaggregatedDetails["beneficiary"],  firstname: 'Beneficiary',secondname: 'Non-beneficiary')) ,
                        SizedBox(
                          height: 20,
                        ),
                          Container(
                        width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(10),
                              // border: Border.all(color: Colors.grey)
                              ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                      //          SizedBox(
                      //   height: 20,
                      // ),
                      Text(
                        "Booth Mapping Completed",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-SemiBold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                            // Text("Total voter : ${kkAnalytics["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                            // Text("Mapped voter  : ${kkAnalytics["edited"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                              Row(
                                children: [
                                  Text( "${percentage.toStringAsFixed(2) ??'0'}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight:  FontWeight.bold),),
                                  
                                     CustomLinearProgressIndicator(
                                      totalNumber:widget.aaggregatedDetails["totalCount"],
                                      actualNumber: widget.aaggregatedDetails["edited"]),
                                ],
                              )
                          ],)),
                        // Text(
                        //   "Booth Mapping Completed",
                        //   style:  TextStyle(fontFamily: 'Gilroy',
                        //     fontSize: 20,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black,
                        //   ),
                        // ),
                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //     padding: const EdgeInsets.all(10),
                        //     decoration: BoxDecoration(
                        //         borderRadius: BorderRadius.circular(20),
                        //         border: Border.all(color: Colors.grey)),
                        //     child:Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       Text("Total voter : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       Text("Mapped voter  : ${widget.aaggregatedDetails["edited"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       Text("Mapping percentage : ${percentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),)
                        //     ],)
                            //  CustomLinearProgressIndicator(
                            //     totalNumber:
                            //         widget.aaggregatedDetails["totalCount"],
                            //     actualNumber:
                            //         widget.aaggregatedDetails["edited"])
                                    // ),
                        SizedBox(
                          height: 10,
                        ),
                        Container(
                        decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: Color(0xffF6F6F6))),
                        child: Column(
                          crossAxisAlignment:  CrossAxisAlignment.start,
                          children: [
                             SizedBox(
                        height: 20,
                      ),
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: Text(
                        "Party Bifurcation",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                            ),
                            SizedBox(
                              
                              height:( MediaQuery.of(context).size.height * 0.1)* partyBifurcation.length,
                              width: MediaQuery.of(context).size.width,
                              child: ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: partyBifurcation.length,
                                itemBuilder: (BuildContext context, int index) {
                                  String key =
                                      partyBifurcation.keys.elementAt(index);
                                  int value =
                                      partyBifurcation.values.elementAt(index);
                                   var    partypercentage =( widget.aaggregatedDetails["totalCount"] != 0) ? (value / widget.aaggregatedDetails["totalCount"]) * 100 : 0;
                      
                                  return ListTile(
                                    title: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(key + " : " , style: TextStyle(fontFamily: 'Gilory-Bold-Medium' , fontWeight: FontWeight.w400), ),
                                        SizedBox(height: 5,),
                                         Row(
                                           children: [
                                             Text("${partypercentage.toStringAsFixed(2)??"0"}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),),
                                            CustomLinearProgressIndicator(
                                            totalNumber:widget.aaggregatedDetails["totalCount"],
                                            actualNumber: value),],
                                         )

                                         
                                       
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ), 

                        // Text(
                        //   "Party Bifurcation",
                        //   style:  TextStyle(fontFamily: 'Gilroy',
                        //     fontSize: 20,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black,
                        //   ),
                        // ),

                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //   decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //       border: Border.all(color: Colors.grey)),
                        //   height: (MediaQuery.of(context).size.height * 0.08)*partyBifurcation.length,
                        //   width: MediaQuery.of(context).size.width,
                        //   child: ListView.builder(
                        //     physics: NeverScrollableScrollPhysics(),
                        //     itemCount: partyBifurcation.length,
                        //     itemBuilder: (BuildContext context, int index) {
                        //        //print(partyBifurcation);
                        //        //print(index);

                        //       String key =
                        //           partyBifurcation.keys.elementAt(index);
                        //       int value =
                        //           partyBifurcation.values.elementAt(index);
                        //            //print(key);
                        //            //print(value);
                        //        var    partypercentage =(widget.aaggregatedDetails["totalCount"] != 0) ? (value / widget.aaggregatedDetails["totalCount"]) * 100 : 0;

                        //       return ListTile(
                        //         title: Column(
                        //           crossAxisAlignment: CrossAxisAlignment.start,
                        //           children: [
                        //             Text(key + " : ${value}" ),
                        //             Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       // Text("Edited Number  : ${value}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       Text("percentage : ${partypercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),)
                        //     ],)
                        //             // CustomLinearProgressIndicator(
                        //             //     totalNumber: widget
                        //             //         .aaggregatedDetails["totalCount"],
                        //             //     actualNumber: value),
                        //           ],
                        //         ),
                        //       );
                        //     },
                        //   ),
                        // ),
                        
                        // SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.aaggregatedDetails["totalCount"], beneficiaryCount:widget.aaggregatedDetails["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
                        SizedBox(
                          height: 20,
                        ),
  Container(
                       decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: Color(0xffF6F6F6))),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                             SizedBox(
                          height: 10,
                        ),
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: Text(
                          "Last Voted Party Bifurcation",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        )
                             ) ),
                             SizedBox(height: 10,),
                            Container(
                             
                              height: (MediaQuery.of(context).size.height * 0.1)*lastpartyBifurcation.length,
                              width: MediaQuery.of(context).size.width,
                              child: ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                itemCount:lastpartyBifurcation.length,
                                itemBuilder: (BuildContext context, int index) {
                                  String key =
                                      lastpartyBifurcation.keys.elementAt(index);
                                  int value =
                                      lastpartyBifurcation.values.elementAt(index);
                                                                     var    lastpartypercentage =(widget.aaggregatedDetails["totalCount"] != 0) ? (value /widget.aaggregatedDetails["totalCount"]) * 100 : 0;

                                  return ListTile(
                                    title: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(key + " : ",style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                        ),
                       ),
                       SizedBox(height: 10,),
                                        Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
 
                                Text("${lastpartypercentage.toStringAsFixed(2)}%" ,  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),)
                               , CustomLinearProgressIndicator(
                                            totalNumber: 
                                               widget.aaggregatedDetails["totalCount"],
                                            actualNumber: value),
                                ],)  ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                        // Text(
                        //   "Last Party Bifurcation",
                        //   style:  TextStyle(fontFamily: 'Gilroy',
                        //     fontSize: 20,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black,
                        //   ),
                        // ),
                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //   decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //       border: Border.all(color: Colors.grey)),
                        //   height: (MediaQuery.of(context).size.height * 0.08)*lastpartyBifurcation.length,
                        //   width: MediaQuery.of(context).size.width,
                        //   child: ListView.builder(
                        //     physics: NeverScrollableScrollPhysics(),
                        //     itemCount: lastpartyBifurcation.length,
                        //     itemBuilder: (BuildContext context, int index) {
                        //        //print(lastpartyBifurcation);
                        //        //print(index);
                              
                        //       String key =
                        //           lastpartyBifurcation.keys.elementAt(index);
                        //       int value =
                        //           lastpartyBifurcation.values.elementAt(index);
                        //            //print(key);
                        //            //print(value);
                        //        var    lastpartypercentage =(widget.aaggregatedDetails["totalCount"] != 0) ? (value / widget.aaggregatedDetails["totalCount"]) * 100 : 0;

                        //       return ListTile(
                        //         title: Column(
                        //           crossAxisAlignment: CrossAxisAlignment.start,
                        //           children: [
                        //             Text(key + " : ${value}" ),
                        //             Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       // Text("Edited Number  : ${value}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       Text("percentage : ${lastpartypercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),)
                        //     ],)
                        //             // CustomLinearProgressIndicator(
                        //             //     totalNumber: widget
                        //             //         .aaggregatedDetails["totalCount"],
                        //             //     actualNumber: value),
                        //           ],
                        //         ),
                        //       );
                        //     },
                        //   ),
                        // ),
                        SizedBox(
                          height: 10,
                        ),
                          Container(
                        width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              ),
                          child:    Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
Text(
                        "No. of Beneficiaries : ",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 10,),
                            // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                            //  Text("Beneficiary Number  : ${kkAnalytics["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                            Row(
                              children: [
                                Text("${
                                 beneficiarypercentage.toStringAsFixed(2)}%",  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),),

                                  CustomLinearProgressIndicator(
                                            totalNumber: 
                                               widget.aaggregatedDetails["totalCount"],
                                            actualNumber:widget.aaggregatedDetails["beneficiary"]),
                              ],
                            )
                          ],)),
SizedBox(height: 20,)


                        // Text(
                        //   "No. of Beneficiaries ",
                        //   style:  TextStyle(fontFamily: 'Gilroy',
                        //       fontSize: 20,
                        //       fontWeight: FontWeight.bold,
                        //       color: Colors.black),
                        // ),
                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //     padding: const EdgeInsets.all(10),
                        //     decoration: BoxDecoration(
                        //         borderRadius: BorderRadius.circular(20),
                        //         border: Border.all(color: Colors.grey)),
                        //     child:
                        //     Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //        Text("Beneficiary Number  : ${widget.aaggregatedDetails["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                        //       Text("percentage : ${beneficiarypercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),)
                        //     ],)
                        //     //  CustomLinearProgressIndicator(
                        //     //     totalNumber:
                        //     //         widget.aaggregatedDetails["totalCount"],
                        //     //     actualNumber:
                        //     //         widget.aaggregatedDetails["beneficiary"])
                        //             ),

                        // Container(
                        //   // height: 300,
                        //   // width: 300,
                        // decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),
                        //   child: SfCartesianChart(
                        //     primaryXAxis: CategoryAxis(),
                        //     series: <ChartSeries>[
                        //       ColumnSeries<SalesData, String>(
                        //         dataSource: chartData,
                        //         xValueMapper: (SalesData sales, _) => sales.category,
                        //         yValueMapper: (SalesData sales, _) => sales.sales,
                        //         dataLabelSettings: DataLabelSettings(isVisible: true),
                        //       ),

                        //     ],
                        //   ),
                        // ),
                      ]),
                ),
              ),
      ),
    );
  }
}

class CustomLinearProgressIndicator extends StatelessWidget {
  final int totalNumber;
  final int actualNumber;

  CustomLinearProgressIndicator(
      {required this.totalNumber, required this.actualNumber});

  @override
  Widget build(BuildContext context) {
    double percentage = (actualNumber / totalNumber) * 100;

    return Container(
      width: MediaQuery.of(context).size.width*0.6,
      margin: EdgeInsets.only(left: 10),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
      child: Stack(
        alignment: Alignment.center,
        children: [
          LinearProgressIndicator(
            value: percentage / 100,
            minHeight: 17,
            backgroundColor: Color(0xffD9D9D9),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
          ),
          Text(
            '${percentage.toStringAsFixed(2)}%',
            style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
          )
        ],
      ),
    );
  }

  _color(double percentage) {
    if (percentage <= 30) {
      return Colors.red;
    } else if (percentage >= 30 && percentage <= 60) {
      return Colors.yellow;
    } else {
      return Colors.green;
    }
  }
}
