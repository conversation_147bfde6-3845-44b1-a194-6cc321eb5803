import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mla_connect/controller/bottomBarController.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/new_ui/analytics/overall_analytics.dart';
import 'package:mla_connect/widgets/kk_ana.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../karyakarta/karyakarta_leaderboard.dart';

class Analytics extends StatefulWidget {
  const Analytics({Key? key}) : super(key: key);

  @override
  State<Analytics> createState() => _AnalyticsState();
}

class _AnalyticsState extends State<Analytics> {
 @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchNetaAna();
  }
  LoginService login =LoginService() ;
  String? selectedValue;
  var boothSpread ;
var netaAna;
var kk ;
var aaggregatedDetails;
var kkleaderboard ;
BottomBarController bottomBarController= Get.find();
 List<Map<String, dynamic>> nameAndBoothIdList =[] ;
  fetchNetaAna()async{
var res =await  login.netaAnalytics(context) ;
  
 netaAna = res['res'] ;

 if(res['status'] == 403) {
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
 }
boothSpread=netaAna["boothSpreadList"];
selectedValue = boothSpread.keys.first;
 //print(bottomBarController.userLeaderBoard);
var ress = await login.karyakartaList(context);
 kk =ress['res'] ;
  //print(kk);
 if(ress['status'] ==403) {
 showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
 }
    //print("karyakartalist  ${kk['users']}");
   
aaggregatedDetails =  netaAna["aggregatedDetails"] ;
kkleaderboard = netaAna["aggregatedDetails"]['pointsLeaderBoard'] ;
setState(() {
  
});
  }
  bool party =false ;
  bool benefit =false ;
  int selectedBoothId =2;
  List<String> _items = ['All' ];
  String _selectedItem="All";

  
  @override
  Widget build(BuildContext context) {
         fetchNetaAna();
    return Scaffold( appBar: AppBar(title: Text("Analytics" ,style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',color: Colors.white),),centerTitle: true,elevation: 0,backgroundColor:  
      Color(0xff4C3AB4) , // Change the color here
  ) ,
      body:netaAna ==null ? Center(child: CircularProgressIndicator()): 
     SingleChildScrollView(
       child: Column(children: [
        Container(
          width: double.infinity,
             margin: EdgeInsets.only(left: 20 ,right: 20,top: 10),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5),color: Color.fromARGB(255,248,244,244),border: Border.all(color: Color(0xffEBEBEB)) ),
          padding: EdgeInsets.all(24.0),
          // Adjust the color to match the exact hue
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
         Text("OverAll Analytics",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,fontWeight: FontWeight.bold,),) ,
      SizedBox(height: 20,) ,  Row(
         mainAxisAlignment: MainAxisAlignment.spaceAround,
           children: [
             Container(
              width: 100,
              padding: EdgeInsets.only(left: 10 ,right: 10),
              decoration: BoxDecoration( color: Colors.white ,borderRadius: BorderRadius.circular(5),),
              child: DropdownButton<String>(
                 value: _selectedItem,
                isExpanded: true,
                underline: SizedBox(),
                   iconDisabledColor: Colors.white,
                icon: SvgPicture.asset('assets/icons/dropdown.svg') ,
                 items: _items .map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
                 onChanged: (String? value) { // Specify the type of value as String?
                 setState(() {
                   _selectedItem = value!;
                 });
                 },
               ),
             ),
             
              SizedBox(
                 height: MediaQuery.of(context).size.height*0.055,
                width:  MediaQuery.of(context).size.height*0.14,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
    backgroundColor: Color(0xff4C3AB4) , // Change the color here
  ),
                  onPressed: () async {
     
                    if(selectedValue!= null){

                    
                    Navigator.push(context, MaterialPageRoute(builder:  (context) {
                      return PBCharts(aaggregatedDetails :aaggregatedDetails );
                    },));
                    }else {
                       final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('select Booth Number'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
      
                  },
                  child: Text('Submit', style: TextStyle(color: Colors.white),),
                ),
              ),
           ],
         ),
         
              ],
          ),
        ) ,
        SizedBox(height: 20,),
         Container(
          width: double.infinity,
             margin: EdgeInsets.only(left: 20 ,right: 20),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5),color: Color.fromARGB(255,248,244,244),border: Border.all(color: Color(0xffEBEBEB)) ),
          padding: EdgeInsets.all(24.0),
          // Adjust the color to match the exact hue
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
        
         Text("Select Booth Number",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,fontWeight: FontWeight.bold),) ,
     SizedBox(height: 20,) ,   Row(
         mainAxisAlignment: MainAxisAlignment.spaceAround,
           children: [
             Container(
              width: 100,
              padding: EdgeInsets.only(left: 10 ,right: 10),
              decoration: BoxDecoration( color: Colors.white  ,borderRadius: BorderRadius.circular(5)),
             
              
               child: DropdownButton<String>(
                 value: selectedValue,
                isExpanded: true,
                underline: SizedBox(),
                   iconDisabledColor: Colors.white,
                icon: SvgPicture.asset('assets/icons/dropdown.svg') ,
                 items: boothSpread.keys.map<DropdownMenuItem<String>>((String key) {
                 return DropdownMenuItem<String>(
                   value: key,
                   child: Text(key),
                 );
                 }).toList(),
                 onChanged: (String? value) { // Specify the type of value as String?
                 setState(() {
                   selectedValue = value!;
                 });
                 },
               ),
             ),
             
              SizedBox(
                 height: MediaQuery.of(context).size.height*0.055,
                width:  MediaQuery.of(context).size.height*0.14,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xff4C3AB4) , // Change the color here
             ),
                  onPressed: () async {
                    
                    if(selectedValue!= null){
                        
                        String boothId = netaAna['boothSpreadList']['1']['boothId'];
                     
                        var data = netaAna['boothAnalytics'][boothId];
                       
                    Navigator.push(context, MaterialPageRoute(builder:  (context) {
                      return NetaPBChart(netaAna:  data) ;
                    },));
                    }else {
                       final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('select Booth Number'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
      
                  },
                  child: Text('Submit', style: TextStyle(color: Colors.white),),
                ),
              ),
           ],
         ),
         
              ],
          ),
        ) ,
        SizedBox(height: 20,),
         Container(
          width: double.infinity,
             margin: EdgeInsets.only(left: 20 ,right: 20),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5),color: Color.fromARGB(255,248,244,244),border: Border.all(color: Color(0xffEBEBEB)) ),
          padding: EdgeInsets.all(24.0),
          // Adjust the color to match the exact hue
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
        
         Text(  "Karyakarta LeaderBoard",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,fontWeight: FontWeight.bold),) ,
        SizedBox(height: 20,), Row(
         mainAxisAlignment: MainAxisAlignment.spaceAround,
           children: [
             Container(
              width: 100,
              padding: EdgeInsets.only(left: 10 ,right: 10),
              decoration: BoxDecoration( color: Colors.white  ,borderRadius: BorderRadius.circular(5)),
               child: DropdownButton<String>(
                 value: _selectedItem,
                isExpanded: true,
                underline: SizedBox(),
                   iconDisabledColor: Colors.white,
                icon: SvgPicture.asset('assets/icons/dropdown.svg') ,
                 items: _items .map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
                 onChanged: (String? value) { // Specify the type of value as String?
                 setState(() {
                   _selectedItem = value!;
                 });
                 },
               ),
             ),
             
              SizedBox(
                height: MediaQuery.of(context).size.height*0.055,
                width:  MediaQuery.of(context).size.height*0.14,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
    backgroundColor: Color(0xff4C3AB4) , // Change the color here
  ),
                  onPressed: () async {
    //   //print(kkleaderboard.length);
      //print(kkleaderboard);
                    if(selectedValue!= null){
                    Navigator.push(context, MaterialPageRoute(builder:  (context) {
                      return KaryakartaLeaderBoard(kkleaderboard: kkleaderboard) ;
                    },));}else {
                       final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('somethingwent wrong!'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
      
                  },
                  child: Text('Submit', style: TextStyle(color: Colors.white),),
                ),
              ),
           ],
         ),
         
              ],
          ),
        ) , SizedBox(height: 20,),
    //      Container(
    //       width: double.infinity,
    //          margin: EdgeInsets.only(left: 20 ,right: 20),
    //         decoration: BoxDecoration(borderRadius: BorderRadius.circular(10),color: Colors.white, ),
    //       padding: EdgeInsets.all(24.0),
    //       // Adjust the color to match the exact hue
    //       child: Column(
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
        
    //      Text(  "User LeaderBoard",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,fontWeight: FontWeight.bold),) ,
    //      Row(
    //      mainAxisAlignment: MainAxisAlignment.spaceAround,
    //        children: [
    //          Container(
    //           width: 100,
    //           padding: EdgeInsets.only(left: 10 ,right: 10),
    //           decoration: BoxDecoration( color: Colors.white, border: Border.all(color: Colors.grey ,) ,borderRadius: BorderRadius.circular(20)),
             
              
    //            child: DropdownButton<String>(
    //              value: _selectedItem,
    //             isExpanded: true,
    //             underline: SizedBox(),
    //              items: _items .map((String item) {
    //           return DropdownMenuItem<String>(
    //             value: item,
    //             child: Text(item),
    //           );
    //         }).toList(),
    //              onChanged: (String? value) { // Specify the type of value as String?
    //              setState(() {
    //                _selectedItem = value!;
    //              });
    //              },
    //            ),
    //          ),
             
    //           Padding(
    //                 padding: const EdgeInsets.symmetric(vertical:MediaQuery.of(context).size.height*0.023.0),
    //                 child: ElevatedButton(
    //                   onPressed: () async {
    // //   //print(kkleaderboard.length);
    //   //print(kkleaderboard);
    //                     if(selectedValue!= null){
                          
    //                     // Navigator.push(context, MaterialPageRoute(builder:  (context) {
    //                     //   return KaryakartaLeaderBoard(kkleaderboard: bottomBarController.userLeaderBoard) ;
    //                     // },));
    //                     }else {
    //                        final snackBar = SnackBar(
    //         behavior: SnackBarBehavior.floating,
    //         margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
    //         content: const Text('somethingwent wrong!'),
    //       );
    //       ScaffoldMessenger.of(context).showSnackBar(snackBar);
    //                     }
      
    //                   },
    //                   child: Text('Submit'),
    //                 ),
    //               ),
    //        ],
    //      ),
         
    //           ],
    //       ),
    //     ) ,
    //     SizedBox(height: 20,),
        // Container(   
        //      margin: EdgeInsets.only(left: 20 ,right: 20),
        //     decoration: BoxDecoration(borderRadius: BorderRadius.circular(10),color: Colors.white, ),
        //   padding: EdgeInsets.all(24.0),
        //   // Adjust the color to match the exact hue
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        
        //  Text(  "Select karyakarta name for analytics",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,fontWeight: FontWeight.bold),) ,
        //  SizedBox(height: 20,),
        //  SizedBox(
        //   height:(MediaQuery.of(context).size.height*0.09)*kk['users'].length,
        //    child: ListView.builder(
        //     itemCount: kk['users'].length,
        //     itemBuilder: (context, index) {
      
        //       return GestureDetector(
        //         onTap: () {
        //             Navigator.push(context, MaterialPageRoute(builder:  (context) {
        //                 return PBChart(boothNumber:  kk['users'][index]['boothId']!) ;
        //               },));
        //         },
        //         child: ListTile(
        //           title: Container( 
        //             padding: EdgeInsets.only(top: 10 ,bottom: 10),
        //             alignment: Alignment.center,
        //             decoration: BoxDecoration(
        //               color: Colors.white,
        //               border: Border.all(
        //                 color: Colors.grey, // Border color
        //                 width: 1.0, // Border width
        //               ),
        //               borderRadius:
        //                   BorderRadius.circular(30), // Optional: border radius
        //             ),
                   
        //             child: Column(
        //               children: [
              
        //                 // CachedNetworkImage(
        //                 //   imageUrl: kk['users'][index]['profileImgUrl'],
        //                 //   // imageBuilder: (context, imageProvider) => Container(child: ,),
        //                 //   // progressIndicatorBuilder: (context, url, downloadProgress) =>
        //                 //   //     CircularProgressIndicator(value: downloadProgress.progress),
        //                 //   errorWidget: (context, url, error) =>
        //                 //       Icon(Icons.error),
        //                 //   width: 50,
        //                 //   fit: BoxFit.fill,
        //                 // ),
        //                 // SizedBox(
        //                 //   width: 30,
        //                 // ),
        //                 Text('Name: ${kk['users'][index]['name'].toString()}'),
        //                 SizedBox(
        //                   height: 3,
        //                 ),
        //                 // Text('Number: ${kk['users'][index]['number']}'),
        //                 SizedBox(
        //                   height: 3,
        //                 ),
        //                 Text('Booth No : ${kk['users'][index]['boothId']}'),
                           
                         
                                
        //               ],
        //             ),
        //           ),
        //         ),
        //       );
        //     },
        //     ),
        //  ),
         
          
         
        //       ],
        //   ),
        // ) ,
          
       ],),
     ) ,
     
      );
  }

   
  int getBoothId(String name) {
    for (var item in nameAndBoothIdList) {
      if (item['name'] == name) {
        return item['boothId'];
      }
    }
    return -1;
  }
   
} 


class PBChart extends StatefulWidget {
  String boothNumber ;
    PBChart({Key? key ,required this.boothNumber}) : super(key: key);

  @override
  State<PBChart> createState() => _PBChartState();
}
 
class _PBChartState extends State<PBChart> {
LoginService login =LoginService() ;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
   fetchNetaAna();
  }
var netaAna;
  List<SalesData> chartData =[];
    Map partyBifurcation={} ;
  fetchNetaAna()async{
var result =await  login.netaboothAnalytics(context , widget.boothNumber) ;
netaAna = result['res'] ;
 //print(netaAna);
 if(result['status'] == 403) 
 {
    showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
 }
 partyBifurcation =
  // {
  //     "INC": 30,
  //     "BJP": 600,
  //     'NCP-Sharad Pawar': 20,
  //     "NCP- Ajit Pawar": 120,
  //     "Shiv Sena-Uddhav Thackeray": 1200,
  //     "Shiv Sena- Eknath Shinde": 99,
  //     "AIMIM": 100,
  //     "VBA": 200,
  //     "SP": 300,
  //     "BSP": 400,
  //     "Other": 20
  //   };
 netaAna["partyBifurcation"] ;
var totalPB;
 final Map<String, int> partyBifurcationConverted = Map<String, int>.fromEntries(
  partyBifurcation.entries.map((entry) => MapEntry(entry.key.toString(), entry.value as int)),
 
);
 
  chartData = partyBifurcationConverted.entries
    .map((entry) => SalesData(entry.key, entry.value))
    .toList();
    
    int totalValues = 0;
partyBifurcationConverted.forEach((key, value) {
  totalValues += value;
  chartData.add( SalesData("Total",  netaAna["totalCount"]-totalValues) ) ;
});
 
setState(() {
  
});}


  @override
  Widget build(BuildContext context) {

    return SafeArea(
      child: Scaffold(
       
        appBar: AppBar(title: Text("Analytics" ,style:  TextStyle(fontFamily: 'Gilory-Bold',color: Colors.white),),centerTitle: true,elevation: 0,backgroundColor:  
      Color(0xff4C3AB4) , // Change the color here
  ) ,
        body: netaAna==null ? Center(child: CircularProgressIndicator( )): Padding(
        padding: const EdgeInsets.all(10),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center
            ,
            children: [
                SizedBox(
                          height: 20,
                        ),
                        Text(
                          "Booth Mapping Completed",
                          style:  TextStyle(fontFamily: 'Gilory-Bold',
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                       netaAna['totalCount']==0 ? Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey)),
                            child: CustomLinearProgressIndicator(
                                totalNumber:
                                   1,
                                actualNumber:netaAna['beneficiary'] ,
                                )) :  Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey)),
                            child: CustomLinearProgressIndicator(
                                totalNumber:
                                     netaAna['totalCount'],
                                actualNumber: 
                                    netaAna["edited"]
                                    )),
                        SizedBox(
                          height: 10,
                        ),
                        Divider(
                          height: 10,
                          color: Colors.grey,
                        ),
                        // SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.netaAna["totalCount"], beneficiaryCount:widget.aaggregatedDetails["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
                        SizedBox(
                          height: 20,
                        ),

                        Text(
                          "Party Bifurcation",
                          style:  TextStyle(fontFamily: 'Gilory-Bold',
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      partyBifurcation.length ==  0 ?Text("no Work") : Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: Colors.grey)),
                          height: (MediaQuery.of(context).size.height * 0.1)*partyBifurcation.length,
                          width: MediaQuery.of(context).size.width,
                          child: ListView.builder(
                            shrinkWrap: true, // Adjusts the size based on content
    
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: partyBifurcation.length,
                            itemBuilder: (BuildContext context, int index) {

                              String key =
                                  partyBifurcation.keys.elementAt(index);
                              int value =
                                  partyBifurcation.values.elementAt(index);
                              return ListTile(
                                title: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(key + " : "),
                                    CustomLinearProgressIndicator(
                                        totalNumber: 1499,
                                        actualNumber: value),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Divider(
                          height: 10,
                          color: Colors.grey,
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        Text(
                          "No. of Beneficiaries ",
                          style:  TextStyle(fontFamily: 'Gilory-Bold',
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      netaAna['totalCount']==0 ?Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey)),
                            child: CustomLinearProgressIndicator(
                                totalNumber:
                                   1,
                                actualNumber:netaAna['beneficiary'] ,
                                )) :  Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey)),
                            child: CustomLinearProgressIndicator(
                                totalNumber:
                                  netaAna['totalCount'],
                                actualNumber:netaAna['beneficiary'] ,
                                )),
      //         SizedBox(
      // height: 110,
      //  child: ListView.builder(
      //   itemCount:  netaAna["pointsLeaderBoard"].length,
      //   itemBuilder: (context, index) {
 
      //     return ListTile(
      //       title: Container( 
      //         padding: EdgeInsets.all(20),
      //         // alignment: Alignment.topLeft,
      //         decoration: BoxDecoration(
      //           color: Colors.white,
      //           border: Border.all(
      //             color: Colors.grey, // Border color
      //             width: 1.0, // Border width
      //           ),
      //           borderRadius:
      //               BorderRadius.circular(30), // Optional: border radius
      //         ),
             
      //         child: Row( 
      //           crossAxisAlignment: CrossAxisAlignment.center,
      //           mainAxisAlignment: MainAxisAlignment.start,
      //           children: [
          
      //             CachedNetworkImage(
      //               imageUrl: netaAna["pointsLeaderBoard"][index]['profileImgUrl'],
      //               // imageBuilder: (context, imageProvider) => Container(child: ,),
      //               // progressIndicatorBuilder: (context, url, downloadProgress) =>
      //               //     CircularProgressIndicator(value: downloadProgress.progress),
      //               errorWidget: (context, url, error) =>
      //                   Icon(Icons.error),
      //               width: 50,
      //               fit: BoxFit.fill,
      //             ),
      //             // SizedBox(
      //             //   width: 30,
      //             // ),
      //         SizedBox(width: 20,),   
      //              Column(
      //                 crossAxisAlignment: CrossAxisAlignment.start,
      //           mainAxisAlignment: MainAxisAlignment.start,
      //               children: [  Text('Name: ${netaAna["pointsLeaderBoard"][index]['name'].toString()}'),
      //             SizedBox(
      //               height: 3,
      //             ),
      //             // Text('Number: ${kk['users'][index]['number']}'),
      //             SizedBox(
      //               height: 3,
      //             ),
      //               Text('Total Points : ${netaAna["pointsLeaderBoard"][index]['count'].toString()}'),],
      //              ) ],
      //         ),
      //       ),
      //     );
      //   },
      //   ),
      //  ),
             

        //     Text("Beneficiary",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 30 ,fontWeight: FontWeight.bold  ,color: Colors.black,letterSpacing: 5),),
        //     SizedBox(height: 20,),Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount:netaAna["totalCount"] , beneficiaryCount:netaAna["beneficiary"],  firstname: 'Beneficiary',secondname: 'Non-beneficiary')) ,
        //    SizedBox(height: 20,), Text("Profile Edit",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 30 ,fontWeight: FontWeight.bold  ,color:  Colors.black,letterSpacing: 5),),
        //     SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: netaAna["totalCount"], beneficiaryCount:netaAna["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
        //  SizedBox(height: 20,),
            
        //          Text("Party Bifurcation",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 30 ,fontWeight: FontWeight.bold  ,color:  Colors.black,letterSpacing: 5),),
        //         SizedBox(height: 20),
        //         Container(
        //           // height: 300,
        //           // width: 300,
        //         decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),
        //           child: SfCartesianChart(
        //             primaryXAxis: CategoryAxis(),
        //             series: <ChartSeries>[
        //               ColumnSeries<SalesData, String>(
        //                 dataSource: chartData,
        //                 xValueMapper: (SalesData sales, _) => sales.category,
        //                 yValueMapper: (SalesData sales, _) => sales.sales,
        //                 dataLabelSettings: DataLabelSettings(isVisible: true),
        //               ),
                      
        //             ],
        //           ),
        //         ),
           ]),
        ),
      ),),
    );
  }
}

class NetaPBChart extends StatefulWidget {
 var netaAna ;
    NetaPBChart({Key? key ,required this.netaAna}) : super(key: key);

  @override
  State<NetaPBChart> createState() => _NetaPBChartState();
}
 
class _NetaPBChartState extends State<NetaPBChart> {
LoginService login =LoginService() ;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
   fetchNetaAna();
  }
 
  List<SalesData> chartData =[];
    Map partyBifurcation  ={};
    var percentage ;
    var beneficiarypercentage ;
  fetchNetaAna()async{
// netaAna =await  login.netaboothAnalytics(context , widget.boothNumber) ;
  //print(widget.netaAna);
  partyBifurcation =
  //  {
  //     "INC": 30,
  //     "BJP": 600,
  //     'NCP-Sharad Pawar': 20,
  //     "NCP- Ajit Pawar": 120,
  //     "Shiv Sena-Uddhav Thackeray": 1200,
  //     "Shiv Sena- Eknath Shinde": 99,
  //     "AIMIM": 100,
  //     "VBA": 200,
  //     "SP": 300,
  //     "BSP": 400,
  //     "Other": 20
  //   };
   widget.netaAna["partyBifurcation"] ;
var totalPB;

 final Map<String, int> partyBifurcationConverted = Map<String, int>.fromEntries(
  partyBifurcation.entries.map((entry) => MapEntry(entry.key.toString(), entry.value as int)),
 
);


// Convert the partyBifurcation map into a dynamic list of SalesData
  chartData = partyBifurcationConverted.entries
    .map((entry) => SalesData(entry.key, entry.value))
    .toList();
    
    int totalValues = 0;
partyBifurcationConverted.forEach((key, value) {
  totalValues += value;
  chartData.add( SalesData("Total",   widget.netaAna["totalCount"]-totalValues) ) ;
});
 percentage =(widget.netaAna["totalCount"] != 0) ? (widget.netaAna["edited"] / widget.netaAna["totalCount"]) * 100 : 0;
           beneficiarypercentage =(widget.netaAna["totalCount"] != 0) ? ( widget.netaAna["beneficiary"]/ widget.netaAna["totalCount"]) * 100 : 0;

 //print(chartData);
 //print("total number of PB ");
     //print(totalValues);
 //print( widget.netaAna["totalCount"]-totalValues);
setState(() {
  
});}


  @override
  Widget build(BuildContext context) {

    return SafeArea(
      child: Scaffold(
       
           appBar: AppBar(title: Text("Analytics"),centerTitle: true,elevation: 0, backgroundColor: Color(0xff4C3AB4),),
        body:  widget.netaAna==null ? Center(child: CircularProgressIndicator( )): Padding(
        padding: const EdgeInsets.all(10),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center
            ,
            children: [
              SingleChildScrollView(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // CustomProgressBar(progressNo:widget.netaAna["totalCount"] ,totalNumber:widget.aaggregatedDetails["beneficiary"] ) ,
                        // SizedBox(height: 20,),Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.netaAna["totalCount"], beneficiaryCount:widget.aaggregatedDetails["beneficiary"],  firstname: 'Beneficiary',secondname: 'Non-beneficiary')) ,
                        SizedBox(
                          height: 20,
                        ),
                         Container(
                        width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(10),
                              // border: Border.all(color: Colors.grey)
                              ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                      //          SizedBox(
                      //   height: 20,
                      // ),
                      Text(
                        "Booth Mapping Completed",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-SemiBold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                            // Text("Total voter : ${kkAnalytics["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            // Text("Mapped voter  : ${kkAnalytics["edited"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                              Row(
                                children: [
                                  Text( "${percentage.toStringAsFixed(2) ??'0'}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight:  FontWeight.bold),),
                                  
                                     CustomLinearProgressIndicator(
                                      totalNumber:widget.netaAna["totalCount"],
                                      actualNumber:widget.netaAna["edited"]),
                                ],
                              )
                          ],)),
                        // Text(
                        //   "Booth Mapping Completed",
                        //   style:  TextStyle(fontFamily: 'Gilory-Bold',
                        //     fontSize: 20,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black,
                        //   ),
                        // ),
                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //     padding: const EdgeInsets.all(10),
                        //     decoration: BoxDecoration(
                        //         borderRadius: BorderRadius.circular(20),
                        //         border: Border.all(color: Colors.grey)),
                        //     child: Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       Text("Total voter : ${widget.netaAna["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       Text("Mapped voter  : ${widget.netaAna["edited"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       Text("Mapping percentage : ${percentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),)
                        //     ],)
                        //     // CustomLinearProgressIndicator(
                        //     //     totalNumber:
                        //     //         widget.netaAna["totalCount"],
                        //     //     actualNumber:
                        //     //        widget.netaAna["edited"])
                        //            ),
                        SizedBox(
                          height: 10,
                        ),
                         Container(
                        decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: Color(0xffF6F6F6))),
                        child: Column(
                          crossAxisAlignment:  CrossAxisAlignment.start,
                          children: [
                             SizedBox(
                        height: 20,
                      ),
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: Text(
                        "Party Bifurcation",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                            ),
                            SizedBox(
                              
                              height:( MediaQuery.of(context).size.height * 0.08 )* partyBifurcation.length,
                              width: MediaQuery.of(context).size.width,
                              child: ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: partyBifurcation.length,
                                itemBuilder: (BuildContext context, int index) {
                                  String key =
                                      partyBifurcation.keys.elementAt(index);
                                  int value =
                                      partyBifurcation.values.elementAt(index);
                                   var    partypercentage =(widget.netaAna["totalCount"] != 0) ? (value / widget.netaAna["totalCount"]) * 100 : 0;
                      
                                  return ListTile(
                                    title: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(key + " : " , style: TextStyle(fontFamily: 'Gilory-Bold-Medium' , fontWeight: FontWeight.w400), ),
                                        SizedBox(height: 5,),
                                         Row(
                                           children: [
                                             Text("${partypercentage.toStringAsFixed(2)??"0"}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),),
                                            CustomLinearProgressIndicator(
                                            totalNumber:widget.netaAna["totalCount"],
                                            actualNumber: value),],
                                         )

                                         
                                       
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ), 

                        // Text(
                        //   "Party Bifurcation",
                        //   style:  TextStyle(fontFamily: 'Gilory-Bold',
                        //     fontSize: 20,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black,
                        //   ),
                        // ),

                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //   decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //       border: Border.all(color: Colors.grey)),
                        //   height: (MediaQuery.of(context).size.height * 0.08)*partyBifurcation.length,
                        //   width: MediaQuery.of(context).size.width,
                        //   child: ListView.builder(
                        //     physics: NeverScrollableScrollPhysics(),
                        //     itemCount: partyBifurcation.length,
                        //     itemBuilder: (BuildContext context, int index) {
                        //       String key =
                        //           partyBifurcation.keys.elementAt(index);
                        //       int value =
                        //           partyBifurcation.values.elementAt(index);
                        //                                          var    partypercentage =(widget.netaAna["totalCount"] != 0) ? (value / widget.netaAna["totalCount"]) * 100 : 0;

                        //       return ListTile(
                        //         title: Column(
                        //           crossAxisAlignment: CrossAxisAlignment.start,
                        //           children: [
                        //              Text(key + " : ${value}" ),
                        //             Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       // Text("Edited Number  : ${value}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       Text("percentage : ${partypercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),)
                        //     ],)])) ;
                              
                        //     },
                        //   ),
                        // ),
                        SizedBox(
                          height: 10,
                        ),
                        // Divider(
                        //   height: 10,
                        //   color: Colors.grey,
                        // ),
                         Container(
                       decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: Color(0xffF6F6F6))),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                             SizedBox(
                          height: 10,
                        ),
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: Text(
                          "Last Voted Party Bifurcation",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        )
                             ) ),
                             SizedBox(height: 10,),
                            Container(
                             
                              height: (MediaQuery.of(context).size.height * 0.08)*widget.netaAna['lastPartyBifurcation'].length,
                              width: MediaQuery.of(context).size.width,
                              child: ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                itemCount:widget.netaAna['lastPartyBifurcation'].length,
                                itemBuilder: (BuildContext context, int index) {
                                  String key =
                                      widget.netaAna['lastPartyBifurcation'].keys.elementAt(index);
                                  int value =
                                      widget.netaAna['lastPartyBifurcation'].values.elementAt(index);
                                                                     var    lastpartypercentage =(widget.netaAna["totalCount"] != 0) ? (value /widget.netaAna["totalCount"]) * 100 : 0;

                                  return ListTile(
                                    title: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(key + " : ",style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                        ),
                       ),
                       SizedBox(height: 10,),
                                        Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
 
                                Text("${lastpartypercentage.toStringAsFixed(2)}%" ,  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),)
                               , CustomLinearProgressIndicator(
                                            totalNumber: 
                                              widget.netaAna["totalCount"],
                                            actualNumber: value),
                                ],)  ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                        //  Text(
                        //   "Responsibility taken by ",
                        //   style:  TextStyle(fontFamily: 'Gilory-Bold',
                        //     fontSize: 20,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black,
                        //   ),
                        // ),

                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //   decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //       border: Border.all(color: Colors.grey)),
                        //   height: (MediaQuery.of(context).size.height *0.08)*widget.netaAna['lastPartyBifurcation'].length,
                        //   width: MediaQuery.of(context).size.width,
                        //   child: ListView.builder(shrinkWrap: true,
                        //     physics: NeverScrollableScrollPhysics(),
                        //     itemCount: widget.netaAna['lastPartyBifurcation'].length,
                        //     itemBuilder: (BuildContext context, int index) {
                        //       String key =
                        //           widget.netaAna['lastPartyBifurcation'].keys.elementAt(index);
                        //       int value =
                        //           widget.netaAna['lastPartyBifurcation'].values.elementAt(index);
                        //                                          var    lastpartypercentage =(widget.netaAna["totalCount"] != 0) ? (value / widget.netaAna["totalCount"]) * 100 : 0;

                        //       return ListTile(
                        //         title: Column(
                        //           crossAxisAlignment: CrossAxisAlignment.start,
                        //           children: [
                        //             Text(key + " : ${value}" ),
                        //             Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       // Text("Edited Number  : ${value}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       Text("percentage : ${lastpartypercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),)
                        //     ],)])
                        //       );
                        //     },
                        //   ),
                        // ),
                        SizedBox(
                          height: 20,
                        ),
                         Container(
                        width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              ),
                          child:    Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
Text(
                        "No. of Beneficiaries : ",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 10,),
                            // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            //  Text("Beneficiary Number  : ${kkAnalytics["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            Row(
                              children: [
                                Text("${
                                 beneficiarypercentage.toStringAsFixed(2)}%",  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),),

                                  CustomLinearProgressIndicator(
                                            totalNumber: 
                                             widget.netaAna["totalCount"],
                                            actualNumber:widget.netaAna["beneficiary"]),
                              ],
                            )
                          ],)),
                        // Text(
                        //   "No. of Beneficiaries ",
                        //   style:  TextStyle(fontFamily: 'Gilory-Bold',
                        //       fontSize: 20,
                        //       fontWeight: FontWeight.bold,
                        //       color: Colors.black),
                        // ),
                        // SizedBox(
                        //   height: 10,
                        // ),
                        // Container(
                        //     padding: const EdgeInsets.all(10),
                        //     decoration: BoxDecoration(
                        //         borderRadius: BorderRadius.circular(20),
                        //         border: Border.all(color: Colors.grey)),
                        //     child: Column(
                        //       mainAxisAlignment: MainAxisAlignment.start,
                        //       crossAxisAlignment: CrossAxisAlignment.start,
                        //       children: [

                        //       // Text("Total : ${widget.netaAna["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //        Text("Beneficiary Number  : ${widget.netaAna["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                        //       Text("percentage : ${beneficiarypercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),)
                        //     ],)
                            // CustomLinearProgressIndicator(
                            //     totalNumber:
                            //       widget.netaAna["totalCount"],
                            //     actualNumber:
                            //        widget.netaAna["beneficiary"])
                                  //  ),
SizedBox(
                          height: 20,
                        ),
                                   Container(   
            //  margin: EdgeInsets.only(left: 20 ,right: 20),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10) ,border: Border.all(color: Colors.grey) ),
          padding: EdgeInsets.all(24.0),
          // Adjust the color to match the exact hue
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
        
         Text(  "Select Karyakarta Name ",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 20 ,fontWeight: FontWeight.bold),) ,
         SizedBox(height: 20,),
         SizedBox(
          // height:MediaQuery.of(context).size.height,
           child: ListView.builder(

            physics: NeverScrollableScrollPhysics(),
            itemCount:widget.netaAna['karyakartaInfos'].length,
            shrinkWrap: true,
            itemBuilder: (context, index) {
      
              return GestureDetector(
                onTap: () {
                    Navigator.push(context, MaterialPageRoute(builder:  (context) {
                        return KKAna(netaAna: widget.netaAna['karyakartaInfos'][index],totalNumber: widget.netaAna["totalCount"], ) ;
                      },));
                },
                child: ListTile(
                  title: Container( 
                    padding: EdgeInsets.only(top: 10 ,bottom: 10),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Colors.grey, // Border color
                        width: 1.0, // Border width
                      ),
                      borderRadius:
                          BorderRadius.circular(5), // Optional: border radius
                    ),
                   
                    child: Row( 
                    //  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(
                          width: 30,
                        ),
              Container(
              width:50,
              height:50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(width: 2, color: Color(0xff4C3AB4)),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(widget.netaAna['karyakartaInfos'][index]['profileImgUrl']  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
                        // CachedNetworkImage(
                        //   imageUrl: widget.netaAna['karyakartaInfos'][index]['profileImgUrl'],
                        //   // imageBuilder: (context, imageProvider) => Container(child: ,),
                        //   // progressIndicatorBuilder: (context, url, downloadProgress) =>
                        //   //     CircularProgressIndicator(value: downloadProgress.progress),
                        //   errorWidget: (context, url, error) =>
                        //       Icon(Icons.error),
                        //   width: 50,
                        //   fit: BoxFit.fill,
                        // ),
                        SizedBox(
                          width: 30,
                        ),
                        Row(
                          children: [
                            Text('Name: ' , style: TextStyle(fontFamily: 'Gilory-Bold' , fontSize: 16, fontWeight: FontWeight.bold),),
                            Text('${widget.netaAna['karyakartaInfos'][index]['name']}'),
                          ],
                        ),
                      
                         
                                
                      ],
                    ),
                  ),
                ),
              );
            },
            ),
         ),
         
          
       
              ],
          ),
        ) ,
            SizedBox(height: 20,),

                        // Container(
                        //   // height: 300,
                        //   // width: 300,
                        // decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),
                        //   child: SfCartesianChart(
                        //     primaryXAxis: CategoryAxis(),
                        //     series: <ChartSeries>[
                        //       ColumnSeries<SalesData, String>(
                        //         dataSource: chartData,
                        //         xValueMapper: (SalesData sales, _) => sales.category,
                        //         yValueMapper: (SalesData sales, _) => sales.sales,
                        //         dataLabelSettings: DataLabelSettings(isVisible: true),
                        //       ),

                        //     ],
                        //   ),
                        // ),
                      ]),
      //         SizedBox(
      // height: 110,
      //  child: ListView.builder(
      //   itemCount:  netaAna["pointsLeaderBoard"].length,
      //   itemBuilder: (context, index) {
 
      //     return ListTile(
      //       title: Container( 
      //         padding: EdgeInsets.all(20),
      //         // alignment: Alignment.topLeft,
      //         decoration: BoxDecoration(
      //           color: Colors.white,
      //           border: Border.all(
      //             color: Colors.grey, // Border color
      //             width: 1.0, // Border width
      //           ),
      //           borderRadius:
      //               BorderRadius.circular(30), // Optional: border radius
      //         ),
             
      //         child: Row( 
      //           crossAxisAlignment: CrossAxisAlignment.center,
      //           mainAxisAlignment: MainAxisAlignment.start,
      //           children: [
          
      //             CachedNetworkImage(
      //               imageUrl: netaAna["pointsLeaderBoard"][index]['profileImgUrl'],
      //               // imageBuilder: (context, imageProvider) => Container(child: ,),
      //               // progressIndicatorBuilder: (context, url, downloadProgress) =>
      //               //     CircularProgressIndicator(value: downloadProgress.progress),
      //               errorWidget: (context, url, error) =>
      //                   Icon(Icons.error),
      //               width: 50,
      //               fit: BoxFit.fill,
      //             ),
      //             // SizedBox(
      //             //   width: 30,
      //             // ),
      //         SizedBox(width: 20,),   
      //              Column(
      //                 crossAxisAlignment: CrossAxisAlignment.start,
      //           mainAxisAlignment: MainAxisAlignment.start,
      //               children: [  Text('Name: ${netaAna["pointsLeaderBoard"][index]['name'].toString()}'),
      //             SizedBox(
      //               height: 3,
      //             ),
      //             // Text('Number: ${kk['users'][index]['number']}'),
      //             SizedBox(
      //               height: 3,
      //             ),
      //               Text('Total Points : ${netaAna["pointsLeaderBoard"][index]['count'].toString()}'),],
      //              ) ],
      //         ),
      //       ),
      //     );
      //   },
      //   ),
      //  ),
             

        //     Text("Beneficiary",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 30 ,fontWeight: FontWeight.bold  ,color: Colors.black,letterSpacing: 5),),
        //     SizedBox(height: 20,),Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.netaAna["totalCount"] , beneficiaryCount: widget.netaAna["beneficiary"],  firstname: 'Beneficiary',secondname: 'Non-beneficiary')) ,
        //    SizedBox(height: 20,), Text("Profile Edit",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 30 ,fontWeight: FontWeight.bold  ,color:  Colors.black,letterSpacing: 5),),
        //     SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount:  widget.netaAna["totalCount"], beneficiaryCount: widget.netaAna["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
        //  SizedBox(height: 20,),
            
        //          Text("Party Bifurcation",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 30 ,fontWeight: FontWeight.bold  ,color:  Colors.black,letterSpacing: 5),),
        //         SizedBox(height: 20),
        //         Container(
        //           // height: 300,
        //           // width: 300,
        //         decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),
        //           child: SfCartesianChart(
        //             primaryXAxis: CategoryAxis(),
        //             series: <ChartSeries>[
        //               ColumnSeries<SalesData, String>(
        //                 dataSource: chartData,
        //                 xValueMapper: (SalesData sales, _) => sales.category,
        //                 yValueMapper: (SalesData sales, _) => sales.sales,
        //                 dataLabelSettings: DataLabelSettings(isVisible: true),
        //               ),
                      
        //             ],
        //           ),
        //         ),
              
        
              
          
        )]),
        
        
        ),
      ),),
    );
  }
}

class PieChartData {
  final String category;
  final double value;

  PieChartData(this.category, this.value);
}

class KaryakartaAna extends StatefulWidget {
  const KaryakartaAna({Key? key}) : super(key: key);

  @override
  State<KaryakartaAna> createState() => _KaryakartaAnaState();
}

class _KaryakartaAnaState extends State<KaryakartaAna> {
 LoginService login =LoginService() ;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchkaryakarta() ;
  }
 var kkAnalytics ;
   List<SalesData> chartData =[];
   Map partyBifurcation  ={};
   Map lastVotedpartyBifurcation  ={};
   Map responsibilityMap ={};
   Map karyakartaMap = {} ;
    
   var data  ;
 var percentagee ;
      var beneficiarypercentage ;
      var responsiblefor ;
  fetchkaryakarta()async{
 var res =await login.karyakartaAnalytics(context);
 kkAnalytics = res['res'] ;
  //print(kkAnalytics);
 data   
   = kkAnalytics["pointsLeaderBoard"] ; 
  //print('hh${kkAnalytics["partyBifurcation"].isEmpty}');
setState(() {
  
});
if(res['status'] == 403) {
 showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
 }
  
 lastVotedpartyBifurcation = kkAnalytics['lastPartyBifurcation'];
 partyBifurcation =   kkAnalytics["partyBifurcation"] ;
 
 
 final Map<String, int> partyBifurcationConverted = Map<String, int>.fromEntries(
  partyBifurcation.entries.map((entry) => MapEntry(entry.key.toString(), entry.value as int)),
 
);


// Convert the partyBifurcation map into a dynamic list of SalesData
  chartData = partyBifurcationConverted.entries
    .map((entry) => SalesData(entry.key, entry.value))
    .toList();
    
    int totalValues = 0;
partyBifurcationConverted.forEach((key, value) {
  totalValues += value;
  chartData.add( SalesData("Total",  kkAnalytics["totalCount"]-totalValues) ) ;
});
responsibilityMap =kkAnalytics['karyakartaWorkInfoMap'];
 SharedPreferences prefs = await SharedPreferences.getInstance();
    
    String userId = prefs.getString('userId')!;
    
responsibilityMap.forEach((key, value) {
 
  if ( responsibilityMap.containsKey(userId)) {
 
  karyakartaMap = value ;
  }
},) ;

 percentagee =(kkAnalytics["totalCount"] != 0) ? ( karyakartaMap["edited"] / kkAnalytics["totalCount"]) * 100 : 0;
  
           beneficiarypercentage =(kkAnalytics["totalCount"] != 0) ? (kkAnalytics["beneficiary"] / kkAnalytics["totalCount"]) * 100 : 0;
           responsiblefor =(kkAnalytics["totalCount"] != 0) ? (karyakartaMap['responsibleFor'] / kkAnalytics["totalCount"]) * 100 : 0;
 
 setState(() {
   
 });
 
  }
  @override
  Widget build(BuildContext context) { 

 
       fetchkaryakarta();

    return Scaffold(
     backgroundColor: Colors.white,
      appBar: AppBar(
        title:Text('Work Analytics'),
        titleTextStyle: TextStyle(fontSize: 26 , color: Colors.white , fontFamily: "Gilory-Bold-SemiBold", fontWeight: FontWeight.w400),
        backgroundColor: Color(0xff4C3AB4),
        centerTitle: true,
        
      ),
      body:
      // partyBifurcation.isEmpty ? CircularProgressIndicator() : 
      Padding(
      padding: const EdgeInsets.all(10),
    
      child: SingleChildScrollView(
        child:
      kkAnalytics==null  ? CircularProgressIndicator() :
          Column(
          crossAxisAlignment: CrossAxisAlignment.start ,
          children: [
          SizedBox(height: 20,),
    GestureDetector(
      onTap: () {
        Navigator.push(context,  MaterialPageRoute(builder: (context) {
        return  kkLeaderboard(data: data,kkAnalytics:kkAnalytics ,);
        },));
      }, child:  Image(image:AssetImage('assets/kk.png'))
      // SvgPicture.asset('assets/karyakartaleaderboard.svg')
     
        ),
     
      SizedBox(height: 10,),
                 Divider(color: Colors.black) ,
    //  Center(child: Text("Your Work Analytics",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 20 ,fontWeight: FontWeight.bold  ,color: Colors.black, ),)),
    //  SizedBox(height: 20,),
          SizedBox(height: 5,),
       
                      Container(
                        width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(10),
                              // border: Border.all(color: Colors.grey)
                              ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                      //          SizedBox(
                      //   height: 20,
                      // ),
                      Text(
                        "Booth Mapping Completed",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-SemiBold',
                          fontSize: MediaQuery.of(context).size.height*0.023,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                            // Text("Total voter : ${kkAnalytics["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            // Text("Mapped voter  : ${kkAnalytics["edited"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                              Row(
                                children: [
                              karyakartaMap["edited"] != null ?    Text( "${percentagee.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: MediaQuery.of(context).size.height*0.02 , fontWeight:  FontWeight.bold),)
                              :Text( "0%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: MediaQuery.of(context).size.height*0.02 , fontWeight:  FontWeight.bold),),
                                  
                                karyakartaMap["edited"] != null ?      CustomLinearProgressIndicator(
                                      totalNumber:kkAnalytics["totalCount"],
                                      actualNumber: kkAnalytics["edited"]):SizedBox(),
                                ],
                              )
                          ],)),
                      SizedBox(
                        height: 10,
                      ),
                      
                      Container(
                        decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: Color(0xffF6F6F6))),
                        child: Column(
                          crossAxisAlignment:  CrossAxisAlignment.start,
                          children: [
                             SizedBox(
                        height: 20,
                      ),
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: Text(
                        "Party Bifurcation",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize:MediaQuery.of(context).size.height*0.023,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                            ),
                      kkAnalytics["partyBifurcation"].isEmpty  ? 
                      Padding(
                                                       padding: const EdgeInsets.only(left: 10),
                                                       child: SizedBox(
                                                        width: MediaQuery.of(context).size.width,
                                                         child: Text("0%" ,  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,
                                                          fontWeight: FontWeight.bold),),
                                                       ),
                                                     ) 
                       : SizedBox(
                              
                              height:( MediaQuery.of(context).size.height * 0.08 )* partyBifurcation.length,
                              width: MediaQuery.of(context).size.width,
                              child: ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: partyBifurcation.length,
                                itemBuilder: (BuildContext context, int index) {
                                  String key =
                                      partyBifurcation.keys.elementAt(index);
                                  int value =
                                      partyBifurcation.values.elementAt(index);
                                   var    partypercentage =( kkAnalytics["totalCount"] != 0) ? (value / kkAnalytics["totalCount"]) * 100 : 0;
                      
                                  return ListTile(
                                    title: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(key + " : " , style: TextStyle(fontFamily: 'Gilory-Bold-Medium' , fontWeight: FontWeight.w400), ),
                                        SizedBox(height: 5,),
                                         Row(
                                           children: [
                                       kkAnalytics["partyBifurcation"].isNotEmpty  ? Text("${partypercentage.toStringAsFixed(2)??"0"}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),):
                                       Text("${"0"}%" , style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),),
                                        kkAnalytics["partyBifurcation"].isNotEmpty ?     CustomLinearProgressIndicator(
                                            totalNumber:kkAnalytics["totalCount"],
                                            actualNumber: value):SizedBox(),],
                                         )

                                         
                                       
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                       
                      

                      SizedBox(
                        height: 10,
                      ),
                      Container(
                       decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: Color(0xffF6F6F6))),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                             SizedBox(
                          height: 10,
                        ),
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: Text(
                          "Last Voted Party Bifurcation",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize:MediaQuery.of(context).size.height*0.023,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        )
                             ) ),
                             SizedBox(height: 10,),
                       kkAnalytics['lastPartyBifurcation']==null?  
                                                     Padding(
                                                       padding: const EdgeInsets.only(left: 10),
                                                       child: SizedBox(
                                                        width: MediaQuery.of(context).size.width,
                                                         child: Text("0%" ,  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 ,
                                                          fontWeight: FontWeight.bold),),
                                                       ),
                                                     )

                       :    Container(
                             
                              height: (MediaQuery.of(context).size.height * 0.09)*lastVotedpartyBifurcation.length,
                              width: MediaQuery.of(context).size.width,
                              child: ListView.builder(
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: lastVotedpartyBifurcation.length,
                                itemBuilder: (BuildContext context, int index) {
                                  String key =
                                      lastVotedpartyBifurcation.keys.elementAt(index);
                                  int value =
                                      lastVotedpartyBifurcation.values.elementAt(index);
                                                                     var    lastpartypercentage =(kkAnalytics["totalCount"] != 0) ? (value / kkAnalytics["totalCount"]) * 100 : 0;

                                  return ListTile(
                                    title: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(key + " : ",style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize:MediaQuery.of(context).size.height*0.023,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                        ),
                       ),
                       SizedBox(height: 10,),
                                        Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
 
                             kkAnalytics['lastPartyBifurcation']!=null ?    Text("${lastpartypercentage.toStringAsFixed(2)}%" ,  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),)
                             :  Text("0%" ,  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),)
                               ,kkAnalytics['lastPartyBifurcation']!=null ? CustomLinearProgressIndicator(
                                            totalNumber: 
                                                kkAnalytics["totalCount"],
                                            actualNumber: value):SizedBox() ,
                                ],)  ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                              
                      // SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.netaAna["totalCount"], beneficiaryCount:widget.aaggregatedDetails["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
                      
                              
                      SizedBox(
                        height: 10,
                      ),
                                
                                 Container(
                                  width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                               ),
                          child:  Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
  Text(
                        "Total Votes responsibility taken by you-",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize:MediaQuery.of(context).size.height*0.023,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 10,),
                            // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            //  Text("${kkAnalytics["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                                                       Row(
                                                         children: [
                                                    karyakartaMap['responsibleFor']!=null ?       Text("${responsiblefor.toStringAsFixed(2)}%", style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),):
                                                    Text("0%", style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),),
                                              karyakartaMap['responsibleFor']!=null ?     CustomLinearProgressIndicator(
                                            totalNumber: 
                                                kkAnalytics["totalCount"],
                                            actualNumber: kkAnalytics["responsibleFor"]== null ? 0:kkAnalytics["responsibleFor"]):SizedBox(),
                                                         ],
                                                       )

                            // Text("percentage : ${responsiblepercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),)
                          ],)),
                      
                      
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        width: double.infinity,
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Color(0xffF6F6F6),
                              borderRadius: BorderRadius.circular(5),
                              ),
                          child:    Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
Text(
                        "No. of Beneficiaries : ",
                        style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                          fontSize:MediaQuery.of(context).size.height*0.023,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 10,),
                            // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            //  Text("Beneficiary Number  : ${kkAnalytics["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize:MediaQuery.of(context).size.height*0.023),) ,
                            Row(
                              children: [
                            kkAnalytics["beneficiary"] != 0 ?    Text("${
                                 beneficiarypercentage.toStringAsFixed(2)}%",  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),):
                                  Text("0%",  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize:MediaQuery.of(context).size.height*0.023 , fontWeight: FontWeight.bold),)

                               , kkAnalytics["beneficiary"] != 0 ?   CustomLinearProgressIndicator(
                                            totalNumber: 
                                                kkAnalytics["totalCount"],
                                            actualNumber: kkAnalytics["beneficiary"]):
                                            SizedBox(),
                              ],
                            )
                          ],)),

                          ///////////////
          // Text("Beneficiary",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 20 ,fontWeight: FontWeight.bold  ,color: Colors.black,letterSpacing: 5),),
          // SizedBox(height: 20,),Container( decoration: BoxDecoration(border: Border.all(color: Colors.black)),child: MyHomePage(totalCount: kkAnalytics["totalCount"], beneficiaryCount:kkAnalytics["beneficiary"],  firstname: 'Beneficiary',secondname: 'Non-beneficiary')) ,
          // SizedBox(height: 20,), Text("Profile Edit",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 20 ,fontWeight: FontWeight.bold  ,color: Colors.black,letterSpacing: 5),),
          // SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black)),child: MyHomePage(totalCount: kkAnalytics["totalCount"], beneficiaryCount:kkAnalytics["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
          // SizedBox(height: 20,),
          // Text("Party Bifurcation",style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 20 ,fontWeight: FontWeight.bold  ,color: Colors.black,letterSpacing: 5),),
          // SizedBox(height: 20),
          // Container(
          //       // height: 300,
          //       // width: 300,
          //     decoration: BoxDecoration(border: Border.all(color: Colors.black)),
          //       child: SfCartesianChart(
          //         primaryXAxis: CategoryAxis(),
          //         series: <ChartSeries>[
          //           ColumnSeries<SalesData, String>(
          //             dataSource: chartData,
          //             xValueMapper: (SalesData sales, _) => sales.category,
          //             yValueMapper: (SalesData sales, _) => sales.sales,
          //             dataLabelSettings: DataLabelSettings(isVisible: true),
          //           ),
                    
          //         ],
          //       ),
          //     ),
            
      
          SizedBox(height: 30,)  
        
        ]),
      ),
    ),);
  }
}

class _ChartData {
  final String x;
  final int y;

  _ChartData(this.x, this.y);
}

class MyHomePage extends StatelessWidget {
    final int totalCount;
  final int beneficiaryCount;
   final String firstname ;
   final String secondname ;

  MyHomePage({required this.totalCount, required this.beneficiaryCount, required this.firstname ,required this.secondname});

  
  @override
  Widget build(BuildContext context) {
     
    return   Center(
        child: PieChartWidget(
          totalCount: totalCount,
          beneficiaryCount: beneficiaryCount,
          firstname: firstname,
          secondname: secondname,
        ),
     
    );
  }
}

class PieChartWidget extends StatelessWidget {
  final int totalCount;
  final int beneficiaryCount;
  final String firstname ;
   final String secondname ;

  PieChartWidget({required this.totalCount, required this.beneficiaryCount , required this.firstname ,required this.secondname});

  @override
  Widget build(BuildContext context) {
    return SfCartesianChart(
      primaryXAxis: CategoryAxis(),
      primaryYAxis: NumericAxis(),
      // series: <ChartSeries>[
      //   ColumnSeries<_ChartData, String>(
      //     dataSource: [
      //       _ChartData(firstname, beneficiaryCount),
      //       _ChartData(secondname, totalCount - beneficiaryCount),
      //     ],
      //     xValueMapper: (_ChartData data, _) => data.x,
      //     yValueMapper: (_ChartData data, _) => data.y,
      //     dataLabelSettings: DataLabelSettings(isVisible: true),
      //   ),
      // ],
    );
  }
}

 
class SalesData {
  final String category;
  final int sales;

  SalesData(this.category, this.sales);
}
class kkLeaderboard extends StatelessWidget {
  var data ; 
  var kkAnalytics ;
    kkLeaderboard({Key? key , required this.data ,required this.kkAnalytics}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(

      appBar: AppBar(
        title:Text('Leaderboard'),
        titleTextStyle: TextStyle(fontSize: 26 , color: Colors.white , fontFamily: "Gilory-Bold-SemiBold", fontWeight: FontWeight.w400),
        backgroundColor: Color(0xff4C3AB4),
        centerTitle: true,
         ),
      body:  data.isEmpty? Text("No Data yet") : SizedBox(
       
       child: ListView.builder(
        itemCount: kkAnalytics["pointsLeaderBoard"].length,
        itemBuilder: (context, index) {
 
          return ListTile(
            title: Container( 
              padding: EdgeInsets.all(20),
              margin: EdgeInsets.only(top: 20),
              // alignment: Alignment.topLeft,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.grey.withOpacity(0.4), // Border color
                  width: 1.0, // Border width
                ),
                borderRadius:
                    BorderRadius.circular(10), // Optional: border radius
              ),
             
              child: Row( 
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
          Container(
                  width:  MediaQuery.of(context).size.width*0.24  ,
                  height:MediaQuery.of(context).size.width*0.24  ,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(width: 2, color: Color(0xff4C3AB4)),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(kkAnalytics["pointsLeaderBoard"][index]['profileImgUrl']  ),
                      fit: BoxFit.cover,
                    ),
                  )) ,
                  // CachedNetworkImage(
                  //   imageUrl: kkAnalytics["pointsLeaderBoard"][index]['profileImgUrl'],
                  //   // imageBuilder: (context, imageProvider) => Container(child: ,),
                  //   // progressIndicatorBuilder: (context, url, downloadProgress) =>
                  //   //     CircularProgressIndicator(value: downloadProgress.progress),
                  //   errorWidget: (context, url, error) =>
                  //       Icon(Icons.error),
                  //   width: 50,
                  //   fit: BoxFit.fill,
                  // ),
                  // SizedBox(
                  //   width: 30,
                  // ),
              SizedBox(width: 20,),   
                   Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                    children: [  Row(
                      children: [
                         Text('Name: ' , style: TextStyle(fontFamily: "Gilory-Bold-SemiBold" , fontWeight:  FontWeight.bold),),
                        Text('${kkAnalytics["pointsLeaderBoard"][index]['name'].toString()}'),
                      ],
                    ),
                  SizedBox(
                    height: 3,
                  ),
                  // Text('Number: ${kk['users'][index]['number']}'),
                  SizedBox(
                    height: 3,
                  ),
                    Container( 
                      height:20,
                      decoration: BoxDecoration( color: Color.fromARGB(255, 74, 73, 73), borderRadius: BorderRadius.circular(10)),
                     padding: EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        children: [
                          Stack(
                            children: [
                               Icon(Icons.circle_outlined ,color:  Colors.grey, size:15,),SizedBox(width: 10,),
Positioned(right:0,left: 5,
  child: Icon(Icons.circle_outlined ,color:  Colors.grey, size: 15,)),SizedBox(width: 30,),
                              
                            ],
                          ) ,
                         
                          Text('${kkAnalytics["pointsLeaderBoard"][index]['count'].toString()}  ' ,style:  TextStyle(fontFamily: 'Gilory-Bold',color: Colors.white),),
                        ],
                      ),
                    ),
                    ],
                   )
                  ],
              ),
            ),
          );
        },
        ),
       ),
    );
  }
}