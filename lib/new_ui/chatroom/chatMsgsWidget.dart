// import 'dart:io';

// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/new_ui/chatroom/ImagePreviewMessage.dart';
// import 'package:mla_connect/services/firebase_chat.dart';
// import 'package:mla_connect/utils/colorConstants.dart';
// import 'package:mla_connect/utils/imagePickerUtil.dart';
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';
// import 'package:share_plus/share_plus.dart';
// import 'package:mla_connect/models/ChatMessage.dart';
// import 'package:mla_connect/services/firebase_cloud.dart';

// enum MESSAGETYPE { TEXT, POLL, IMAGE }

// class MessageTypeBox extends StatelessWidget {
//   SelfProfile selfProfile;
//   String title, roomId;

//   MessageTypeBox(
//       {Key? key,
//       required this.selfProfile,
//       required this.roomId,
//       required this.title})
//       : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     TextEditingController _messageController = new TextEditingController();

//     sendMessage(MESSAGETYPE type,
//         {String? image, ChatMessagePoll? poll}) async {
//       if (type == MESSAGETYPE.TEXT) {
//         if (_messageController.text.isNotEmpty) {
//           var _currentTime = DateTime.now().millisecondsSinceEpoch;
//           ChatMessage message = ChatMessage(
//               message: _messageController.text,
//               messageId: _currentTime.toString(),
//               messageType: "text",
//               profileImageUrl: selfProfile.profileUrl,
//               recipientId: roomId,
//               recipientName: title,
//               senderId: selfProfile.id,
//               senderName: selfProfile.name,
//               timeStamp: _currentTime,
//               mediaUrl: null);
//           _messageController.clear();
//           FirebaseChat.instance
//               .sendMessageToFirebase(msg: message, chatId: roomId);
//         }
//       }
//       if (type == MESSAGETYPE.IMAGE) {
//         if (image != null) {
//           final url = await FirebaseCloud.instance
//               .uploadFileToFirebaseStorage(File(image));
//           var _currentTime = DateTime.now().millisecondsSinceEpoch;
//           ChatMessage message = ChatMessage(
//               message: "",
//               messageId: _currentTime.toString(),
//               messageType: "image",
//               profileImageUrl: selfProfile.profileUrl,
//               recipientId: roomId,
//               recipientName: title,
//               senderId: selfProfile.id,
//               senderName: selfProfile.name,
//               timeStamp: _currentTime,
//               mediaUrl: url);
//           _messageController.clear();
//           FirebaseChat.instance
//               .sendMessageToFirebase(msg: message, chatId: roomId);
//         }
//       }
//       if (type == MESSAGETYPE.POLL) {
//         if (poll != null) {
//           var _currentTime = DateTime.now().millisecondsSinceEpoch;
//           ChatMessage message = ChatMessage(
//               message: "",
//               messageId: _currentTime.toString(),
//               messageType: "poll",
//               profileImageUrl: selfProfile.profileUrl,
//               recipientId: roomId,
//               recipientName: title,
//               senderId: selfProfile.id,
//               senderName: selfProfile.name,
//               timeStamp: _currentTime,
//               pollData: poll,
//               mediaUrl: null);
//           _messageController.clear();
//           FirebaseChat.instance
//               .sendMessageToFirebase(msg: message, chatId: roomId);
//         }
//       }
//     }

//     createRoomDialog() {
//       showDialog(
//           context: context,
//           builder: (context) {
//             String? error = null;
//             int noOfFields = 0;

//             List<TextEditingController> col = [
//               TextEditingController(),
//               TextEditingController(),
//               TextEditingController(),
//               TextEditingController(),
//               TextEditingController()
//             ];
//             List fields = [
//               TextField(
//                 controller: col[0],
//                 decoration:
//                     InputDecoration(label: Text("Option ${noOfFields + 1}")),
//               )
//             ];
//             TextEditingController _questionController =
//                 new TextEditingController();

//             return StatefulBuilder(builder: (context, setState) {
//               Map<String, List<String?>> options = {};
//               createPoll() {
//                 setState(() {
//                   error = null;
//                 });
//                 if (noOfFields < 1) {
//                   setState(() {
//                     error = "Add atleast 2 options";
//                   });
//                 }
//                 if (_questionController.text.isEmpty)
//                   setState(() {
//                     error = "Enter Question";
//                   });
//                 for (int i = 0; i <= noOfFields; i++) {
//                   if (col[i].text.isEmpty) {
//                     setState(() {
//                       error = "Enter Option ${i + 1}";
//                     });
//                     break;
//                   }
//                   options[(col[i].text)] = [];
//                 }
//                 if (error == null) {
//                   ChatMessagePoll poll = new ChatMessagePoll(
//                       options: options, question: _questionController.text);
//                   sendMessage(MESSAGETYPE.POLL, poll: poll);
//                   Navigator.pop(context);
//                 }
//               }

//               addField() {
//                 // col.add(TextEditingController());
//                 if (noOfFields == 4) {
//                   // context.snackBar("Cant add more than 5");
//                   return;
//                 }
//                 setState(() {
//                   noOfFields++;
//                   fields.add(TextField(
//                     controller: col[noOfFields],
//                     decoration: InputDecoration(
//                         label: Text("Option ${noOfFields + 1}")),
//                   ));
//                 });
//               }

//               subtractField() {
//                 if (noOfFields < 1) {
//                   return;
//                 }
//                 // col.removeLast();

//                 setState(() {
//                   fields.removeLast();
//                   noOfFields--;
//                 });
//               }

//               return AlertDialog(
//                 title: Text("Create a poll"),
//                 content: Container(
//                     height: MediaQuery.of(context).size.height * 0.5,
//                     child: SingleChildScrollView(
//                         child: Column(children: [
//                       TextField(
//                         controller: _questionController,
//                         decoration:
//                             InputDecoration(label: Text("Enter Question")),
//                       ),
//                       ...fields,
//                       error != null
//                           ? Text(error!, style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red))
//                           : Container()
//                     ]))),
//                 actions: [
//                   ElevatedButton(
//                       onPressed: addField,
//                       child: Text(
//                         "+",
//                         style:  TextStyle(fontFamily: 'Gilroy',fontSize: 30),
//                       )),
//                   ElevatedButton(
//                       onPressed: subtractField,
//                       child: Text(
//                         "-",
//                         style:  TextStyle(fontFamily: 'Gilroy',fontSize: 30),
//                       )),
//                   ElevatedButton(
//                       onPressed: createPoll,
//                       child: Text(
//                         "Create",
//                         style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18),
//                       ))
//                 ],
//               );
//             });
//           });
//     }

//     return Align(
//         alignment: Alignment.bottomRight,
//         child: Container(
//             child: Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Card(
//                 elevation: 3,
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(30.0),
//                 ),
//                 child: Container(
//                     height: 60,
//                     width: MediaQuery.of(context).size.width * 0.8,
//                     child: Row(
//                       children: [
//                         Expanded(
//                             child: Padding(
//                                 padding: EdgeInsets.symmetric(horizontal: 10),
//                                 child: TextField(
//                                     controller: _messageController,
//                                     decoration: InputDecoration(
//                                         border: InputBorder.none,
//                                         focusedBorder: InputBorder.none,
//                                         enabledBorder: InputBorder.none,
//                                         errorBorder: InputBorder.none,
//                                         disabledBorder: InputBorder.none)))),
//                         Container(
//                           width: 25,
//                           padding: EdgeInsets.all(0),
//                           margin: EdgeInsets.all(0),
//                           child: IconButton(
//                             padding: EdgeInsets.zero,
//                             onPressed: () {
//                               createRoomDialog();
//                             }, //icon: Icon(Icons.send)
//                             icon: Image.asset(
//                               'assets/icons/polls.png',
//                               // width: 35,
//                               // height: 35,
//                               fit: BoxFit.cover,
//                               color: Colors.black,
//                             ),
//                           ),
//                         ),
//                         SizedBox(
//                           width: 10,
//                         ),
//                         // Container(
//                         //     width: 25,
//                         //     child: IconButton(
//                         //         onPressed: () {}, //icon: Icon(Icons.send)
//                         //         icon: Image.asset(
//                         //           'assets/icons/attachment.png',
//                         //           // height: 25,
//                         //           color: Colors.black,
//                         //         ))),
//                         Container(
//                             padding: EdgeInsets.zero,
//                             margin: EdgeInsets.zero,
//                             width: 25,
//                             child: IconButton(
//                                 padding: EdgeInsets.zero,
//                                 onPressed: () async {
//                                   try {
//                                     final XFile? image =
//                                         await ImageUtil.pickImageFromFile();
//                                     if (image != null) {
//                                       // createRoomDialog();
//                                       final res =
//                                           await PersistentNavBarNavigator
//                                               .pushNewScreen(
//                                         context,
//                                         screen: ImagePreviewMessage(
//                                           path: image.path,
//                                         ),
//                                         withNavBar: false,
//                                       );
//                                       if (res != null && res != false) {
//                                         sendMessage(MESSAGETYPE.IMAGE,
//                                             image: image.path);
//                                       }
//                                     }
//                                     // setState(() {
//                                     //   this.profileFile = image;
//                                     // });
//                                   } catch (e) {
//                                     context.snackBar(e.toError());
//                                   }
//                                 }, //icon: Icon(Icons.send)
//                                 icon: Image.asset(
//                                   'assets/icons/Camera.png',
//                                   fit: BoxFit.fill,
//                                   color: Colors.black,
//                                 ))),
//                         SizedBox(
//                           width: 10,
//                         )
//                       ],
//                     ))),
//             ClipOval(
//                 child: Material(
//               color: PRIMARY_COLOR_ORANGE, // Button color
//               child: InkWell(
//                 splashColor: Colors.black, // Splash color
//                 onTap: () => sendMessage(MESSAGETYPE.TEXT),
//                 child: SizedBox(
//                   width: 45,
//                   height: 45,
//                   child: Padding(
//                       padding: EdgeInsets.all(9),
//                       child: Image.asset(
//                         "assets/icons/Send.png",
//                         color: Colors.white,
//                       )),
//                 ),
//               ),
//             )),
//           ],
//         )));
//   }
// }
