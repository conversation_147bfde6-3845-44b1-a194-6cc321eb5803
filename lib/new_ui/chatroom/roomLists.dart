// import 'package:mla_connect/controller/chatRoomController.dart';
// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/models/RoomList.dart';
// import 'package:mla_connect/new_ui/chatroom/message_screen.dart';
// import 'package:mla_connect/services/firebase_chat.dart';
// import 'package:mla_connect/services/firebase_cloud.dart';
// import 'package:mla_connect/services/unseenMsgController.dart';
// import 'package:mla_connect/utils/colorConstants.dart';
// import 'package:mla_connect/utils/database_helper.dart';
// import 'package:mla_connect/utils/timeUtil.dart';
// import 'package:mla_connect/widgets/progress.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get_state_manager/get_state_manager.dart';
// import 'package:get/instance_manager.dart';
// import 'package:intl/intl.dart';
// import 'package:mla_connect/models/ChatMessage.dart';

// class RoomListPage extends StatefulWidget {
//   const RoomListPage({Key? key}) : super(key: key);

//   @override
//   State<RoomListPage> createState() => _RoomListPageState();
// }

// class _RoomListPageState extends State<RoomListPage>
//     with WidgetsBindingObserver {
//   late ChatRoomController _chatRoomController;
//   AppLifecycleState? _notification;
//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) {
//      //print("STATE IS");
//      //print(state.name);
//     if (state == AppLifecycleState.resumed) {
//       _chatRoomController.fetchRefreshedData();
//     }
//     setState(() {
//       _notification = state;
//     });
//   }

//   @override
//   void initState() {
//     super.initState();
//     _chatRoomController = Get.find<ChatRoomController>();
//     WidgetsBinding.instance.addObserver(this);

//     // Get.put(UnseenMsgController());
//   }

//   @override
//   void dispose() {
//     WidgetsBinding.instance.removeObserver(this);
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         resizeToAvoidBottomInset: true,
//         appBar: AppBar(
//           title: Text("Channels"),
//           elevation: 0,
//         ),
//         body:
//             // GetBuilder<ChatRoomController>(
//             // builder: (controller) =>
//             // controller.oldRoomListsData.length == 0

//             Obx(() => _chatRoomController.oldRoomListsData.length == 0
//                 ? PI1()
//                 : ListView.builder(
//                     itemCount: _chatRoomController.oldRoomListsData.length,
//                     itemBuilder: (context, index) {
//                        //print("ROOM LIST DATA");
//                        //print(_chatRoomController
//                           .oldRoomListsData[index].unseenCount);
//                       return Container(
//                           color: Colors.grey[100],
//                           child: GestureDetector(
//                               onTap: () {
//                                 context.pushWidget((context) => MessageScreen(
//                                     title: _chatRoomController
//                                         .oldRoomListsData[index].name,
//                                     roomId: _chatRoomController
//                                         .oldRoomListsData[index].roomId));
//                               },
//                               child: Column(
//                                 children: [
//                                   ListTile(
//                                       title: Text(_chatRoomController
//                                           .oldRoomListsData[index].name),
//                                       subtitle: _chatRoomController
//                                                       .oldRoomListsData[index]
//                                                       .lastMessage
//                                                       .message ==
//                                                   null ||
//                                               _chatRoomController
//                                                       .oldRoomListsData[index]
//                                                       .lastMessage
//                                                       .message ==
//                                                   ""
//                                           ? Text(_chatRoomController
//                                                   .oldRoomListsData[index]
//                                                   .lastMessage
//                                                   .senderName +
//                                               " : " +
//                                               _chatRoomController
//                                                   .oldRoomListsData[index]
//                                                   .lastMessage
//                                                   .messageType)
//                                           : Text(_chatRoomController
//                                                   .oldRoomListsData[index]
//                                                   .lastMessage
//                                                   .senderName +
//                                               " : " +
//                                               _chatRoomController
//                                                   .oldRoomListsData[index]
//                                                   .lastMessage
//                                                   .message!),
//                                       trailing:
//                                           // _unseenMsg_chatRoomController
//                                           // .loaded.value &&
//                                           // snapshot.data != 0
//                                           _chatRoomController
//                                                       .oldRoomListsData[index]
//                                                       .unseenCount !=
//                                                   0
//                                               ? ClipOval(
//                                                   child: Material(
//                                                   color:
//                                                       PRIMARY_COLOR_ORANGE, // Button color
//                                                   child: InkWell(
//                                                     // Splash color
//                                                     child: SizedBox(
//                                                       width: 45,
//                                                       height: 45,
//                                                       child: Center(
//                                                           child: Text(
//                                                         "${_chatRoomController.oldRoomListsData[index].unseenCount ?? ""}",
//                                                         style:  TextStyle(fontFamily: 'Gilroy',
//                                                             color:
//                                                                 Colors.white),
//                                                       )),
//                                                     ),
//                                                   ),
//                                                 ))
//                                               : Text("")),
//                                   Divider(
//                                     thickness: 10,
//                                     color: Colors.white,
//                                   )
//                                 ],
//                               )));
//                     },
//                   )));
//   }
// }
