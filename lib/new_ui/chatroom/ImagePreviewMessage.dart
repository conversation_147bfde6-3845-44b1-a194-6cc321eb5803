import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class ImagePreviewMessage extends StatelessWidget {
  String path;
  String? url;
  ImagePreviewMessage({Key? key, required this.path, this.url})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        floatingActionButton: url == null
            ? FloatingActionButton(
                backgroundColor: PRIMARY_COLOR_ORANGE,
                onPressed: () {
                  Navigator.pop(context, true);
                },
                child: Icon(Icons.check))
            : null,
        appBar: AppBar(
            backgroundColor: Colors.white,
            title: Text(
              "Preview",
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black),
            ),
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context, false);
              },
              icon: Icon(Icons.arrow_back),
              color: Colors.black,
            )),
        body: url == null
            ? PhotoView(imageProvider: FileImage(File(path)))
            : PhotoView(imageProvider: CachedNetworkImageProvider(url!)));
  }
}
