// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:mla_connect/controller/bottomBarController.dart';
// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/new_ui/chatroom/message_screen.dart';
// import 'package:mla_connect/services/firebase_chat.dart';
// import 'package:mla_connect/services/firebase_cloud.dart';
// import 'package:mla_connect/services/unseenMsgController.dart';
// import 'package:mla_connect/utils/colorConstants.dart';
// import 'package:mla_connect/widgets/progress.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get_state_manager/get_state_manager.dart';
// import 'package:get/instance_manager.dart';
// import 'package:mla_connect/models/ChatMessage.dart';
// // import 'package:get/get_state_manager/get_state_manager.dart';
// // import 'package:get/instance_manager.dart';
// // package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart

// class DummyStream extends StatefulWidget {
//   const DummyStream({Key? key}) : super(key: key);

//   @override
//   State<DummyStream> createState() => _DummyStreamState();
// }

// class _DummyStreamState extends State<DummyStream> {
//   late UnseenMsgController _unseenMsgController;
//   createRoomDialog() {
//     bool isCreating = false;

//     String? error;

//     TextEditingController _nameController = new TextEditingController();

//     showDialog(
//         context: context,
//         builder: (context) {
//           return StatefulBuilder(
//             builder: (context, setState) {
//               return AlertDialog(
//                 title: Text("Create a room"),
//                 content: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     TextField(
//                       decoration: InputDecoration(hintText: "Room Name"),
//                       controller: _nameController,
//                     ),
//                     Align(
//                         alignment: Alignment.centerLeft,
//                         child: Text(error ?? "",
//                             style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red))),
//                   ],
//                 ),
//                 actions: [
//                   TextButton(
//                       onPressed: () => Navigator.pop(context),
//                       child: Text("Cancel")),
//                   TextButton(
//                       onPressed: () => {
//                             if (_nameController.text.isNotEmpty)
//                               {
//                                 setState(() {
//                                   isCreating = true;
//                                 }),
//                                 FirebaseChat.instance
//                                     .createRoom(_nameController.text)
//                                     .then((value) => {
//                                           if (value)
//                                             {
//                                               setState(() {
//                                                 isCreating = false;
//                                                 Navigator.pop(context);
//                                               }),
//                                             }
//                                           else
//                                             {
//                                               setState(() {
//                                                 isCreating = false;
//                                                 error =
//                                                     "Something went wrong, try again";
//                                               })
//                                             }
//                                         })
//                               }
//                             else
//                               setState(() {
//                                 error = "Enter room Name";
//                               })
//                           },
//                       child: isCreating
//                           ? SizedBox(
//                               height: 24,
//                               width: 24,
//                               child: CircularProgressIndicator(),
//                             )
//                           : Text("Create"))
//                 ],
//               );
//             },
//           );
//         });
//   }

//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     _unseenMsgController = Get.put(UnseenMsgController());
//     getUnseenMsgCount();
//   }

//   getUnseenMsgCount() async {
//     DashboardResponse d = await FirebaseCloud.instance.getDashboardData();
//     await FirebaseChat.instance.getLastSeenMsgCount(d.profile.id);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         // floatingActionButton: FloatingActionButton(
//         //     child: ImageIcon(
//         //       AssetImage("assets/icons/chat-group.png"),
//         //       color: Colors.white,
//         //     ),
//         //     onPressed: () {
//         //       createRoomDialog();
//         //     }),
//         resizeToAvoidBottomInset: true,
//         appBar: AppBar(
//           title: Text("Channels"),
//           elevation: 0,
//         ),
//         body: StreamBuilder<QuerySnapshot>(
//           stream: FirebaseFirestore.instance
//               .collection('rooms')
//               .orderBy("lastMessageTimestamp", descending: true)
//               .snapshots(),
//           builder: (context, AsyncSnapshot<QuerySnapshot> snapshot) {
//             if (!snapshot.hasData) {
//               return CircularProgressIndicator();
//             }
//             return ListView.builder(
//               itemCount: snapshot.data!.docs.length,
//               itemBuilder: (context, index) {
//                 DocumentSnapshot document = snapshot.data!.docs[index];
//                 String id = document.id;
//                 Map<String, dynamic> newData =
//                     document.data() as Map<String, dynamic>;
//                 return Obx(() => Container(
//                     color: Colors.grey[100],
//                     child: GestureDetector(
//                         onTap: () {
//                           context.pushWidget((context) => MessageScreen(
//                               title: newData['name'], roomId: id));
//                         },
//                         child: StreamBuilder<ChatMessageUnread>(
//                             stream: getUnreadMessagesCount(
//                                 document.id,
//                                 _unseenMsgController.getUnseenMsgMap,
//                                 _unseenMsgController
//                                         .getUnseenMsgMap[document.id] ==
//                                     newData['lastMessageTimestamp']),
//                             builder: (context, snapshot) {
//                               if (!snapshot.hasData) {
//                                 return Container();
//                               }
//                               return Column(
//                                 children: [
//                                   ListTile(
//                                       title: Text(newData['name'].toString()),
//                                       subtitle: snapshot.data!.msg.message ==
//                                                   null ||
//                                               snapshot.data!.msg.message == ""
//                                           ? Text(snapshot.data!.msg.senderName +
//                                               " : " +
//                                               snapshot.data!.msg.messageType)
//                                           : Text(snapshot.data!.msg.senderName +
//                                               " : " +
//                                               snapshot.data!.msg.message!),
//                                       trailing:
//                                           // _unseenMsgController
//                                           // .loaded.value &&
//                                           // snapshot.data != 0
//                                           snapshot.data!.unreadMsg != 0
//                                               ? ClipOval(
//                                                   child: Material(
//                                                   color:
//                                                       PRIMARY_COLOR_ORANGE, // Button color
//                                                   child: InkWell(
//                                                     // Splash color
//                                                     child: SizedBox(
//                                                       width: 45,
//                                                       height: 45,
//                                                       child: Center(
//                                                           child: Text(
//                                                         "${snapshot.data!.unreadMsg}",
//                                                         style:  TextStyle(fontFamily: 'Gilroy',
//                                                             color:
//                                                                 Colors.white),
//                                                       )),
//                                                     ),
//                                                   ),
//                                                 ))
//                                               : Text("")),
//                                   Divider(
//                                     thickness: 10,
//                                     color: Colors.white,
//                                   )
//                                 ],
//                               );
//                             }))));
//               },
//             );
//           },
//         ));
//   }

//   Stream<ChatMessageUnread> getUnreadMessagesCount(
//       String roomId, Map<String, dynamic> lastCount, bool alreadyLastMsg) {
//     Stream<ChatMessageUnread> data = FirebaseFirestore.instance
//         .collection('rooms')
//         .doc(roomId)
//         .collection("messages")
//         .orderBy('timeStamp')
//         .startAfter(
//             alreadyLastMsg ? [lastCount[roomId] - 1] : [lastCount[roomId]])
//         .snapshots()
//         .map((querySnapshot) {
//       ChatMessageUnread msg = new ChatMessageUnread(
//           msg: ChatMessage.fromJson(querySnapshot.docs.last.data()),
//           unreadMsg: alreadyLastMsg ? 0 : querySnapshot.docs.length);
//       return msg;
//     });
//     return data;
//   }
// }
