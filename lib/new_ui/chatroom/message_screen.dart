// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/new_ui/chatroom/ImagePreviewMessage.dart';
// import 'package:mla_connect/new_ui/chatroom/chatMsgsWidget.dart';
// import 'package:mla_connect/services/firebase_chat.dart';
// import 'package:mla_connect/services/firebase_cloud.dart';
// import 'package:mla_connect/utils/colorConstants.dart';
// import 'package:mla_connect/utils/timeUtil.dart';
// import 'package:mla_connect/widgets/progress.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:mla_connect/models/ChatMessage.dart';
// import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';

// class MessageScreen extends StatefulWidget {
//   String title;
//   String roomId;
//   MessageScreen({Key? key, required this.title, required this.roomId})
//       : super(key: key);

//   @override
//   State<MessageScreen> createState() => _MessageScreenState();
// }

// class _MessageScreenState extends State<MessageScreen> {
//   TextEditingController _messageController = new TextEditingController();

//   bool firstTimeMsgOpenUpdate = false;
//   bool _isLoading = false;

//   late SelfProfile _selfProfile;
//   ScrollController _scrollController = ScrollController();
//   final int _messagesPerPage = 10;
//   int _messagesToLoad = 10;
//   bool _hasMoreMessages = true;
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     getSelfData();
//     _scrollController.addListener(_scrollListener);
//   }

//   void _scrollListener() {
//     if (_scrollController.position.pixels ==
//         _scrollController.position.maxScrollExtent) {
//       if (!_isLoading) {
//         setState(() {
//           _isLoading = true;
//           _messagesToLoad += _messagesPerPage;
//         });
//         // Add a delay to simulate loading time before setting _isLoading back to false
//         Future.delayed(Duration(milliseconds: 1000), () {
//           setState(() {
//             _isLoading = false;
//           });
//         });
//       }
//     }
//   }

//   void _checkIfHasMoreMessages(int loadedMessages, int totalMessages) {
//     if (loadedMessages == totalMessages) {
//       setState(() {
//         _hasMoreMessages = false;
//       });
//     }
//   }

//   getSelfData() async {
//     DashboardResponse d = await FirebaseCloud.instance.getDashboardData();
//     _selfProfile = d.profile;
//   }

//   Widget messageBubble(ChatMessage msg) {
//     bool self = msg.senderId == _selfProfile.id;
//     return Container(
//         margin: EdgeInsets.all(2),
//         child: Row(
//           mainAxisAlignment:
//               self ? MainAxisAlignment.end : MainAxisAlignment.start,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             self
//                 ? Container()
//                 : Container(
//                     width: 60,
//                     height: 60,
//                     child: ClipOval(
//                         child: msg.profileImageUrl == null
//                             ? Image.asset('assets/userImage.png')
//                             : CachedNetworkImage(
//                                 imageUrl: msg.profileImageUrl!,
//                                 fit: BoxFit.cover,
//                               )),
//                   ),
//             SizedBox(
//               width: 3,
//             ),
//             Container(
//                 padding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
//                 constraints: BoxConstraints(
//                     minWidth: 100,
//                     maxWidth: MediaQuery.of(context).size.width * 0.7),
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(10),
//                   color: PRIMARY_COLOR_ORANGE.withOpacity(0.1),
//                 ),
//                 child: Column(
//                   crossAxisAlignment:
//                       self ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Text(msg.senderName),
//                     SizedBox(height: 5),
//                     msg.messageType == 'image'
//                         ? _renderImageMsg(msg)
//                         : msg.messageType == 'poll'
//                             ? _renderPollMsg(msg)
//                             : Text(
//                                 msg.message ?? "",
//                                 style:  TextStyle(fontFamily: 'Gilroy',
//                                     fontWeight: FontWeight.w300, fontSize: 16),
//                               ),
//                     SizedBox(height: 5),
//                     Text(
//                       TimeUtil().formatedCreateTime(msg.timeStamp),
//                       style:
//                            TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.w300, fontSize: 10),
//                     ),
//                   ],
//                 )),
//             SizedBox(
//               width: 3,
//             ),
//             self
//                 ? Container(
//                     width: 60,
//                     height: 60,
//                     child: ClipOval(
//                         child: msg.profileImageUrl == null
//                             ? Image.asset('assets/userImage.png')
//                             : CachedNetworkImage(
//                                 imageUrl: msg.profileImageUrl!,
//                                 fit: BoxFit.cover,
//                               )),
//                   )
//                 : Container(),
//           ],
//         ));
//   }

//   @override
//   void dispose() {
//     // TODO: implement dispose
//     _scrollController.dispose();
//     super.dispose();
//     _messageController.dispose();
//   }

//   ChatMessage? latestAddedDocument;

//   Widget _renderPollMsg(ChatMessage msg) {
//     int totalVotes = 0;
//     bool voted = false;
//     msg.pollData!.options.forEach((key, value) {
//       totalVotes += value.length;
//       if (value.contains(_selfProfile.id)) {
//         voted = true;
//       }
//     });
//     if (totalVotes == 0) totalVotes = 1;
//     String? selectedVote;
//     return Container(
//       padding: EdgeInsets.all(10),
//       color: PRIMARY_COLOR_ORANGE.withOpacity(0.1),
//       child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
//         Text(msg.pollData!.question),
//         Divider(
//           color: Colors.white,
//         ),
//         voted
//             ? Column(
//                 children: [
//                   for (var item in msg.pollData!.options.entries)
//                     Column(
//                       mainAxisAlignment: MainAxisAlignment.start,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text(item.key),
//                             Text(item.value.length.toString()),
//                           ],
//                         ),
//                         Container(
//                             margin: EdgeInsets.symmetric(vertical: 10),
//                             height: 11,
//                             child: ClipRRect(
//                                 borderRadius:
//                                     BorderRadius.all(Radius.circular(10)),
//                                 child: LinearProgressIndicator(
//                                   value: item.value.length / totalVotes,
//                                   minHeight: 10,
//                                   backgroundColor: Colors.black,
//                                   color: Colors.white,
//                                 ))),

//                         //to take input
//                       ],
//                     )
//                 ],
//               )
//             : Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: [
//                     for (var item in msg.pollData!.options.entries)
//                       ListTile(
//                         contentPadding: EdgeInsets.zero,
//                         title: Text(item.key),
//                         leading: Radio(
//                           value: item.key,
//                           groupValue: selectedVote,
//                           onChanged: (String? value) {
//                             selectedVote = value;
//                             ChatMessagePoll addedData = msg.pollData!;
//                             addedData.options[item.key]!.add(_selfProfile.id);
//                             FirebaseChat.instance.addVote(_selfProfile.id,
//                                 widget.roomId, msg.messageId, addedData);
//                           },
//                         ),
//                       ),
//                   ])
//       ]),
//     );
//   }

//   Widget _renderImageMsg(ChatMessage msg) {
//     return GestureDetector(
//         onTap: () {
//           PersistentNavBarNavigator.pushNewScreen(
//             context,
//             screen: ImagePreviewMessage(path: "", url: msg.mediaUrl),
//             withNavBar: false,
//           );
//         },
//         child: Container(
//             padding: EdgeInsets.zero,
//             width: MediaQuery.of(context).size.width * 0.6,
//             height: MediaQuery.of(context).size.width * 0.6,
//             child: CachedNetworkImage(
//               imageUrl: msg.mediaUrl!,
//               height: 250,
//               fit: BoxFit.cover,
//             )));
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         resizeToAvoidBottomInset: true,
//         appBar: AppBar(
//             title: Text(widget.title),
//             elevation: 0,
//             leading: IconButton(
//               onPressed: () {
//                 Navigator.pop(context, true);
//               },
//               icon: Icon(Icons.arrow_back),
//               color: Colors.black,
//             )),
//         body: StreamBuilder<QuerySnapshot>(
//             stream: FirebaseFirestore.instance
//                 .collection('rooms')
//                 .doc(widget.roomId)
//                 .collection("messages")
//                 .orderBy('timeStamp', descending: true)
//                 .limit(_messagesToLoad)
//                 .snapshots(),
//             builder:
//                 (BuildContext context, AsyncSnapshot<QuerySnapshot> snapshot) {
//               if (!snapshot.hasData) return new Center(child: PI1());
//               if (mounted) if (!firstTimeMsgOpenUpdate &&
//                   snapshot.data!.docs.isNotEmpty) {
//                 FirebaseChat.instance.updateLastSeenMsgUser(
//                     _selfProfile.id,
//                     widget.roomId,
//                     snapshot.data!.docs.first['timeStamp'].toString(),
//                     ChatMessage.fromJson(
//                         snapshot.data!.docs[0].data() as Map<String, dynamic>));
//                 latestAddedDocument = ChatMessage.fromJson(
//                     snapshot.data!.docs[0].data() as Map<String, dynamic>);
//                 firstTimeMsgOpenUpdate = true;
//               }

//               final addedDocs = snapshot.data!.docChanges
//                   .where(
//                       (docChange) => docChange.type == DocumentChangeType.added)
//                   .toList();

//               if (addedDocs.isNotEmpty) {
//                 if (latestAddedDocument!.timeStamp <=
//                     ChatMessage.fromJson(
//                             addedDocs[0].doc.data() as Map<String, dynamic>)
//                         .timeStamp) {
//                   FirebaseChat.instance.updateLastSeenMsgUser(
//                       _selfProfile.id,
//                       widget.roomId,
//                       snapshot.data!.docs.first['timeStamp'].toString(),
//                       ChatMessage.fromJson(
//                           addedDocs[0].doc.data() as Map<String, dynamic>));
//                   latestAddedDocument = ChatMessage.fromJson(
//                       addedDocs[0].doc.data() as Map<String, dynamic>);
//                    //print('Added document: ${addedDocs[0].doc.data()}');
//                 }
//                 // Process the newly added documents
//                 // for (final addedDoc in addedDocs) {
//                 // }
//               }

//               return Column(
//                 children: [
//                   Expanded(
//                       child: Stack(
//                     children: [
//                       Column(children: [
//                         snapshot.data!.size == 0
//                             ? new Container(
//                                 child: Center(child: Text("No Messages yet")),
//                                 margin: EdgeInsets.only(
//                                     top: MediaQuery.of(context).size.height *
//                                         0.3),
//                               )
//                             : Expanded(
//                                 child: Stack(
//                                   children: [
//                                     ListView.builder(
//                                       // Add the controller to the ListView
//                                       controller: _scrollController,
//                                       reverse: true,
//                                       shrinkWrap: true,
//                                       itemCount: snapshot.data!.size,
//                                       itemBuilder: ((context, index) {
//                                         ChatMessage message =
//                                             ChatMessage.fromJson(snapshot
//                                                     .data!.docs[index]
//                                                     .data()
//                                                 as Map<String, dynamic>);
//                                         return Container(
//                                           child: messageBubble(message),
//                                         );
//                                       }),
//                                     ),
//                                     Positioned(
//                                       top: 3,
//                                       left: 0,
//                                       right: 0,
//                                       child: _isLoading
//                                           ? Center(
//                                               child:
//                                                   CircularProgressIndicator(),
//                                             )
//                                           : Container(),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                         SizedBox(
//                           height: 65,
//                         )
//                       ]),
//                       MessageTypeBox(
//                           selfProfile: _selfProfile,
//                           roomId: widget.roomId,
//                           title: widget.title)
//                     ],
//                   )),
//                 ],
//               );
//             }));
//   }
// }
