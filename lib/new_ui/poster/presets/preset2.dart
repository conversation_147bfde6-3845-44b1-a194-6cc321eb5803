import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:flutter/material.dart';

class Preset2 extends StatelessWidget {
  final String profileUrl;
  final String name;
  bool minimize = false;

  Preset2(
      {Key? key,
      required this.profileUrl,
      required this.name,
      this.minimize = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.transparent,
      child: Stack(
        children: [
          Positioned(
              bottom: 0,
              child: Container(
                height: 60,
                // width: double.infinity,
                width: MediaQuery.of(context).size.width,
                color: PRIMARY_COLOR_ORANGE,
              )),
          Positioned(
            left: MediaQuery.of(context).size.width * 0.34,
            bottom: 30,
            child: Container(
              width: minimize ? 70 : 120,
              height: minimize ? 70 : 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(width: 2, color: Colors.white),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(profileUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          Positioned(
            // left: MediaQuery.of(context).size.width * 3 / 4 * 0.5,
            bottom: 2,
            child: Container(
                width: MediaQuery.of(context).size.width,
                child: Center(
                    child: Text(
                  name,
                  style:  TextStyle(fontFamily: 'Gilroy',
                      fontSize: minimize ? 14 : 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.white),
                ))),
          ),
        ],
      ),
    );
  }
}
