import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:flutter/material.dart';

class Preset1 extends StatelessWidget {
  final String profileUrl;
  final String name;
  bool minimize = false;
  String ? netaName ;
  Preset1(
      {Key? key,
      this.profileUrl = USERIMAGE_LINK,
      required this.name,
      this.minimize = false ,   this.netaName})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.transparent,
      // height: MediaQuery.of(context).size.width * 3 / 4,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            bottom: 0,
            child: Container(
                width: MediaQuery.of(context).size.width,
                height:  MediaQuery.of(context).size.height*0.08,
                padding: minimize
                    ? EdgeInsets.only(left:  MediaQuery.of(context).size.height*0.13, top: MediaQuery.of(context).size.height*0.01)
                    : EdgeInsets.only(left: MediaQuery.of(context).size.height*0.16, top: 10),
                color:Color(0xff231864),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      name ,
                      style:  TextStyle(fontFamily: 'Gilroy-Bolds',
                            fontSize: MediaQuery.of(context).size.height*0.019 ,
                         
                          color: Colors.white),
                    ),
                   
                    Text(
                             netaName!  ?? '',style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.white ,  fontSize: MediaQuery.of(context).size.height*0.027  , fontWeight: FontWeight.w500,),
                          ),
                  ],
                )),
          ),
          Positioned(
            left:  MediaQuery.of(context).size.height*0.01,
            bottom:  MediaQuery.of(context).size.height*0.005 ,
            child: Container(
              width: MediaQuery.of(context).size.height*0.11 ,
              //  minimize ? 110 : 120,
              height: MediaQuery.of(context).size.height*0.11 ,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(width: 2, color: Colors.white),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(profileUrl  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class Preset11 extends StatelessWidget {
  final String profileUrl;
  final String name;
  bool minimize = false;
  String ? netaName ;
  Preset11(
      {Key? key,
      this.profileUrl = USERIMAGE_LINK,
      required this.name,
      this.minimize = false ,   this.netaName})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.transparent,
      // height: MediaQuery.of(context).size.width * 3 / 4,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            bottom: 0,
            child: Container(
                width: MediaQuery.of(context).size.width,
                height: minimize ? 70 : 70,
                padding: minimize
                    ? EdgeInsets.only(left: 120, top: 10)
                    : EdgeInsets.only(left: 130, top: 10),
                color: PRIMARY_COLOR_ORANGE,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style:  TextStyle(fontFamily: 'Gilroy',
                          fontSize:   22 ,
                          // fontWeight: FontWeight.bold,
                          color: Colors.white),
                    ),
                    Text(
                       'Team Monika Tai',style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white , fontSize: 22 , fontWeight: FontWeight.bold ),
                          ),
                  ],
                )),
          ),
          Positioned(
            left: 2,
            bottom: -5,
            child: Container(
              width: minimize ? 110 : 120,
              height: minimize ? 110 : 160,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(width: 2, color: Colors.white),
                image: DecorationImage(
                  image: CachedNetworkImageProvider(profileUrl  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
