import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_social_share_plugin/flutter_social_share.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/PosterV2.dart';
import 'package:mla_connect/new_ui/poster/presets/preset1.dart';
import 'package:mla_connect/new_ui/poster/presets/preset2.dart';
import 'package:mla_connect/new_ui/poster/presets/preset3.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:mla_connect/utils/shareUtil.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
 
import 'package:image_picker/image_picker.dart';
import 'dart:ui' as ui;

import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

class PosterView extends StatefulWidget {
    String profileURl ;
    String posterID;
     String posterURl ;
    String name ;
    String captionText ;
  // PosterNetworkModelV2? model;
  PosterView({Key? key ,required this.posterID ,required this.profileURl , required this.name ,required this.posterURl ,required this.captionText}) : super(key: key);

  @override
  State<PosterView> createState() => _PosterViewState();
}

class _PosterViewState extends State<PosterView> {
  // void generatePreviewFromModel() async {
  //   try {
  //     setState(() {
  //       error = null;
  //       generating = true;
  //     });

  //     final preview = await PosterUtils.overlapPosterFromModel(
  //         widget.model!,
  //         widget.profile?.profileUrl ?? USERIMAGE_LINK,
  //         widget.profile?.name ?? "");

  //     if (preview != null) {
  //       generating = false;
  //       previewPath = preview;
  //     } else {
  //       error = "Error Creating Preview";
  //     }
  //   } catch (e) {
  //     // error = e.toError();
  //   } finally {
  //     if (mounted) setState(() {});
  //   }
  // }
  var presets = [];
  int? presetIndex;
    LoginService login  = LoginService() ;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () async {
      // generatePreviewFromModel();
      setState(() {
        generating = false;
      });
    });
    
    presets = [
      Preset11(
        profileUrl: widget.profileURl,
        name: widget.name,
      ),
      Preset2(
          profileUrl: widget.profileURl, name: widget.name),
      Preset3(
          profileUrl: widget.profileURl, name: widget.name)
    ];
    presetIndex = 0;
  }

  // final profileUrl = widget.profile?.profileUrl;
  // final profileName = widget.profile?.name ?? "";
  bool generating = true;
  String? previewPath;
  String? error;
  GlobalKey _posterkey = GlobalKey();
 Future<bool> sharePoster(String type) async {
     //print("CLICKED");
    try {
      final boundary = _posterkey.currentContext?.findRenderObject();
      double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      if (boundary is RenderRepaintBoundary) {
        ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
        ByteData? byteData =
            await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List? pngBytes = byteData?.buffer.asUint8List();

        final directory =
            (await getTemporaryDirectory()).path; //from path_provide package
        final fileName = "poster.png";
        final path = '$directory/$fileName';

        await File(path).writeAsBytes(pngBytes!, flush: true);

        if (type == 'facebook') {
          await Share.shareXFiles([XFile(path)], text: widget.captionText);
return true; 
     
        } else if (type =="whatsapp") {
           await Share.shareXFiles([XFile(path)], text: widget.captionText);
          // FlutterSocialShare().shareToWhatsApp(
          //     msg:
          //         "${widget.captionText} \n Download your poster now $SHARE_URL",
          //     imagePath: "$path");
           return true; 
        } else {
          await Share.shareXFiles([XFile(path)], text: widget.captionText);
         return true; 
        }
      }
    } catch (e) {
       //print("ERROR COMING INSIDE CATCH");
      // context.snackBar(e.toError());
 return false; 
    }
    return false; 
    //  //printfSd");
      // ShareUtil.sharePoster(type, widget.model!, widget.profileURl);
  }
 
  copyCaption() async {
    Clipboard.setData(ClipboardData(text: widget.captionText)).then((_) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text("Caption copied")));
    });
  }

  double get screenWidth => MediaQuery.of(context).size.width;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            title: Text("Poster"),
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                Navigator.pop(context, true);
              },
              icon: Icon(Icons.arrow_back),
              color: Colors.black,
            )),
        body: SingleChildScrollView(
          child: Column(children: [
            Card(
                elevation: 1,
                child:
                //  error != null
                //     ? Padding(
                //         padding: const EdgeInsets.all(8.0),
                //         child: Text(error!),
                //       )
                //     : 
                    (generating
                        ? CircularProgressIndicator()
                        :
                         RepaintBoundary(
                            key: _posterkey,
                            child: Stack(
                              children: [
                                AspectRatio(
                                    aspectRatio: 10 / 9,
                                    child: CachedNetworkImage(
                                      imageUrl:
                                      widget.posterURl
                                      // "https://media.istockphoto.com/id/1354898581/photo/shot-of-a-young-businessman-using-a-laptop-in-a-modern-office.jpg?s=612x612&w=0&k=20&c=dDDNcvIoG-4VdO01ZlENqODBoNocT434vIFp0duuTZM=",
                                    )),
                                presetIndex == null
                                    ? Container()
                                    : GestureDetector(
                                        onHorizontalDragEnd:
                                            (DragEndDetails details) {
                                          if (details.primaryVelocity! < 0) {
                                            // User swiped Left
                                            setState(() {
                                              if (presetIndex! <
                                                  presets.length - 1) {
                                                presetIndex = presetIndex! + 1;
                                              }
                                            });
                                          } else if (details.primaryVelocity! >
                                              0) {
                                            setState(() {
                                              if (presetIndex! > 0) {
                                                presetIndex = presetIndex! - 1;
                                              }
                                            });
                                          }
                                           //print("preset Index ${presetIndex!}");
                                        },
                                        child: AspectRatio(
                                            aspectRatio: 10 / 9,
                                            child: presets[presetIndex!])),
                              ],
                            )))),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: presets.asMap().entries.map((entry) {
                return Container(
                    // color: Colors.black38,
                    width: presetIndex == entry.key ? 16 : 12.0,
                    height: presetIndex == entry.key ? 16 : 12.0,
                    margin:
                        EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                    decoration: BoxDecoration(
                        // shape: BoxShape.circle,
                        borderRadius: BorderRadius.circular(100),
                        // borderRadius: BorderRadius.circular(2),
                        color: presetIndex == entry.key
                            ? PRIMARY_COLOR_ORANGE.withOpacity(1.0)
                            : Colors.black.withOpacity(0.4)));
              }).toList(),
            ),
            Padding(
                padding: EdgeInsets.all(20),
                child: GestureDetector(
                    onTap: () {
                      copyCaption();
                    },
                    child: Text(
                      widget.captionText,
                      style:  TextStyle(fontFamily: 'Gilroy',
                          fontWeight: FontWeight.normal, fontSize: 16),
                    ))),
            Text(
              "Share",
              style:  TextStyle(fontFamily: 'Gilroy',fontSize: 24),
            ),
            SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // SizedBox(
                //   width: 20,
                // ),
                GestureDetector(
                    onTap: ()async {
                    var posterstatus =await  sharePoster("whatsapp");
                    if(posterstatus==true) {
                     var res= await login.updatePoster(context, widget.posterID) ;
                     if(res ['status'] ==403) {
 
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                     
                     }
                    }else{
                       final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('Something Went wrong!'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
                    },
                    child: Image.asset(
                      "assets/icons/whatsapp.png",
                      height: 40,
                      width: 40,
                    )
                    // Container(
                    //     height: 50,
                    //     width: 50,
                    //     decoration: new BoxDecoration(
                    //       color: PRIMARY_COLOR_ORANGE,
                    //       shape: BoxShape.circle,
                    //     ),
                    //     child: Padding(
                    //       padding: EdgeInsets.all(8),
                    //       child: ImageIcon(
                    //           AssetImage("assets/icons/whatsapp_outline.png"),
                    //           color: Colors.white),
                    //     ))
                    ),
                SizedBox(
                  width: 20,
                ),
                GestureDetector(
                  onTap: ()async {
                 var posterstatus =   await  sharePoster("facebook");
                    if(posterstatus==true) {
                      
                       var res= await login.updatePoster(context, widget.posterID) ;
                     if(res ['status'] ==403) {
 
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                     
                     }
                    }  else{
                       final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('Something Went wrong!'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }  
                     //print(posterstatus);
                  },
                  child: Image.asset(
                    "assets/icons/facebook_icon.png",
                    height: 40,
                    width: 40,
                  ),
                  // child: Container(
                  //     height: 50,
                  //     width: 50,
                  //     decoration: new BoxDecoration(
                  //       color: PRIMARY_COLOR_ORANGE,
                  //       shape: BoxShape.circle,
                  //     ),
                  //     child: Padding(
                  //       padding: EdgeInsets.all(8),
                  //       child: ImageIcon(
                  //           AssetImage("assets/icons/facebook_outline.png"),
                  //           color: Colors.white),
                  //     ))
                ),
                SizedBox(
                  width: 20,
                ),
                GestureDetector(
                    onTap: ()async {
                     var posterstatus = await sharePoster("facebook");
                     if(posterstatus==true) {
                    
                       var res= await login.updatePoster(context, widget.posterID) ;
                     if(res ['status'] ==403) {
 
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                     
                     }
                    }else{
                       final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('Something Went wrong!'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
                       //print(posterstatus);
                    },
                    child: Image.asset(
                      "assets/icons/Send.png",
                      height: 40,
                      width: 40,
                    )
                    //      Container(
                    // height: 50,
                    // width: 50,
                    // decoration: new BoxDecoration(
                    //   color: PRIMARY_COLOR_ORANGE,
                    //   shape: BoxShape.circle,
                    // ),
                    // child: Padding(
                    //   padding: EdgeInsets.all(10),
                    //   child: ImageIcon(AssetImage("assets/icons/Send.png"),
                    //       color: Colors.white),
                    // ))
                    ),
              ],
            ),
            SizedBox(height: 40)
          ]),
        ));
  }
}
