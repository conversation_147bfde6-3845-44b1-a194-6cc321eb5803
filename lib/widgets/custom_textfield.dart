import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomTextFormField extends StatelessWidget {
  final String label ;
  final String hint ;
  String? Function(String?)? validator ;
  TextEditingController? controller ;
  double ? h ;
  double ? v ;
   void Function(String)? onchanged ;

    CustomTextFormField({super.key, required this.label, required this.hint , this.onchanged ,  this.validator ,   this.controller , this.h , this.v});
  @override
  Widget build(BuildContext context) {
    return Padding(
         padding:   EdgeInsets.symmetric(
                    horizontal: h==null ? 20: h  as double   ,
                  vertical: v==null ? 10 :   v as double ,
                  ),
      child: TextFormField(
        onChanged: onchanged,
        controller:controller ,
        validator: validator,
        // autofocus: true,
        decoration: InputDecoration(
          labelText: label,
          labelStyle:  TextStyle(fontFamily: 'Gilroy-SemiBold',color: Colors.black , fontWeight:  FontWeight.bold),
          hintText: hint,
          
          hintStyle:  TextStyle(fontFamily: '<PERSON>roy-<PERSON>Bold',color: Color(0xFF838383)),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5.0),
            borderSide: BorderSide(
              color: Color(0xFFE8E8E8),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5.0),
            borderSide: BorderSide(
              color: Color(0xFFE8E8E8),
            ),
          ),
        ),
      ),
    );
  }
}
class READCustomTextFormField extends StatelessWidget {
  final String label ;
  final String hint ;
  String? Function(String?)? validator ;
  TextEditingController? controller ;

    READCustomTextFormField({super.key, required this.label, required this.hint ,   this.validator ,   this.controller});
  @override
  Widget build(BuildContext context) {
    return Padding(
         padding: const EdgeInsets.symmetric(
                    horizontal:  0,
                   
                  ),
      child: TextFormField(
        controller:controller ,
        validator: validator,
        autofocus: true,
        showCursor: false,
        readOnly: true,
        decoration: InputDecoration(
          labelText: label,
          labelStyle:  TextStyle(fontFamily: 'Gilroy-SemiBold',color: Colors.black , fontWeight:  FontWeight.bold),
          hintText: hint,
          
          hintStyle:  TextStyle(fontFamily: 'Gilroy-SemiBold',color: Color(0xFF838383)),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5.0),
            borderSide: BorderSide(
              color: Color(0xFFE8E8E8),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5.0),
            borderSide: BorderSide(
              color: Color(0xFFE8E8E8),
            ),
          ),
        ),
      ),
    );
  }
}