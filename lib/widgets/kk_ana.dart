 import 'package:mla_connect/new_ui/analytics/overall_analytics.dart';
 
import 'package:flutter/material.dart';
 

class KKAna extends StatefulWidget {
  var   netaAna ;
  int totalNumber ;
    KKAna({Key? key , required  this.netaAna , required this.totalNumber}) : super(key: key);

  @override
  State<KKAna> createState() => _KKAnaState();
}
var percentage ;
      var responsiblepercentage ;
      getData(){}
class _KKAnaState extends State<KKAna> {


  @override
  Widget build(BuildContext context) {
   percentage = (widget.totalNumber != 0) ? (widget.netaAna["edited"] / widget.totalNumber) * 100 : 0;
   responsiblepercentage = (widget.totalNumber != 0) ? (widget.netaAna['responsibleFor'] / widget.totalNumber) * 100 : 0; 
    return   Scaffold(
     
            appBar: AppBar(title: Text("Analytics"),centerTitle: true,elevation: 0, backgroundColor: Color(0xff4C3AB4),),
      body:  widget.netaAna==null ? Center(child: CircularProgressIndicator( )): Padding(
      padding: const EdgeInsets.all(10),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center
          ,
          children: [
              SizedBox(
                        height: 20,
                      ),
                        Container(
                      width: double.infinity,
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                            borderRadius: BorderRadius.circular(5),
                            ),
                        child:    Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
Text(
                      "No. of Beneficiaries : ",
                      style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 10,),
                          // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                          //  Text("Beneficiary Number  : ${kkAnalytics["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                          Row(
                            children: [
                              Text( percentage.toStringAsFixed(2),  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),),

                             widget.totalNumber==0 ?    SizedBox() : CustomLinearProgressIndicator(
                                          totalNumber: 
                                          widget.totalNumber,
                                          actualNumber: widget.netaAna["edited"]),
                            ],
                          )
                        ],)),
                    //   Text(
                    //     "Booth Mapping Completed",
                    //     style:  TextStyle(fontFamily: 'Gilroy',
                    //       fontSize: 20,
                    //       fontWeight: FontWeight.bold,
                    //       color: Colors.black,
                    //     ),
                    //   ),
                    //   SizedBox(
                    //     height: 10,
                    //   ),
                    //  widget.totalNumber==0 ? Container(
                    //       padding: const EdgeInsets.all(10),
                    //       decoration: BoxDecoration(
                    //           borderRadius: BorderRadius.circular(20),
                    //           border: Border.all(color: Colors.grey)),
                    //       child: CustomLinearProgressIndicator(
                    //           totalNumber:
                    //              1,
                    //           actualNumber:0 ,
                    //           )) :  Container(
                    //       padding: const EdgeInsets.all(10),
                    //       decoration: BoxDecoration(
                    //           borderRadius: BorderRadius.circular(20),
                    //           border: Border.all(color: Colors.grey)),
                    //       child: Column(
                    //         mainAxisAlignment: MainAxisAlignment.start,
                    //         crossAxisAlignment: CrossAxisAlignment.start,
                    //         children: [

                    //         Text("Total voter : ${widget.netaAna["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                    //         Text("Mapped voter  : ${widget.netaAna["edited"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                    //         Text("Mapping percentage : ${percentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),)
                    //       ],)),
                        
                     SizedBox(
                        height: 10,
                      ),
                         Container(
                      width: double.infinity,
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Color(0xffF6F6F6),
                            borderRadius: BorderRadius.circular(5),
                            ),
                        child:    Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
Text(
                      "Responsible For :  ",
                      style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 10,),
                          // Text("Total : ${widget.aaggregatedDetails["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                          //  Text("Beneficiary Number  : ${kkAnalytics["beneficiary"]}" , style:  TextStyle(fontFamily: 'Gilory-Bold',fontSize: 24),) ,
                          Row(
                            children: [
                              Text( percentage.toStringAsFixed(2).toString(),  style:  TextStyle(fontFamily: 'Gilory-Bold-Bold',fontSize: 14 , fontWeight: FontWeight.bold),),

                             widget.totalNumber==0 ?    SizedBox() : CustomLinearProgressIndicator(
                                          totalNumber: 
                                          widget.totalNumber,
                                          actualNumber:widget.netaAna['responsibleFor']),
                            ],
                          )
                        ],)),
                    //   Text(
                    //     "Responsible For",
                    //     style:  TextStyle(fontFamily: 'Gilroy',
                    //         fontSize: 20,
                    //         fontWeight: FontWeight.bold,
                    //         color: Colors.black),
                    //   ),
                    //   SizedBox(
                    //     height: 10,
                    //   ),
                    // widget.totalNumber==0 ?Container(
                    //       padding: const EdgeInsets.all(10),
                    //       decoration: BoxDecoration(
                    //           borderRadius: BorderRadius.circular(20),
                    //           border: Border.all(color: Colors.grey)),
                    //       child: CustomLinearProgressIndicator(
                    //           totalNumber:
                    //              1,
                    //           actualNumber:0 ,
                    //           )) :  Container(
                    //       padding: const EdgeInsets.all(10),
                    //       decoration: BoxDecoration(
                    //           borderRadius: BorderRadius.circular(20),
                    //           border: Border.all(color: Colors.grey)),
                    //       child: Column(
                    //         mainAxisAlignment: MainAxisAlignment.start,
                    //         crossAxisAlignment: CrossAxisAlignment.start,
                    //         children: [

                    //         // Text("Total voter : ${widget.netaAna["totalCount"]}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                    //         Text("Mapped voter  : ${widget.netaAna['responsibleFor']}" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),) ,
                    //         Text("Mapping percentage : ${responsiblepercentage.toStringAsFixed(2)}%" , style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16),)
                    //       ],)
                          // CustomLinearProgressIndicator(
                          //     totalNumber:
                          //       widget.totalNumber,
                          //     actualNumber:widget.netaAna['responsibleFor'] ,
                          //     )
                              // ),
    //         SizedBox(
    // height: 110,
    //  child: ListView.builder(
    //   itemCount:  widget.netaAna["pointsLeaderBoard"].length,
    //   itemBuilder: (context, index) {
 
    //     return ListTile(
    //       title: Container( 
    //         padding: EdgeInsets.all(20),
    //         // alignment: Alignment.topLeft,
    //         decoration: BoxDecoration(
    //           color: Colors.white,
    //           border: Border.all(
    //             color: Colors.grey, // Border color
    //             width: 1.0, // Border width
    //           ),
    //           borderRadius:
    //               BorderRadius.circular(30), // Optional: border radius
    //         ),
           
    //         child: Row( 
    //           crossAxisAlignment: CrossAxisAlignment.center,
    //           mainAxisAlignment: MainAxisAlignment.start,
    //           children: [
        
    //             CachedNetworkImage(
    //               imageUrl: widget.netaAna["pointsLeaderBoard"][index]['profileImgUrl'],
    //               // imageBuilder: (context, imageProvider) => Container(child: ,),
    //               // progressIndicatorBuilder: (context, url, downloadProgress) =>
    //               //     CircularProgressIndicator(value: downloadProgress.progress),
    //               errorWidget: (context, url, error) =>
    //                   Icon(Icons.error),
    //               width: 50,
    //               fit: BoxFit.fill,
    //             ),
    //             // SizedBox(
    //             //   width: 30,
    //             // ),
    //         SizedBox(width: 20,),   
    //              Column(
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //           mainAxisAlignment: MainAxisAlignment.start,
    //               children: [  Text('Name: ${widget.netaAna["pointsLeaderBoard"][index]['name'].toString()}'),
    //             SizedBox(
    //               height: 3,
    //             ),
    //             // Text('Number: ${kk['users'][index]['number']}'),
    //             SizedBox(
    //               height: 3,
    //             ),
    //               Text('Total Points : ${widget.netaAna["pointsLeaderBoard"][index]['count'].toString()}'),],
    //              ) ],
    //         ),
    //       ),
    //     );
    //   },
    //   ),
    //  ),
           

      //     Text("Beneficiary",style:  TextStyle(fontFamily: 'Gilroy',fontSize: 30 ,fontWeight: FontWeight.bold  ,color: Colors.black,letterSpacing: 5),),
      //     SizedBox(height: 20,),Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount:widget.netaAna["totalCount"] , beneficiaryCount:widget.netaAna["beneficiary"],  firstname: 'Beneficiary',secondname: 'Non-beneficiary')) ,
      //    SizedBox(height: 20,), Text("Profile Edit",style:  TextStyle(fontFamily: 'Gilroy',fontSize: 30 ,fontWeight: FontWeight.bold  ,color:  Colors.black,letterSpacing: 5),),
      //     SizedBox(height: 20,), Container( decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),child: MyHomePage(totalCount: widget.netaAna["totalCount"], beneficiaryCount:widget.netaAna["edited"] ,firstname: "Profile edited", secondname: "Non-edited",)),
      //  SizedBox(height: 20,),
          
      //          Text("Party Bifurcation",style:  TextStyle(fontFamily: 'Gilroy',fontSize: 30 ,fontWeight: FontWeight.bold  ,color:  Colors.black,letterSpacing: 5),),
      //         SizedBox(height: 20),
      //         Container(
      //           // height: 300,
      //           // width: 300,
      //         decoration: BoxDecoration(border: Border.all(color: Colors.black) ,color: Colors.white),
      //           child: SfCartesianChart(
      //             primaryXAxis: CategoryAxis(),
      //             series: <ChartSeries>[
      //               ColumnSeries<SalesData, String>(
      //                 dataSource: chartData,
      //                 xValueMapper: (SalesData sales, _) => sales.category,
      //                 yValueMapper: (SalesData sales, _) => sales.sales,
      //                 dataLabelSettings: DataLabelSettings(isVisible: true),
      //               ),
                    
      //             ],
      //           ),
      //         ),
         ]),
      ),
    ),);;
  }
}