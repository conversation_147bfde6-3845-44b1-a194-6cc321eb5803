import 'package:flutter_svg/svg.dart';
import 'package:mla_connect/screens/leaderboard/user_leaderboard_page.dart';
import 'package:mla_connect/widgets/rank_con.dart';
import 'package:flutter/material.dart';

class UserLeaderboard extends StatelessWidget {
  var homeData;
  bool isfirst;
  UserLeaderboard({Key? key, required this.homeData, required this.isfirst})
      : super(key: key);
  String capitalizeWords(String name) {
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  @override
  Widget build(BuildContext context) {
    var screenSize = MediaQuery.of(context).size;
    //  //print(homeData);
    //  //print(homeData["posterLeaderBoard"]);
    return Container(
        padding:
            EdgeInsets.only(top: screenSize.height * 0.01),
        // color: Color.fromARGB(
        // 255, 208, 208, 208),
        // height: MediaQuery.of(context)
        //         .size
        //         .height   ,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          // color: Color(0xff4C3AB4)
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.yellow,
              Color.fromARGB(255, 241, 228, 106),
              Color.fromARGB(255, 241, 228, 106),
            ],
          ),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                Text('User LeaderBoard',
                    style: TextStyle(
                        fontFamily: 'Gilroy-Bold',
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                        fontSize: screenSize.height * 0.025)),
                SizedBox(
                  height: screenSize.height * 0.02,
                ),
                Row(
                  children: [
                    SizedBox(
                      width: screenSize.height * 0.01,
                    ),
                    homeData["posterLeaderBoard"].length == 1
                        ? SizedBox()
                        : Column(
                            // crossAxisAlignment:
                            //     CrossAxisAlignment.center,
                            // mainAxisAlignment:
                            //     MainAxisAlignment.center,
                            children: [
                              RankCon(
                                  profileUrl: homeData["posterLeaderBoard"][1]
                                      ["profileImgUrl"],
                                  colors: Color(0xff009BD6),
                                  text: "2",
                                  name: capitalizeWords(
                                      homeData["posterLeaderBoard"][1]['name']),
                                  point: homeData["posterLeaderBoard"][1]
                                          ['count']
                                      .toString()),
                            ],
                          ),
                    Stack(
                      children: [
                        RankCon(
                            profileUrl: homeData["posterLeaderBoard"][0]
                                ["profileImgUrl"],
                            colors: Color(0xffFFAA00),
                            text: "1",
                            name: capitalizeWords(
                                homeData["posterLeaderBoard"][0]['name']),
                            point: homeData["posterLeaderBoard"][0]['count']
                                .toString()),
                      ],
                    ),
                    homeData["posterLeaderBoard"].length == 1 ||
                            homeData["posterLeaderBoard"].length == 2
                        ? SizedBox()
                        : RankCon(
                            profileUrl: homeData["posterLeaderBoard"][2]
                                ["profileImgUrl"],
                            colors: Color(0xff00D95F),
                            text: "3",
                            name: capitalizeWords(
                                homeData["posterLeaderBoard"][2]['name']),
                            point: homeData["posterLeaderBoard"][2]['count']
                                .toString()),
                  ],
                ),
                isfirst == true
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width*0.4,
                        height: screenSize.height * 0.045,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.push(context, MaterialPageRoute(
                              builder: (context) {
                                return UserLeaderboardPage(homeData: homeData);
                              },
                            ));
                          },
                          child:   Text(
                            "See List >",
                            style: TextStyle(
                              color: Colors.white,
                                fontFamily: 'Gilroy-Bold',
                                fontSize:
                                    screenSize.height * 0.02),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xff231864),
                            // onPrimary: Colors.black,
                            // side: BorderSide(color: Colors.white, width: 2), // White border
                          ),
                        ),
                      )
                    : SizedBox(
                        height: screenSize.height * 0.045,
                      )
              ],
            ),
            Positioned(
                left: screenSize.height * 0.193,
                bottom: screenSize.height * 0.166,
                child: SvgPicture.asset(
                  'assets/icons/kingc.svg',
                  height: screenSize.height * 0.025,
                )),
            Positioned(
                left: screenSize.height * 0.192,
                top: screenSize.height * 0.025,
                bottom: screenSize.height * 0.01,
                child: SvgPicture.asset(
                  'assets/1st (1).svg',
                  height: screenSize.height * 0.03,
                )),
            Positioned(
                left: screenSize.height * 0.325 ,
                top: screenSize.height * .015,
                bottom: screenSize.height * 0.01,
                child: SvgPicture.asset(
                  'assets/3rd.svg',
                  height: screenSize.height * 0.03,
                )),
            Positioned(
                left: screenSize.height * 0.06,
                top: screenSize.height * 0.018,
                bottom: screenSize.height * 0.01,
                child: SvgPicture.asset(
                  'assets/2nd.svg',
                  height: screenSize.height * 0.03,
                ))
          ],
        ));
  }
}
