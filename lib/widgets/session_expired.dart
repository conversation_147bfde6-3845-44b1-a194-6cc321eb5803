 
 

import 'package:mla_connect/new_ui/onboarding/phoneInput.dart';
import 'package:mla_connect/screens/onBoarding_page.dart';
import 'package:flutter/material.dart';

class SessionExpiredDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // Disable back button
      child: AlertDialog(
        title: Text('Session Expired'),
        content: Text('Your session is expired, please login again.'),
        actions: <Widget>[
          ElevatedButton(
            onPressed: () {
                  Navigator.of(context, rootNavigator: true)
                          .pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (BuildContext context) {
                            return PhoneInput();
                          },
                        ),
                        (_) => false,
                      );
            },
            child: Text('Login'),
          ),
        ],
      ),
    );
  }
}
