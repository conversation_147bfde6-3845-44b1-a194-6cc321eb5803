 

import 'package:cached_network_image/cached_network_image.dart';

import 'package:flutter/material.dart';
 

class IDCard extends StatefulWidget {
  IDCard(
      {Key? key,
      required this.cardKey,
      required this.position,
      required this.name,
      required this.profilePicUrl,
      required this.number,
      required this.dob,
      // required this.netaprofilePicUrl,
      this.showAssetProfilePic = false})
      : super(key: key);
  Key cardKey;
  String profilePicUrl;
  String name;
  String position;
  // String netaprofilePicUrl ;
  bool showAssetProfilePic;
  String number ;
  String dob ;

  @override
  State<IDCard> createState() => _IDCardState();
}

class _IDCardState extends State<IDCard> {
   
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // retrieveStoredName();
  }
  // void retrieveStoredName() async {
  //    //print("coming here ");
  //   String? retrievedName = await retrieveValue('user_name');
  //    //print(" name here : $retrievedName");
  //   setState(() {
  //     name = retrievedName ?? 'Name not found';
  //   });
  // }
  

//   Future<String> retrieveValue(String username ) async {
//   var dbFactory = databaseFactoryIo;
//   Directory appDocDir = await getApplicationDocumentsDirectory();
//   String databasePath = appDocDir.path + '/database.db';
//   var dbPath = databasePath;
//   Database db = await dbFactory.openDatabase(dbPath);

//   var store = StoreRef.main();

//   // Ensure key is not null or empty before attempting to retrieve the record
//   if (username != null && username.isNotEmpty) {
//     var record = await store.record(username).get(db);
//     await db.close();
//     return record as String;
//   } else {
     
//      //print("Invalid key");
//     await db.close();
//     return 'null'; // or throw an error, depending on your use case
//   }
// }
String capitalizedName = "";
String capitalizeWords(String name) {
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }
String formatName(String name) {
  List<String> parts = name.split(" ");
  if (parts.length > 1) {
    // Ensure the second part is uppercase
    parts[1] = parts[1].toUpperCase();
  }
  // Capitalize the first part
  parts[0] = capitalize(parts[0]);
  return parts.join(" ");
}

String capitalize(String s) {
  if (s.isEmpty) return s;
  return s[0].toUpperCase() + s.substring(1).toLowerCase();
}
  @override
  Widget build(BuildContext context) {
    
    return RepaintBoundary(
        key: widget.cardKey,
        child: Container(
            // width: 350,
            padding: EdgeInsets.symmetric(horizontal: 15),
            height: MediaQuery.of(context).size.height*0.3,
            child: Stack(
              children: [
                // Container(
                // width: 360,
                // height: 195,
                // child: SvgPicture.asset('assets/bgidCard.svg')
                // Positioned(
                //     top: 25,
                //     left: 10,
                //     child:
                //      Image.asset(
                //       // 'assets/background/id_card_shivsena.jpg',
                //       'assets/bgbf.jpeg',
                //       height: 160,
                //       width: 330,
                //       fit: BoxFit.fill,
                //       // fit: BoxFit.fitHeight,
                //      )
                //     ),
                    // ),
                Container(
                    // width: MediaQuery.of(context).size.width*0.2,
                    height: MediaQuery.of(context).size.height*0.3,
                    decoration: BoxDecoration(border:Border.all(color: Color(0xffEEEEEE ,), width: 3),
                     borderRadius: BorderRadius.circular(10),
                    image: DecorationImage(image:  AssetImage (
                  'assets/background/bgm.jpeg',
                  // height: MediaQuery.of(context).size.height,
                  // width: MediaQuery.of(context).size.width,
                  // fit: BoxFit.fill,
                  // color: Colors.black12,
                  // height: 170,
                ),fit: BoxFit.fill)),  ) ,
                
              Positioned(
                    top: MediaQuery.of(context).size.height*0.023,
                    left: MediaQuery.of(context).size.height*0.022,
                    child:Container(
                      width: MediaQuery.of(context).size.width*0.8,
                       padding: EdgeInsets.all(7),
                     
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(5), color:   Color(0xff4C3AB4),),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Container(
                          //       width: 50,
                          //       height: 50,
                              
                          //       decoration: BoxDecoration(
                          //         shape: BoxShape.circle,
                          //         border: Border.all(width: 2, color: Colors.white),
                                  // image: DecorationImage(
                                  //   image: CachedNetworkImageProvider(
                                  //       widget.netaprofilePicUrl),
                                  //   fit: BoxFit.cover,
                                  // ),)), 
                                  // SizedBox(width: 10,),
                                  Text(
                        formatName(widget.position) ,
                        style:  TextStyle(fontFamily: 'Gilroy-Bold',
                            color: Colors.white,
                            fontSize: 16, 
                            // letterSpacing: 1,
                            fontWeight: FontWeight.bold),
                      ),
                        ],
                      ),
                    )) ,  
                      widget.profilePicUrl.isEmpty?SizedBox()   :   Positioned(
                     top:MediaQuery.of(context).size.height*0.1,
                    left:MediaQuery.of(context).size.height*0.021,
                    child:Container(
                          width:  MediaQuery.of(context).size.width*0.24 ,
                          height: MediaQuery.of(context).size.height*0.1 ,
                          decoration: BoxDecoration(
                            color: Colors.grey,
                            // shape: BoxShape.circle,
                            // border: Border.all(width: 2, color: Colors.black),
                            image: DecorationImage(
                              image: 
                              CachedNetworkImageProvider(
                                  widget.profilePicUrl  
                          ),
                                fit: BoxFit.contain,
                            ),))
                         
                     
                      ),
                    //  Positioned(
                    // top: 95,
                    // left: 15,
                    // child:Container(
                    //       width: MediaQuery.of(context).size.width*0.85,
                    //       height:MediaQuery.of(context).size.height*0.12,
                    //       decoration: BoxDecoration(
                    //          borderRadius: BorderRadius.circular(10),
                    //         border: Border.all(width: 1, color: Colors.black),
                    //         ))
                    
                    // ),
                Positioned(
                  top:MediaQuery.of(context).size.height*0.22 ,
                    left:MediaQuery.of(context).size.height*0.021,
                  child: SizedBox(
                    width: MediaQuery.of(context).size.height*0.11,
                    child: Text( 
                       capitalizeWords(widget.name),
                      maxLines: 1,
                      style:  TextStyle(fontFamily: 'Gilroy-Bold',
                          color: Colors.black,
                          fontSize: MediaQuery.of(context).size.height*0.02,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                 
                Positioned(
                  top: MediaQuery.of(context).size.height*0.25,
                  left:MediaQuery.of(context).size.height*0.021,
                  child: Text( 
                    '${widget.number }',
                    style:  TextStyle(fontFamily: 'Gilroy-Bold',
                        color: Colors.black,
                        fontSize:MediaQuery.of(context).size.height*0.02,
                        
                        fontWeight: FontWeight.bold),
                  ),
                ),
                // Positioned(
                //   top:MediaQuery.of(context).size.height*0.12,
                //     left:MediaQuery.of(context).size.height*0.03,
                //   child: Padding(
                //     padding: const EdgeInsets.all(4.0),
                //     child: Text( 
                //       '${widget.dob.capitalizeFirst}',
                //       style:  TextStyle(fontFamily: 'Gilroy-Bold',
                //           color: Colors.black,
                //           fontSize: 22,
                //           fontWeight: FontWeight.bold),
                //     ),
                //   ),
                // ),
                 
              ],
            )));
  }
}
