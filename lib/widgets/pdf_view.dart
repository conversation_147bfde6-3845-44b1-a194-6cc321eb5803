import 'dart:io';

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class PdfUrlViewer extends StatelessWidget {
  final String pdfUrl;
  const PdfUrlViewer(this.pdfUrl, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            child: SfPdfViewer.network(pdfUrl,
                pageLayoutMode: PdfPageLayoutMode.single)));
  }
}

class PdfFileViewer extends StatelessWidget {
  final File pdfFile;
  const PdfFileViewer(this.pdfFile, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            child: SfPdfViewer.file(pdfFile,
                pageLayoutMode: PdfPageLayoutMode.single)));
  }
}
