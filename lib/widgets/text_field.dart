import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class MyTextfield extends StatelessWidget {
  final String lable ;
  final String hint ;
  final TextEditingController controller ;
  final String? Function(String?)? validator ;

  const MyTextfield({Key? key ,required this.controller ,required this.hint ,required this.lable ,this.validator}) : super(key: key  );

  @override
  Widget build(BuildContext context) {
    return   SizedBox(
      height: MediaQuery.of(context).size.height*0.07,
      child: TextFormField(
        controller: controller ,
       validator:validator ,
              decoration: InputDecoration(
              
                labelText: lable,
                
                hintText:hint ,
                hintStyle:  TextStyle(fontFamily: 'Gilroy',color: Color(0xff838383),fontSize: 16),
                labelStyle:  TextStyle(fontFamily: '<PERSON><PERSON>',
          color: Colors.black,fontSize: 12,fontWeight: FontWeight.bold // Set label text color to black
      ),
      filled: true,
      enabled: true,
      fillColor: Colors.white,
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(color: Color(0xffE8E8E8)), // Black border color
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(color:  Color(0xffE8E8E8)), // Black border color when the field is focused
      ),
              ),
            ),
    );
  }
}