 
import 'dart:io';
import 'dart:ui';
import 'package:flutter_svg/svg.dart';
import 'package:mla_connect/controller/bottomBarController.dart';
import 'package:mla_connect/controller/home_controller.dart';
import 'package:flutter/material.dart';
 import 'package:flutter_social_share_plugin/file_type.dart';
import 'package:flutter_social_share_plugin/flutter_social_share.dart';

 
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/widgets/idCard.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
// import 'package:flutter_social_content_share/flutter_social_content_share.dart';
import 'dart:ui' as ui;
import 'package:mla_connect/utils/constants.dart';
import 'package:get/instance_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:social_share_plugin/social_share_plugin.dart';
import '../models/Post.dart';

shareIdCard(cardKey, context) async {
  try {
    final boundary = cardKey.currentContext?.findRenderObject();
    double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    if (boundary is RenderRepaintBoundary) {
      ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List? pngBytes = byteData?.buffer.asUint8List();

      final directory =
          (await getTemporaryDirectory()).path; //from path_provide package
      final fileName = "id_card.png";
      final path = '$directory/$fileName';

      await File(path).writeAsBytes(pngBytes!, flush: true);
      // final appUrl = FirebaseCloud.instance.createDynamicLink();
      
      FlutterSocialShare().shareToWhatsApp(msg: "ID Card", imagePath: "$path");
    }
  } catch (e) {
    context.snackBar("Something went wrong");
  }
}
shareIdCards(cardKey, context) async {

   //print("heeelloooo hhherreeeee") ;
  try {
      RenderRepaintBoundary boundary =
          cardKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage();
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        Uint8List pngBytes = byteData.buffer.asUint8List();
        await Share. 
        share(
          // pngBytes,
          'image.png',
          // 'Check out my ID Card!',
          // 'image/png',
        );
      }
    } catch (e) {
       //print(e.toString());
    }}



void sendToSetting() {
  BottomBarController _bottomBarController = Get.find<BottomBarController>();
  _bottomBarController.setCurrentPage(4);
}
 
void bottomSheetIdCard(context ) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  var name =   prefs.getString("Name");
  var number =   prefs.getString("number");
  var profileURl =    prefs.getString("profileUrl");
  var position =    prefs.getString("ROLEUSERAPI");
  var netaName =    prefs.getString("netaName");
  var netaImage =    prefs.getString("netaImage");
  var dob =    prefs.getString("dob");
   
//   //print("${name }   ${number}  ${profileURl}") ;
//   //print(netaImage);
    WidgetsBinding.instance.addPostFrameCallback((_) async { 
   
    //   //print(homeController.userDetails);
     final cardKey = GlobalKey();
    
    await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (builder) {
          return  
              SizedBox(
                height: MediaQuery.of(context).size.height*0.6,
                child: Column(
                  children: [
                    GestureDetector(
                      onTap:() {
                        Navigator.pop(context);
                      },   
                  child: Container(height: MediaQuery.of(context).size.height*0.06, width: MediaQuery.of(context).size.width*0.1,margin: EdgeInsets.only(left: 250), padding: EdgeInsets.all(10),decoration: BoxDecoration(shape: BoxShape.circle , color: Colors.white),child: SvgPicture.asset("assets/icons/cancel.svg"),)),
                 const  SizedBox(height: 20,) ,  
                    Container(
                       
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: Colors.white, ),
                      child:
                          Column(mainAxisAlignment: MainAxisAlignment.start, children: [
                          
                         SizedBox(
                          height: 10,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children:  [
                            Text(
                              "Your ID card is ready to be shared",
                              style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: MediaQuery.of(context).size.height*0.022 ,
                              fontWeight: FontWeight.w500),
                            ),
                           
                          ],
                        ),
                      const  SizedBox(
                          height: 10,
                        ),
                         
                           Container(
                              child: IDCard(
                                  cardKey: cardKey,
                                  position:  netaName!?? "",
                                  name: name!,
                                  showAssetProfilePic:
                                     profileURl == null ? true : false,
                                  profilePicUrl:profileURl?? "" 
                                  ,
                                  // netaprofilePicUrl:  netaImage ?? "", 
                                  number:  number !,
                                  dob:  dob!,)),
                         
                        // SizedBox(
                        //   height: 20,
                        // ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: SizedBox(
                            height: 40,
                            width: double.infinity,
                            child: ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  // minimumSize: MaterialStateProperty.all(
                                  //     Size(MediaQuery.of(context).size.width * 0.85, 45)),
                                  backgroundColor:
                                      MaterialStateProperty.all(PRIMARY_BUTTON_COLOR),
                                ),
                                onPressed: ()   {
         
                       sharePoster(ShareType.Any ,cardKey ,context);

                                //  profileURl == null
                                //       ? sendToSetting()
                                //       : await shareIdCards(cardKey, context);
                                },
                                child: profileURl == null
                                    ? Text("Upload Picture and Share")
                                    : RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(children: [
                                          WidgetSpan(
                                              alignment: PlaceholderAlignment.middle,
                                              child: Image.asset(
                                                'assets/icons/whatsapp.png',
                                                height: 25,
                                                width: 25,
                                                fit: BoxFit.fill,
                                              )),
                                          TextSpan(
                                            text: "    Share",
                                            style:  TextStyle(fontFamily: 'Gilroy-Bold',fontSize: 16, fontWeight: FontWeight.bold),
                                          )
                                        ]))),
                          ),
                        ),SizedBox(height: 10,)
                      ]),
                    ),
                  ],
                ),
              );
              // Positioned(
              //   right: 10,
              //   child: Icon(Icons.cancel_outlined ,))
            
          });
  });


   
 
}

  sharePoster(ShareType type ,GlobalKey card ,BuildContext context) async {
     //print("CLICKED");
    try {
      final boundary = card.currentContext?.findRenderObject();
      double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      if (boundary is RenderRepaintBoundary) {
        ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
        ByteData? byteData =
            await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List? pngBytes = byteData?.buffer.asUint8List();

        final directory =
            (await getTemporaryDirectory()).path; //from path_provide package
        final fileName = "poster.png";
        final path = '$directory/$fileName';

        await File(path).writeAsBytes(pngBytes!, flush: true);

        if (type == ShareType.Facebook) {
          await Share.shareXFiles([XFile(path)], text:"ID CARD");
        } else if (type == ShareType.Whatsapp) {

           String msg = "ID CARD \n Download your poster now $SHARE_URL";
      
      // Share to WhatsApp
      await FlutterSocialShare().shareToWhatsApp(
        msg: msg,
        imagePath: path,
      );
          // FlutterShareMe().shareToWhatsApp(
          //     msg:
          //         "ID CARD \n Download your poster now $SHARE_URL",
          //     imagePath: "$path");
        } else {
          await Share.shareXFiles([XFile(path)], text: " ID CARD");
        }
      }
    } catch (e) {
       //print("ERROR COMING INSIDE CATCH");
      // context.snackBar(e.toError());

    }
    //  //print("Sd");
      // ShareUtil.sharePoster(type, widget.model!, widget.profileURl);
  }
class FrostedGlassBox extends StatelessWidget {
  const FrostedGlassBox(
      {Key? key,
      required this.theWidth,
      required this.theHeight,
      required this.theChild})
      : super(key: key);
  final double theWidth;
  final double theHeight;
  final theChild;
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(30),
      child: Container(
        width: theWidth,
        height: theHeight,
        color: Colors.transparent,
        //we use Stack(); because we want the effects be on top of each other,
        //  just like layer in photoshop.
        child: Stack(
          children: [
            //blur effect ==> the third layer of stack
            BackdropFilter(
              filter: ImageFilter.blur(
                //sigmaX is the Horizontal blur
                sigmaX: 4.0,
                //sigmaY is the Vertical blur
                sigmaY: 4.0,
              ),
              //we use this container to scale up the blur effect to fit its
              //  parent, without this container the blur effect doesn't appear.
              child: Container(),
            ),
            //gradient effect ==> the second layer of stack
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white.withOpacity(0.13)),
                gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      //begin color  
                      Colors.white.withOpacity(0.15),
                      //end color
                      Colors.white.withOpacity(0.05),
                    ]),
              ),
            ),
            //child ==> the first/top layer of stack
            Center(child: theChild),
          ],
        ),
      ),
    );
  }
}