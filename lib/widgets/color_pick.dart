import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

class ColorPickerDialog extends StatefulWidget {
  final Color? startColor;
  const ColorPickerDialog({Key? key, this.startColor}) : super(key: key);

  @override
  _ColorPickerDialogState createState() => _ColorPickerDialogState();
}

class _ColorPickerDialogState extends State<ColorPickerDialog> {
  Color pickerColor = Color(0xff443a49);

  void changeColor(Color color) {
    setState(() => pickerColor = color);
  }

  @override
  void initState() {
    super.initState();
    final _stColor = widget.startColor;
    if (_stColor != null) pickerColor = _stColor;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Pick a color!'),
      content: SingleChildScrollView(
        child: ColorPicker(
          pickerColor: pickerColor,
          onColorChanged: changeColor,
          enableAlpha: false,
        ),
      ),
      actions: <Widget>[
        ElevatedButton(
          child: const Text('Select'),
          onPressed: () => Navigator.pop(context, pickerColor),
        ),
      ],
    );
  }
}
