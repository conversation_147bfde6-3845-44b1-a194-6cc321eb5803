import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ProfilePicRounded extends StatelessWidget {
  final String? url;
  final double size;
  const ProfilePicRounded({Key? key, required this.url, required this.size})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final profileUrl = url;

    return ClipOval(
      child: profileUrl != null && profileUrl.isNotEmpty
          ? CachedNetworkImage(
              imageUrl: profileUrl,
              fit: BoxFit.cover,
              width: size,
              height: size,
              memCacheWidth: size.toInt(),
              memCacheHeight: size.toInt(),
            )
          : Image.asset("assets/userImage.png",
              width: size, height: size, fit: BoxFit.cover),
    );
  }
}
