 import 'package:flutter/material.dart';

class CustomProgressBar extends StatelessWidget {
  final int totalNumber;
  final int progressNo;

  CustomProgressBar({required this.totalNumber, required this.progressNo});

  @override
  Widget build(BuildContext context) {
    double progressPercent = (progressNo / totalNumber) * 100;

    Color progressBarColor = Colors.grey;

    if (progressPercent < 30) {
      progressBarColor = Colors.red;
    } else if (progressPercent >= 30 && progressPercent < 60) {
      progressBarColor = Colors.orange;
    }
    // else{
    //   progressBarColor = Colors.green;
    // }

    return Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        children: [
          FractionallySizedBox(
            widthFactor: progressPercent / 100,
            child: Container(
              decoration: BoxDecoration(
                color: progressBarColor,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          Positioned.fill(
            child: Center(
              child: Text(
                '${progressPercent.toStringAsFixed(2)}%',
                style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}