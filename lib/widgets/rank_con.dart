import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
 

class RankCon extends StatelessWidget {
  String profileUrl ;
  Color colors ;
  String text ;
  String  name ;
  String point;
    RankCon({Key? key , required this.profileUrl ,required this.colors , required this.text , required this.name ,required this.point}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return    Column(
     
     
      children: [
        Stack(
          
                                                          children: [
                                                            Container(
                  width:  MediaQuery.of(context).size.width*0.14  ,
                  height:MediaQuery.of(context).size.width*0.14 ,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(width: 2, color: colors),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider( profileUrl  ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                
                //  Container(
                //   width:  MediaQuery.of(context).size.width*0.07 ,
                //   height:MediaQuery.of(context).size.width*0.07,
                //    decoration: BoxDecoration(
                    
                //     shape: BoxShape.rectangle,
                //     border: Border.all(width: 1, color: colors),
                     
                //   ),
                //   child:  Container(
                //     margin: EdgeInsets.all(1.5),
                //   width:  MediaQuery.of(context).size.width*0.07  ,
                //   height:MediaQuery.of(context).size.width*0.07,
                //    decoration: BoxDecoration(
                //     color: colors,
                //     shape: BoxShape.circle,
                //     border: Border.all(width: 0.02, color: colors),
                     
                //   ),
                //   child: Center(child: Text(text,style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black,fontWeight: FontWeight.bold,fontSize: 16),)),),
                 
                // ),
               

                                                          ],
                                                        ),
                                                        SizedBox(height:MediaQuery.of(context).size.width*0.015,),
                                                         Center(
                                                           child: SizedBox(
                                                            width:MediaQuery.of(context).size.width*0.28,
                                                             child: 
                                                             Text(
                                                                    name,
                                                                    maxLines: 1,
                                                                    textAlign: TextAlign.center,
                                                                    style:  TextStyle(fontFamily: 'Gilroy-Bold', fontSize: 18, color: Colors.black,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .bold),
                                                                  ),
                                                           ),
                                                         ) ,
                                                               Text(
                                                                'Points : ${point }',
                                                                style:  TextStyle(fontFamily: 'Gilroy',fontSize: 12, color: Colors.black,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
      ],
    ) ;
  }
}