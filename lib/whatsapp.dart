import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart';

// Data Models
class Role {
  final String value;
  final String label;
  final int userCount;

  Role({
    required this.value,
    required this.label,
    required this.userCount,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      value: json['value'],
      label: json['label'],
      userCount: json['userCount'],
    );
  }
}

class RolesResponse {
  final bool success;
  final List<Role> roles;
  final int totalUsers;
  final String message;

  RolesResponse({
    required this.success,
    required this.roles,
    required this.totalUsers,
    required this.message,
  });

  factory RolesResponse.fromJson(Map<String, dynamic> json) {
    return RolesResponse(
      success: json['success'],
      roles: (json['data']['roles'] as List)
          .map((role) => Role.fromJson(role))
          .toList(),
      totalUsers: json['data']['totalUsers'],
      message: json['message'],
    );
  }
}

class BulkMessageResult {
  final String userId;
  final String userName;
  final String userPhone;
  final String userRole;
  final String status;
  final String? messageId;
  final String? deliveredAt;
  final String? personalizedImageUrl;
  final String? error;
  final String? errorCode;

  BulkMessageResult({
    required this.userId,
    required this.userName,
    required this.userPhone,
    required this.userRole,
    required this.status,
    this.messageId,
    this.deliveredAt,
    this.personalizedImageUrl,
    this.error,
    this.errorCode,
  });

  factory BulkMessageResult.fromJson(Map<String, dynamic> json) {
    return BulkMessageResult(
      userId: json['userId'],
      userName: json['userName'],
      userPhone: json['userPhone'],
      userRole: json['userRole'],
      status: json['status'],
      messageId: json['messageId'],
      deliveredAt: json['deliveredAt'],
      personalizedImageUrl: json['personalizedImageUrl'],
      error: json['error'],
      errorCode: json['errorCode'],
    );
  }
}

class BulkMessageSummary {
  final int totalTargeted;
  final int successCount;
  final int failureCount;
  final int processingTimeMs;

  BulkMessageSummary({
    required this.totalTargeted,
    required this.successCount,
    required this.failureCount,
    required this.processingTimeMs,
  });

  factory BulkMessageSummary.fromJson(Map<String, dynamic> json) {
    return BulkMessageSummary(
      totalTargeted: json['totalTargeted'],
      successCount: json['successCount'],
      failureCount: json['failureCount'],
      processingTimeMs: json['processingTimeMs'],
    );
  }
}

class Template {
  final String id;
  final String name;
  final String status;
  final String language;
  final String category;
  final List<TemplateComponent> components;

  Template({
    required this.id,
    required this.name,
    required this.status,
    required this.language,
    required this.category,
    required this.components,
  });

  factory Template.fromJson(Map<String, dynamic> json) {
    return Template(
      id: json['id'],
      name: json['name'],
      status: json['status'],
      language: json['language'],
      category: json['category'],
      components: (json['components'] as List)
          .map((comp) => TemplateComponent.fromJson(comp))
          .toList(),
    );
  }
}

class TemplateComponent {
  final String type;
  final String? format;
  final String? text;

  TemplateComponent({
    required this.type,
    this.format,
    this.text,
  });

  factory TemplateComponent.fromJson(Map<String, dynamic> json) {
    return TemplateComponent(
      type: json['type'],
      format: json['format'],
      text: json['text'],
    );
  }
}

class TemplatesResponse {
  final bool success;
  final List<Template> templates;
  final String message;

  TemplatesResponse({
    required this.success,
    required this.templates,
    required this.message,
  });

  factory TemplatesResponse.fromJson(Map<String, dynamic> json) {
    return TemplatesResponse(
      success: json['success'],
      templates: (json['data']['templates'] as List)
          .map((template) => Template.fromJson(template))
          .toList(),
      message: json['message'],
    );
  }
}

class BulkMessageResponse {
  final bool success;
  final BulkMessageSummary summary;
  final List<BulkMessageResult> results;
  final List<String> targetedRoles;
  final String operationId;
  final String message;

  BulkMessageResponse({
    required this.success,
    required this.summary,
    required this.results,
    required this.targetedRoles,
    required this.operationId,
    required this.message,
  });

  factory BulkMessageResponse.fromJson(Map<String, dynamic> json) {
    return BulkMessageResponse(
      success: json['success'],
      summary: BulkMessageSummary.fromJson(json['data']['summary']),
      results: (json['data']['results'] as List)
          .map((result) => BulkMessageResult.fromJson(result))
          .toList(),
      targetedRoles: List<String>.from(json['data']['targetedRoles']),
      operationId: json['data']['operationId'],
      message: json['message'],
    );
  }
}

// Position Models
class PositionPreset {
  final String value;
  final String label;
  final String description;

  PositionPreset({
    required this.value,
    required this.label,
    required this.description,
  });

  factory PositionPreset.fromJson(Map<String, dynamic> json) {
    return PositionPreset(
      value: json['value'],
      label: json['label'],
      description: json['description'],
    );
  }
}

// Enhanced Admin Configuration Models
class AdminImageConfig {
  final bool useDefaultForMissingImages;
  final String? defaultImageUrl;
  final Map<String, String>? roleSpecificDefaults;
  final bool sendTemplateOnlyToUsersWithoutImages;
  final double userImageSize;
  final double userImageOpacity;
  final bool addBorder;
  final String? borderColor;

  AdminImageConfig({
    this.useDefaultForMissingImages = false,
    this.defaultImageUrl,
    this.roleSpecificDefaults,
    this.sendTemplateOnlyToUsersWithoutImages = true,
    this.userImageSize = 100.0,
    this.userImageOpacity = 1.0,
    this.addBorder = false,
    this.borderColor,
  });

  Map<String, dynamic> toJson() {
    return {
      'useDefaultForMissingImages': useDefaultForMissingImages,
      'defaultImageUrl': defaultImageUrl,
      'roleSpecificDefaults': roleSpecificDefaults,
      'sendTemplateOnlyToUsersWithoutImages': sendTemplateOnlyToUsersWithoutImages,
      'userImageSize': userImageSize,
      'userImageOpacity': userImageOpacity,
      'addBorder': addBorder,
      'borderColor': borderColor,
    };
  }
}

class PreviewUserSample {
  final String name;
  final String role;
  final String? imageUrl;

  PreviewUserSample({
    required this.name,
    required this.role,
    this.imageUrl,
  });
}

// Enhanced Text Configuration Models
class TextPosition {
  final String? preset;
  final int? margin;
  final CustomTextPosition? custom;

  TextPosition({
    this.preset,
    this.margin,
    this.custom,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    if (preset != null) {
      json['preset'] = preset;
      if (margin != null) json['margin'] = margin;
    }
    if (custom != null) {
      json['custom'] = custom!.toJson();
    }
    return json;
  }
}

class CustomTextPosition {
  final dynamic x;  // Can be int or String (for percentages)
  final dynamic y;  // Can be int or String (for percentages)
  final String? align; // 'start', 'middle', 'end'

  CustomTextPosition({
    required this.x,
    required this.y,
    this.align = 'middle',
  });

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'align': align,
    };
  }
}

class TextConfig {
  final bool enabled;
  final int fontSize;
  final String fontColor;
  final String fontFamily;
  final String fontWeight;
  final String? backgroundColor;
  final double? backgroundOpacity;
  final int? padding;
  final int? borderRadius;
  final TextPosition position;

  TextConfig({
    this.enabled = true,
    this.fontSize = 40,
    this.fontColor = 'white',
    this.fontFamily = 'Arial, sans-serif',
    this.fontWeight = 'bold',
    this.backgroundColor,
    this.backgroundOpacity = 0.7,
    this.padding = 10,
    this.borderRadius = 5,
    required this.position,
  });

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'fontSize': fontSize,
      'fontColor': fontColor,
      'fontFamily': fontFamily,
      'fontWeight': fontWeight,
      'backgroundColor': backgroundColor,
      'backgroundOpacity': backgroundOpacity,
      'padding': padding,
      'borderRadius': borderRadius,
      'position': position.toJson(),
    };
  }
}

// Text Options Response Models
class TextOptionsResponse {
  final bool success;
  final TextOptionsData data;
  final String message;

  TextOptionsResponse({
    required this.success,
    required this.data,
    required this.message,
  });

  factory TextOptionsResponse.fromJson(Map<String, dynamic> json) {
    return TextOptionsResponse(
      success: json['success'],
      data: TextOptionsData.fromJson(json['data']),
      message: json['message'],
    );
  }
}

class TextOptionsData {
  final List<TextPositionPreset> positions;
  final List<TextColor> colors;
  final List<FontSize> fontSizes;
  final List<FontFamily> fontFamilies;
  final List<TextFontWeight> fontWeights;
  final List<BackgroundColor> backgroundColors;
  final TextConfigDefault defaultConfig;

  TextOptionsData({
    required this.positions,
    required this.colors,
    required this.fontSizes,
    required this.fontFamilies,
    required this.fontWeights,
    required this.backgroundColors,
    required this.defaultConfig,
  });

  factory TextOptionsData.fromJson(Map<String, dynamic> json) {
    return TextOptionsData(
      positions: (json['positions'] as List)
          .map((item) => TextPositionPreset.fromJson(item))
          .toList(),
      colors: (json['colors'] as List)
          .map((item) => TextColor.fromJson(item))
          .toList(),
      fontSizes: (json['fontSizes'] as List)
          .map((item) => FontSize.fromJson(item))
          .toList(),
      fontFamilies: (json['fontFamilies'] as List)
          .map((item) => FontFamily.fromJson(item))
          .toList(),
      fontWeights: (json['fontWeights'] as List)
          .map((item) => TextFontWeight.fromJson(item))
          .toList(),
      backgroundColors: (json['backgroundColors'] as List)
          .map((item) => BackgroundColor.fromJson(item))
          .toList(),
      defaultConfig: TextConfigDefault.fromJson(json['defaultConfig']),
    );
  }
}

class TextPositionPreset {
  final String value;
  final String label;
  final String description;

  TextPositionPreset({
    required this.value,
    required this.label,
    required this.description,
  });

  factory TextPositionPreset.fromJson(Map<String, dynamic> json) {
    return TextPositionPreset(
      value: json['value'],
      label: json['label'],
      description: json['description'],
    );
  }
}

class TextColor {
  final String value;
  final String label;
  final String hex;
  final bool? preview;

  TextColor({
    required this.value,
    required this.label,
    required this.hex,
    this.preview,
  });

  factory TextColor.fromJson(Map<String, dynamic> json) {
    return TextColor(
      value: json['value'],
      label: json['label'],
      hex: json['hex'],
      preview: json['preview'],
    );
  }
}

class FontSize {
  final int value;
  final String label;

  FontSize({
    required this.value,
    required this.label,
  });

  factory FontSize.fromJson(Map<String, dynamic> json) {
    return FontSize(
      value: json['value'],
      label: json['label'],
    );
  }
}

class FontFamily {
  final String value;
  final String label;
  final String? category;

  FontFamily({
    required this.value,
    required this.label,
    this.category,
  });

  factory FontFamily.fromJson(Map<String, dynamic> json) {
    return FontFamily(
      value: json['value'],
      label: json['label'],
      category: json['category'],
    );
  }
}

class TextFontWeight {
  final String value;
  final String label;

  TextFontWeight({
    required this.value,
    required this.label,
  });

  factory TextFontWeight.fromJson(Map<String, dynamic> json) {
    return TextFontWeight(
      value: json['value'],
      label: json['label'],
    );
  }
}

class BackgroundColor {
  final String? value;
  final String label;
  final String? hex;
  final double? opacity;
  final String? description;

  BackgroundColor({
    this.value,
    required this.label,
    this.hex,
    this.opacity,
    this.description,
  });

  factory BackgroundColor.fromJson(Map<String, dynamic> json) {
    return BackgroundColor(
      value: json['value'],
      label: json['label'],
      hex: json['hex'],
      opacity: json['opacity']?.toDouble(),
      description: json['description'],
    );
  }
}

class TextConfigDefault {
  final bool enabled;
  final int fontSize;
  final String fontColor;
  final String fontFamily;
  final String fontWeight;
  final String? backgroundColor;
  final double backgroundOpacity;
  final int padding;
  final int borderRadius;
  final Map<String, dynamic> position;

  TextConfigDefault({
    required this.enabled,
    required this.fontSize,
    required this.fontColor,
    required this.fontFamily,
    required this.fontWeight,
    this.backgroundColor,
    required this.backgroundOpacity,
    required this.padding,
    required this.borderRadius,
    required this.position,
  });

  factory TextConfigDefault.fromJson(Map<String, dynamic> json) {
    return TextConfigDefault(
      enabled: json['enabled'],
      fontSize: json['fontSize'],
      fontColor: json['fontColor'],
      fontFamily: json['fontFamily'],
      fontWeight: json['fontWeight'],
      backgroundColor: json['backgroundColor'],
      backgroundOpacity: json['backgroundOpacity']?.toDouble() ?? 0.7,
      padding: json['padding'],
      borderRadius: json['borderRadius'],
      position: Map<String, dynamic>.from(json['position']),
    );
  }
}

class PreviewResponse {
  final String status;
  final String previewUrl;
  final Map<String, int> posterDimensions;
  final Map<String, int> userImageSize;
  final Map<String, dynamic> position;

  PreviewResponse({
    required this.status,
    required this.previewUrl,
    required this.posterDimensions,
    required this.userImageSize,
    required this.position,
  });

  factory PreviewResponse.fromJson(Map<String, dynamic> json) {
    return PreviewResponse(
      status: json['status'],
      previewUrl: json['previewUrl'],
      posterDimensions: Map<String, int>.from(json['posterDimensions']),
      userImageSize: Map<String, int>.from(json['userImageSize']),
      position: Map<String, dynamic>.from(json['position']),
    );
  }
}

class CustomPosition {
  final dynamic top;  // Can be int or String (for percentages)
  final dynamic left; // Can be int or String (for percentages)

  CustomPosition({
    required this.top,
    required this.left,
  });

  Map<String, dynamic> toJson() {
    return {
      'top': top,
      'left': left,
    };
  }
}

class UserImagePosition {
  final String? preset;
  final int? margin;
  final CustomPosition? custom;

  UserImagePosition({
    this.preset,
    this.margin,
    this.custom,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    if (preset != null) {
      json['preset'] = preset;
      if (margin != null) json['margin'] = margin;
    }
    if (custom != null) {
      json['custom'] = custom!.toJson();
    }
    return json;
  }
}

class PositionOptionsResponse {
  final bool success;
  final List<PositionPreset> presets;
  final int defaultMargin;
  final String message;

  PositionOptionsResponse({
    required this.success,
    required this.presets,
    required this.defaultMargin,
    required this.message,
  });

  factory PositionOptionsResponse.fromJson(Map<String, dynamic> json) {
    return PositionOptionsResponse(
      success: json['success'],
      presets: (json['data']['presets'] as List)
          .map((preset) => PositionPreset.fromJson(preset))
          .toList(),
      defaultMargin: json['data']['defaultMargin'],
      message: json['message'],
    );
  }
}

// API Service
class WhatsAppApiService {
  static String get baseUrl {
    if (Platform.isAndroid) {
      return 'https://wsup.netaapp.in'; // For Android emulator
    } else {
      return 'https://wsup.netaapp.in'; // Your current IP
    }
  }

  static Future<RolesResponse> getRoles() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/roles'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return RolesResponse.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load roles: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  static Future<TemplatesResponse> getTemplates() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/templates'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return TemplatesResponse.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load templates: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  static Future<PositionOptionsResponse> getPositionOptions() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/position-options'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return PositionOptionsResponse.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load position options: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  static Future<TextOptionsResponse> getTextOptions() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/text-options'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return TextOptionsResponse.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load text options: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  static Future<PreviewResponse> generatePreview({
    required String posterUrl,
    required String userImageUrl,
    required UserImagePosition position,
    double userImageSize = 100.0,
    TextConfig? textConfig,
  }) async {
    try {
      final Map<String, dynamic> requestBody = {
        'poster_url': posterUrl,
        'user_image_url': userImageUrl,
        'user_image_size': {
          'width': userImageSize.round(),
          'height': userImageSize.round(),
        },
      };

      // Convert position to backend format
      if (position.preset != null) {
        requestBody['position'] = {
          'preset': position.preset,
          'margin': position.margin ?? 20,
        };
      } else if (position.custom != null) {
        requestBody['position'] = {
          'top': position.custom!.top,
          'left': position.custom!.left,
        };
      }

      // Add text configuration
      if (textConfig != null) {
        requestBody['text_config'] = textConfig.toJson();
      }

      final response = await http.post(
        Uri.parse('$baseUrl/preview-image'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        return PreviewResponse.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to generate preview: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Preview error: $e');
    }
  }

  static Future<BulkMessageResponse> sendBulkMessage({
    required String targetRole,
    required String templateName,
    File? posterImage,
    bool includeUserImage = false,
    UserImagePosition? userImagePosition,
    AdminImageConfig? adminConfig,
    TextConfig? textConfig,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/api/send-bulk-message'),
      );

      // Add form fields
      request.fields['target_roles'] = json.encode([targetRole]);
      request.fields['template_name'] = templateName;
      request.fields['include_user_image'] = includeUserImage.toString();

      // Add positioning information
      if (userImagePosition != null) {
        request.fields['user_image_position'] = json.encode(userImagePosition.toJson());
      }

      // Add admin configuration
      if (adminConfig != null) {
        request.fields['admin_config'] = json.encode(adminConfig.toJson());
      }

      // Add text configuration
      if (textConfig != null) {
        request.fields['text_config'] = json.encode(textConfig.toJson());
      }

      // Add image file if provided
      if (posterImage != null) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'poster_image',
            posterImage.path,
          ),
        );
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        return BulkMessageResponse.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to send messages: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}

// Main Screen Widget
class WhatsAppBulkMessagingScreen extends StatefulWidget {
  @override
  _WhatsAppBulkMessagingScreenState createState() =>
      _WhatsAppBulkMessagingScreenState();
}

class _WhatsAppBulkMessagingScreenState
    extends State<WhatsAppBulkMessagingScreen> {
  final ImagePicker _picker = ImagePicker();

  List<Role> _availableRoles = [];
  List<Template> _availableTemplates = [];
  List<PositionPreset> _availablePositions = [];
  String? _selectedRole;
  String? _selectedTemplate;
  File? _selectedImage;
  bool _includeUserImage = false;
  bool _isLoading = false;
  bool _isLoadingRoles = false;
  bool _isLoadingTemplates = false;
  bool _isLoadingPositions = false;
  BulkMessageResponse? _lastResponse;

  // Enhanced position configuration with drag-and-drop
  String _selectedPosition = 'center-left'; // Default position
  double _userImageTop = 100.0;  // For custom positioning
  double _userImageLeft = 50.0;  // For custom positioning
  bool _isDragging = false;

  // Simplified position options (only 5 as requested)
  final List<Map<String, dynamic>> _positionOptions = [
    {'value': 'center-left', 'label': 'Center Left', 'icon': Icons.align_horizontal_left},
    {'value': 'center-right', 'label': 'Center Right', 'icon': Icons.align_horizontal_right},
    {'value': 'bottom-left', 'label': 'Bottom Left', 'icon': Icons.vertical_align_bottom},
    {'value': 'bottom-center', 'label': 'Bottom Center', 'icon': Icons.align_horizontal_center},
    {'value': 'top-center', 'label': 'Top Center', 'icon': Icons.vertical_align_top},
  ];

  // Admin configuration
  bool _useDefaultForMissingImages = false;
  String? _defaultImageUrl;
  bool _sendTemplateOnlyToUsersWithoutImages = true;
  double _userImageSize = 100.0;
  double _userImageOpacity = 1.0;
  bool _addBorder = false;
  Color _borderColor = Colors.white;
  bool _showPreview = false;
  bool _isGeneratingPreview = false;
  List<String> _generatedPreviews = [];

  // Text configuration state variables
  bool _enableText = true;
  bool _isLoadingTextOptions = false;
  List<TextPositionPreset> _availableTextPositions = [];
  List<TextColor> _availableTextColors = [];
  List<FontSize> _availableFontSizes = [];
  List<FontFamily> _availableFontFamilies = [];
  List<TextFontWeight> _availableFontWeights = [];
  List<BackgroundColor> _availableBackgroundColors = [];

  // Text positioning - simplified for better UX
  double _textX = 200.0;  // Center horizontally by default
  double _textY = 350.0;  // Near bottom by default
  String _textAlign = 'middle';

  // Text styling
  int _textFontSize = 40;
  String _textFontColor = 'white';
  String _textFontFamily = 'Arial, sans-serif';
  String _textFontWeight = 'bold';
  String? _textBackgroundColor;
  double _textBackgroundOpacity = 0.7;
  int _textPadding = 10;
  int _textBorderRadius = 5;

  // Predefined color palette for easy selection
  final List<Color> _colorPalette = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.orange,
    Colors.purple,
    Colors.pink,
    Colors.brown,
    Colors.grey,
    Colors.teal,
  ];

  // Preview samples
  final List<PreviewUserSample> _previewSamples = [
    PreviewUserSample(name: "John Doe", role: "Volunteer", imageUrl: null),
    PreviewUserSample(name: "Jane Smith", role: "Coordinator", imageUrl: "https://firebasestorage.googleapis.com/v0/b/neta-app-c80be.appspot.com/o/assets%2FuserImage.png?alt=media&token=4c61436d-3dd2-4f11-8b10-530b617dfb71"),
    PreviewUserSample(name: "Mike Johnson", role: "Leader", imageUrl: null),
  ];

  @override
  void initState() {
    super.initState();
    _loadRoles();
    _loadTemplates();
    _loadPositionOptions();
  }

  Future<void> _loadPositionOptions() async {
    setState(() {
      _isLoadingPositions = true;
    });

    try {
      final positionsResponse = await WhatsAppApiService.getPositionOptions();
      setState(() {
        _availablePositions = positionsResponse.presets;
        _isLoadingPositions = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPositions = false;
      });
      _showErrorDialog('Failed to load position options', e.toString());
    }
  }

  Future<void> _loadRoles() async {
    setState(() {
      _isLoadingRoles = true;
    });

    try {
      print("Fetching roles from: ${WhatsAppApiService.baseUrl}/api/roles");
      final rolesResponse = await WhatsAppApiService.getRoles();
      print("Roles response: ${rolesResponse.roles.length} roles found");

      setState(() {
        _availableRoles = rolesResponse.roles;
        _isLoadingRoles = false;
      });
    } catch (e) {
      print("Error loading roles: $e");
      setState(() {
        _isLoadingRoles = false;
      });
      _showErrorDialog('Failed to load roles', e.toString());
    }
  }

  Future<void> _loadTemplates() async {
    setState(() {
      _isLoadingTemplates = true;
    });

    try {
      final templatesResponse = await WhatsAppApiService.getTemplates();
      setState(() {
        _availableTemplates = templatesResponse.templates;
        _isLoadingTemplates = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTemplates = false;
      });
      _showErrorDialog('Failed to load templates', e.toString());
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      _showErrorDialog('Failed to pick image', e.toString());
    }
  }

  UserImagePosition _buildUserImagePosition() {
    // Use the new preset system
    return UserImagePosition(
      preset: _selectedPosition,
      margin: 20,
    );
  }

  AdminImageConfig _buildAdminConfig() {
    return AdminImageConfig(
      useDefaultForMissingImages: _useDefaultForMissingImages,
      defaultImageUrl: _defaultImageUrl,
      sendTemplateOnlyToUsersWithoutImages: _sendTemplateOnlyToUsersWithoutImages,
      userImageSize: _userImageSize,
      userImageOpacity: _userImageOpacity,
      addBorder: _addBorder,
      borderColor: '#${_borderColor.value.toRadixString(16).substring(2)}',
    );
  }

  TextConfig _buildTextConfig() {
    return TextConfig(
      enabled: _enableText,
      fontSize: _textFontSize,
      fontColor: _textFontColor,
      fontFamily: _textFontFamily,
      fontWeight: _textFontWeight,
      backgroundColor: _textBackgroundColor,
      backgroundOpacity: _textBackgroundOpacity,
      padding: _textPadding,
      borderRadius: _textBorderRadius,
      position: TextPosition(
        custom: CustomTextPosition(
          x: _textX.round(),
          y: _textY.round(),
          align: _textAlign,
        ),
      ),
    );
  }

  Future<void> _generateRealPreviews() async {
    if (_selectedImage == null) return;

    setState(() {
      _isGeneratingPreview = true;
      _generatedPreviews.clear();
    });

    try {
      // Upload poster image first (you might need to implement this)
      final posterUrl = await _uploadPosterForPreview();

      final position = _buildUserImagePosition();

      for (final sample in _previewSamples) {
        if (sample.imageUrl != null || _useDefaultForMissingImages) {
          try {
            final userImageUrl = sample.imageUrl ??
              (_useDefaultForMissingImages ? (_defaultImageUrl ?? 'https://firebasestorage.googleapis.com/v0/b/neta-app-c80be.appspot.com/o/assets%2FuserImage.png?alt=media&token=4c61436d-3dd2-4f11-8b10-530b617dfb71') : null);

            if (userImageUrl != null) {
              final preview = await WhatsAppApiService.generatePreview(
                posterUrl: posterUrl,
                userImageUrl: userImageUrl,
                position: position,
                userImageSize: _userImageSize,
              );

              _generatedPreviews.add(preview.previewUrl);
            } else {
              _generatedPreviews.add(''); // Empty for template-only
            }
          } catch (e) {
            print('Error generating preview for ${sample.name}: $e');
            _generatedPreviews.add(''); // Add empty on error
          }
        } else {
          _generatedPreviews.add(''); // Empty for template-only users
        }
      }
    } catch (e) {
      _showErrorDialog('Preview Error', 'Failed to generate previews: $e');
    } finally {
      setState(() {
        _isGeneratingPreview = false;
      });
    }
  }

  Future<String> _uploadPosterForPreview() async {
    // This is a simplified version - you might want to implement proper file upload
    // For now, we'll assume the poster is accessible via a URL
    // In a real implementation, you'd upload the file to your server and return the URL

    // Placeholder implementation - replace with actual upload logic
    throw UnimplementedError('Poster upload for preview not implemented yet');
  }

  Future<void> _sendBulkMessage() async {
    if (!_validateInputs()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      UserImagePosition? positionConfig;
      AdminImageConfig? adminConfig;
      TextConfig? textConfig;

      if (_includeUserImage) {
        positionConfig = _buildUserImagePosition();
        adminConfig = _buildAdminConfig();
      }

      // Always include text configuration if enabled
      if (_enableText) {
        textConfig = _buildTextConfig();
      }

      final response = await WhatsAppApiService.sendBulkMessage(
        targetRole: _selectedRole!,
        templateName: _selectedTemplate!,
        posterImage: _selectedImage,
        includeUserImage: _includeUserImage,
        userImagePosition: positionConfig,
        adminConfig: adminConfig,
        textConfig: textConfig,
      );

      setState(() {
        _lastResponse = response;
        _isLoading = false;
      });

      _showResultDialog(response);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('Failed to send messages', e.toString());
    }
  }

  bool _validateInputs() {
    if (_selectedRole == null) {
      _showErrorDialog('Validation Error', 'Please select a role');
      return false;
    }

    if (_selectedTemplate == null) {
      _showErrorDialog('Validation Error', 'Please select a template');
      return false;
    }

    return true;
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showResultDialog(BulkMessageResponse response) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(response.success ? 'Success' : 'Partial Success'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Targeted: ${response.summary.totalTargeted}'),
            Text('Successful: ${response.summary.successCount}'),
            Text('Failed: ${response.summary.failureCount}'),
            Text('Processing Time: ${response.summary.processingTimeMs}ms'),
            SizedBox(height: 10),
            Text('Operation ID: ${response.operationId}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showDetailedResults(response);
            },
            child: Text('View Details'),
          ),
        ],
      ),
    );
  }

  void _showDetailedResults(BulkMessageResponse response) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Detailed Results'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: response.results.length,
            itemBuilder: (context, index) {
              final result = response.results[index];
              return Card(
                child: ListTile(
                  title: Text(result.userName),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Phone: ${result.userPhone}'),
                      Text('Role: ${result.userRole}'),
                      Text('Status: ${result.status}'),
                      if (result.error != null) Text('Error: ${result.error}'),
                    ],
                  ),
                  leading: Icon(
                    result.status == 'success'
                        ? Icons.check_circle
                        : Icons.error,
                    color: result.status == 'success'
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleSelectionSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Target Role',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 10),
            if (_isLoadingRoles)
              Center(child: CircularProgressIndicator())
            else if (_availableRoles.isEmpty)
              Text('No roles available', style: TextStyle(color: Colors.red))
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Users: ${_availableRoles.fold(0, (sum, role) => sum + role.userCount)}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  SizedBox(height: 10),
                  DropdownButtonFormField<String>(
                    value: _selectedRole,
                    decoration: InputDecoration(
                      labelText: 'Select Role',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.group),
                    ),
                    items: _availableRoles.map((role) {
                      return DropdownMenuItem<String>(
                        value: role.value,
                        child: Text('${role.label} (${role.userCount} users)'),
                      );
                    }).toList(),
                    onChanged: (String? value) {
                      setState(() {
                        _selectedRole = value;
                      });
                    },
                  ),
                  if (_selectedRole != null) ...[
                    SizedBox(height: 10),
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Selected: ${_availableRoles.firstWhere((role) => role.value == _selectedRole).label} - ${_availableRoles.firstWhere((role) => role.value == _selectedRole).userCount} users',
                        style: TextStyle(color: Colors.green[700]),
                      ),
                    ),
                  ],
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateSelectionSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Template',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 10),
            if (_isLoadingTemplates)
              Center(child: CircularProgressIndicator())
            else
              DropdownButtonFormField<String>(
                value: _selectedTemplate,
                decoration: InputDecoration(
                  labelText: 'Select Template',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description_outlined),
                ),
                items: _availableTemplates.map((template) {
                  return DropdownMenuItem<String>(
                    value: template.name,
                    child: Text('${template.name} (${template.language})'),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedTemplate = value;
                  });
                },
              ),
            if (_selectedTemplate != null) ...[
              SizedBox(height: 10),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Selected: ${_selectedTemplate}',
                  style: TextStyle(color: Colors.blue[700]),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Poster Image',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 10),
            if (_selectedImage != null) ...[
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    _selectedImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImage,
                      icon: Icon(Icons.edit),
                      label: Text('Change Image'),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedImage = null;
                        });
                      },
                      icon: Icon(Icons.delete),
                      label: Text('Remove'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                height: 150,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey, style: BorderStyle.solid),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: InkWell(
                  onTap: _pickImage,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_photo_alternate, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('Tap to select poster image', style: TextStyle(color: Colors.grey)),
                      Text('(Optional - PNG/JPG, max 5MB)', style: TextStyle(color: Colors.grey, fontSize: 12)),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPositioningSection() {
    if (!_includeUserImage) return SizedBox.shrink();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Choose Image Position',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),

            // User-friendly explanation
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.touch_app, color: Colors.green[700], size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Select where you want user photos to appear on your poster',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 20),

            // Simplified position options (only 5 as requested)
            Text(
              'Position Options',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12),

            // Large, intuitive position buttons
            Column(
              children: _positionOptions.map((option) {
                final isSelected = _selectedPosition == option['value'];
                return Container(
                  margin: EdgeInsets.only(bottom: 12),
                  child: Material(
                    elevation: isSelected ? 4 : 1,
                    borderRadius: BorderRadius.circular(12),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () {
                        setState(() {
                          _selectedPosition = option['value'];
                          _updatePositionFromPreset(option['value']);
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected ? Colors.blue[600]! : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                          color: isSelected ? Colors.blue[50] : Colors.white,
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.blue[600] : Colors.grey[400],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                option['icon'],
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                option['label'],
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                                  color: isSelected ? Colors.blue[700] : Colors.black87,
                                ),
                              ),
                            ),
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: Colors.blue[600],
                                size: 24,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),

            SizedBox(height: 16),

            // Current selection indicator
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline, color: Colors.blue[700], size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Selected: ${_positionOptions.firstWhere((opt) => opt['value'] == _selectedPosition)['label']}',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Update position based on preset selection
  void _updatePositionFromPreset(String preset) {
    switch (preset) {
      case 'center-left':
        _userImageTop = 150.0;
        _userImageLeft = 20.0;
        break;
      case 'center-right':
        _userImageTop = 150.0;
        _userImageLeft = 280.0;
        break;
      case 'bottom-left':
        _userImageTop = 280.0;
        _userImageLeft = 20.0;
        break;
      case 'bottom-center':
        _userImageTop = 280.0;
        _userImageLeft = 150.0;
        break;
      case 'top-center':
        _userImageTop = 20.0;
        _userImageLeft = 150.0;
        break;
      default:
        _userImageTop = 150.0;
        _userImageLeft = 20.0;
    }
  }

  Widget _buildOptionsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Options',
              style: TextStyle(fontSize: 18),
            ),
            SwitchListTile(
              title: Text('Include User Images'),
              subtitle: Text('Overlay user profile images on poster'),
              value: _includeUserImage,
              onChanged: (bool value) {
                setState(() {
                  _includeUserImage = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextConfigSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Text Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),

            // Enable text toggle
            SwitchListTile(
              title: Text('Add User Names'),
              subtitle: Text('Show user names on the poster'),
              value: _enableText,
              onChanged: (bool value) {
                setState(() {
                  _enableText = value;
                });
              },
            ),

            if (_enableText) ...[
              SizedBox(height: 15),

              // Text color selection with visual palette
              Text(
                'Text Color',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 10),

              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  _buildColorOption('white', Colors.white, 'White'),
                  _buildColorOption('black', Colors.black, 'Black'),
                  _buildColorOption('red', Colors.red, 'Red'),
                  _buildColorOption('blue', Colors.blue, 'Blue'),
                  _buildColorOption('green', Colors.green, 'Green'),
                  _buildColorOption('orange', Colors.orange, 'Orange'),
                  _buildColorOption('purple', Colors.purple, 'Purple'),
                  _buildColorOption('gold', Colors.amber, 'Gold'),
                ],
              ),

              SizedBox(height: 20),

              // Font size slider
              Text(
                'Text Size: ${_textFontSize}px',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              Slider(
                value: _textFontSize.toDouble(),
                min: 20,
                max: 72,
                divisions: 13,
                label: '${_textFontSize}px',
                onChanged: (double value) {
                  setState(() {
                    _textFontSize = value.round();
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildColorOption(String colorValue, Color color, String label) {
    final isSelected = _textFontColor == colorValue;
    return GestureDetector(
      onTap: () {
        setState(() {
          _textFontColor = colorValue;
        });
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.blue[600]! : Colors.grey[300]!,
                width: isSelected ? 3 : 1,
              ),
              boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    )
                  ]
                : null,
            ),
            child: isSelected
              ? Icon(
                  Icons.check,
                  color: color == Colors.white || color == Colors.amber ? Colors.black : Colors.white,
                  size: 24,
                )
              : null,
          ),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? Colors.blue[700] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminConfigSection() {
    if (!_includeUserImage) return SizedBox.shrink();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Image Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),

            // User handling explanation
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[700], size: 20),
                      SizedBox(width: 8),
                      Text(
                        'User Image Handling',
                        style: TextStyle(
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Users WITH images: Will receive poster with their profile image overlay',
                    style: TextStyle(color: Colors.blue[700]),
                  ),
                  Text(
                    '• Users WITHOUT images: Will receive the template message as-is (no image overlay)',
                    style: TextStyle(color: Colors.blue[700]),
                  ),
                ],
              ),
            ),

            SizedBox(height: 15),

            // Default image handling
            SwitchListTile(
              title: Text('Use Default Image for Users Without Profile'),
              subtitle: Text('Add a default image overlay for users without profile pictures'),
              value: _useDefaultForMissingImages,
              onChanged: (bool value) {
                setState(() {
                  _useDefaultForMissingImages = value;
                });
              },
            ),

            if (_useDefaultForMissingImages) ...[
              SizedBox(height: 10),
              TextFormField(
                initialValue: _defaultImageUrl ?? '',
                decoration: InputDecoration(
                  labelText: 'Default Image URL',
                  border: OutlineInputBorder(),
                  hintText: 'Enter URL for default user image',
                  prefixIcon: Icon(Icons.image),
                  helperText: 'This image will be overlaid for users without profile pictures',
                ),
                onChanged: (String value) {
                  setState(() {
                    _defaultImageUrl = value.isEmpty ? null : value;
                  });
                },
              ),
            ],

            SizedBox(height: 15),

            // Image customization
            Text(
              'Image Customization',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 10),

            // User image size
            Text('User Image Size: ${_userImageSize.round()}px'),
            Slider(
              value: _userImageSize,
              min: 50,
              max: 200,
              divisions: 15,
              label: '${_userImageSize.round()}px',
              onChanged: (double value) {
                setState(() {
                  _userImageSize = value;
                });
              },
            ),

            SizedBox(height: 10),

            // Image opacity
            Text('Image Opacity: ${(_userImageOpacity * 100).round()}%'),
            Slider(
              value: _userImageOpacity,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              label: '${(_userImageOpacity * 100).round()}%',
              onChanged: (double value) {
                setState(() {
                  _userImageOpacity = value;
                });
              },
            ),

            SizedBox(height: 10),

            // Border options
            SwitchListTile(
              title: Text('Add Border'),
              subtitle: Text('Add border around user images'),
              value: _addBorder,
              onChanged: (bool value) {
                setState(() {
                  _addBorder = value;
                });
              },
            ),

            if (_addBorder) ...[
              SizedBox(height: 10),
              Text(
                'Border Color',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _colorPalette.map((color) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _borderColor = color;
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _borderColor == color ? Colors.black : Colors.grey[300]!,
                          width: _borderColor == color ? 3 : 1,
                        ),
                      ),
                      child: _borderColor == color
                        ? Icon(Icons.check, color: color == Colors.white ? Colors.black : Colors.white, size: 20)
                        : null,
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    if (!_includeUserImage || _selectedImage == null) return SizedBox.shrink();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Preview',
                  style: TextStyle(fontSize: 18),
                ),
                Spacer(),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _showPreview = !_showPreview;
                        });
                      },
                      icon: Icon(_showPreview ? Icons.visibility_off : Icons.visibility),
                      label: Text(_showPreview ? 'Hide Preview' : 'Show Preview'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                      ),
                    ),
                    SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _isGeneratingPreview ? null : _generateRealPreviews,
                      icon: _isGeneratingPreview
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                          )
                        : Icon(Icons.auto_awesome),
                      label: Text(_isGeneratingPreview ? 'Generating...' : 'Real Preview'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            if (_showPreview) ...[
              SizedBox(height: 15),
              Text(
                'Preview with Sample Users',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 10),

              Container(
                height: 300,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _previewSamples.length,
                  itemBuilder: (context, index) {
                    final sample = _previewSamples[index];
                    return Container(
                      width: 250,
                      margin: EdgeInsets.only(right: 16),
                      child: Column(
                        children: [
                          Text(
                            '${sample.name} (${sample.role})',
                            style: TextStyle(),
                          ),
                          Text(
                            sample.imageUrl != null ? 'Has Profile Image' : 'No Profile Image',
                            style: TextStyle(
                              fontSize: 12,
                              color: sample.imageUrl != null ? Colors.green[600] : Colors.orange[600],
                            ),
                          ),
                          SizedBox(height: 8),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Stack(
                                children: [
                                  // Poster image
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.file(
                                      _selectedImage!,
                                      width: double.infinity,
                                      height: double.infinity,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  // User image overlay (simulated) - only show if user has image OR default is enabled
                                  if (sample.imageUrl != null || _useDefaultForMissingImages)
                                    _buildUserImageOverlay(sample),
                                  // Show "Template Only" indicator for users without images when no default is set
                                  if (sample.imageUrl == null && !_useDefaultForMissingImages)
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: Container(
                                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: Colors.orange[600],
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          'Template Only',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              SizedBox(height: 15),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.orange[700], size: 20),
                        SizedBox(width: 8),
                        Text(
                          'Preview Information',
                          style: TextStyle(
                            color: Colors.orange[700],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• This is a simulated preview showing how user images will be positioned',
                      style: TextStyle(color: Colors.orange[700]),
                    ),
                    Text(
                      '• Users without images will ${_useDefaultForMissingImages ? 'show default image overlay' : 'receive template as-is (no overlay)'}',
                      style: TextStyle(color: Colors.orange[700]),
                    ),
                    Text(
                      '• Final images will be processed on the server with exact positioning',
                      style: TextStyle(color: Colors.orange[700]),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUserImageOverlay(PreviewUserSample sample) {
    // Calculate position based on selected preset
    double top, left;
    switch (_selectedPosition) {
      case 'center-left':
        top = 125.0; // Center vertically in 250px container
        left = 10.0;
        break;
      case 'center-right':
        top = 125.0;
        left = 180.0; // Adjust for image size
        break;
      case 'bottom-left':
        top = 200.0;
        left = 10.0;
        break;
      case 'bottom-center':
        top = 200.0;
        left = 100.0; // Center horizontally
        break;
      case 'top-center':
        top = 10.0;
        left = 100.0;
        break;
      default:
        top = 125.0;
        left = 10.0;
    }

    return Positioned(
      top: top,
      left: left,
      child: GestureDetector(
        onPanUpdate: (details) {
          // Add drag functionality for fine-tuning
          setState(() {
            _isDragging = true;
            // Update position based on drag (for preview only)
            // This doesn't change the preset, just shows visual feedback
          });
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
          });
        },
        child: Container(
          width: _userImageSize * 0.8, // Smaller for preview
          height: _userImageSize * 0.8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: _addBorder
              ? Border.all(
                  color: _borderColor,
                  width: 2,
                )
              : null,
            boxShadow: _isDragging
              ? [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  )
                ]
              : null,
          ),
          child: Opacity(
            opacity: _userImageOpacity,
            child: ClipOval(
              child: sample.imageUrl != null
                ? Image.network(
                    sample.imageUrl!,
                    width: _userImageSize * 0.8,
                    height: _userImageSize * 0.8,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildDefaultUserImage();
                    },
                  )
                : _buildDefaultUserImage(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultUserImage() {
    return Container(
      width: _userImageSize,
      height: _userImageSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[300],
      ),
      child: Icon(
        Icons.person,
        size: _userImageSize * 0.6,
        color: Colors.grey[600],
      ),
    );
  }

  Widget _buildSendButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _sendBulkMessage,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 16),
        textStyle: TextStyle(fontSize: 18),
      ),
      child: _isLoading
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 10),
                Text('Sending Messages...'),
              ],
            )
          : Text('Send Bulk Messages'),
    );
  }

  Widget _buildLastResultSection() {
    final response = _lastResponse!;
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Last Operation Result',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total',
                    response.summary.totalTargeted.toString(),
                    Colors.blue,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'Success',
                    response.summary.successCount.toString(),
                    Colors.green,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'Failed',
                    response.summary.failureCount.toString(),
                    Colors.red,
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Text('Operation ID: ${response.operationId}'),
            Text('Processing Time: ${response.summary.processingTimeMs}ms'),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: () => _showDetailedResults(response),
              child: Text('View Detailed Results'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('WhatsApp Bulk Messaging'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: _isLoadingRoles
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildRoleSelectionSection(),
                  SizedBox(height: 20),
                  _buildTemplateSelectionSection(),
                  SizedBox(height: 20),
                  _buildImageSection(),
                  SizedBox(height: 20),
                  _buildOptionsSection(),
                  SizedBox(height: 20),
                  _buildPositioningSection(),
                  SizedBox(height: 20),
                  _buildAdminConfigSection(),
                  SizedBox(height: 20),
                  _buildTextConfigSection(),
                  SizedBox(height: 20),
                  _buildPreviewSection(),
                  SizedBox(height: 30),
                  _buildSendButton(),
                  if (_lastResponse != null) ...[
                    SizedBox(height: 20),
                    _buildLastResultSection(),
                  ],
                ],
              ),
            ),
    );
  }

}
