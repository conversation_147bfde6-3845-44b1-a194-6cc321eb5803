// import 'dart:io';

// import 'package:mla_connect/models/election.dart';
// import 'package:intl/intl.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
// import 'package: //printing/ //printing.dart';

// class VoterSlipUtil {
//   VoterSlipUtil._();

//   static Future<File> _getPdfFile( ) async {
//     final appDocDir = await getApplicationDocumentsDirectory();
//     final fileName = "testing" + "_slip.pdf";
//     return File(appDocDir.path + "/" + fileName);
//   }

//   static Future<File> generate(Map<String ,dynamic> voterdetails ) async {
//     DateTime now = DateTime.now();
//     final  //printedDate = DateFormat('dd-MM-yyyy HH:mm:ss').format(now);

//     final colorWhite = PdfColor.fromHex("#FFFFFF");
//     final colorDarkGray = PdfColor.fromHex("#6C6D6D");
//     final colorGray = PdfColor.fromHex("#EFEFEF");
//     final borderColor = PdfColor.fromHex("#C1C1C1");

//     final boldFont = await PdfGoogleFonts.karmaBold();
//     final normalFont = await PdfGoogleFonts.karmaRegular();

//     final titleStyle =
//         pw.TextStyle(color: colorWhite, font: boldFont, fontSize: 18);

//     final commonPadding = const pw.EdgeInsets.all(8);

//     final borderSide =
//         ({double width = 2}) => pw.BorderSide(color: borderColor, width: width);

//     pw.Widget rowTextBox(
//         {required String text,
//         PdfColor? bgColor,
//         bool rightBorder = true,
//         pw.Font? font}) {
//       pw.Font fontPdf = font ?? normalFont;
//       final color = bgColor ?? colorGray;

//       final dataStyle = pw.TextStyle(font: fontPdf, fontSize: 12);

//       return pw.Expanded(
//           child: pw.Container(
//               alignment: pw.Alignment.centerLeft,
//               decoration: pw.BoxDecoration(
//                   color: color,
//                   border: pw.Border(
//                       bottom: borderSide(width: 1),
//                       right: rightBorder ? borderSide() : pw.BorderSide.none)),
//               padding: commonPadding,
//               child: pw.Text(text, style: dataStyle)));
//     }

//     pw.Widget getRow({required List<pw.Widget> children}) {
//       return pw.Container(
//           decoration: pw.BoxDecoration(
//               color: colorWhite,
//               border: pw.Border(bottom: borderSide(width: 1))),
//           child: pw.Row(children: children));
//     }

//     String boothName = "-";
//     final part_name = voterdetails["name"];
//     if (part_name != null) {
//       if (part_name.eng.isNotEmpty)
//         boothName = part_name.eng;
//       else if (part_name.lkl.isNotEmpty) boothName = part_name.lkl;
//     }

//     final page = pw.Page(
//         pageFormat: PdfPageFormat.a4,
//         build: (pw.Context context) {
//           return pw.Center(
//               child: pw.Container(
//             decoration: pw.BoxDecoration(
//                 border: pw.Border(left: borderSide(), right: borderSide())),
//             child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
//               pw.Row(children: [
//                 pw.Expanded(
//                     child: pw.Container(
//                         padding: commonPadding,
//                         color: colorDarkGray,
//                         alignment: pw.Alignment.center,
//                         child: pw.Text("मतदाता सूचना/Voter Information",
//                             textAlign: pw.TextAlign.center, style: titleStyle)))
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "राज्य/State"),
//                 rowTextBox(
//                     text: "${"profile.election_info?.state" ?? "-"}",
//                     rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "Assembly Constituency", bgColor: colorWhite),
//                 rowTextBox(
//                     text: "${"profile.election_info?.assembly_name" ?? "-"}",
//                     rightBorder: false,
//                     bgColor: colorWhite)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "Name"),
//                 rowTextBox(
//                     text: "${voterdetails['name']} ${voterdetails["lastname"]}",
//                     rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "नाम", bgColor: colorWhite),
//                 rowTextBox(
//                     text: "${voterdetails['name']} ${voterdetails["lastname"]}",
//                     rightBorder: false,
//                     bgColor: colorWhite)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "लिंग/Gender"),
//                 rowTextBox(text: voterdetails['gender'], rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(
//                     text: "पहचान पत्र क्रमांक/EPIC No", bgColor: colorWhite),
//                 rowTextBox(
//                     text:voterdetails['epicNumber'],
//                     rightBorder: false,
//                     bgColor: colorWhite)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "Relative's Name"),
//                 rowTextBox(
//                     text:
//                      voterdetails['relativeName'],
//                     rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "रिश्तेदार का नाम", bgColor: colorWhite),
//                 rowTextBox(
//                     text:
//                         "${voterdetails['name']} ${voterdetails["lastname"]}",
//                     rightBorder: false,
//                     bgColor: colorWhite)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "भाग संख्या/Part Number"),
//                 rowTextBox(text: "${"profile.part_no" ?? ""}", rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "भाग का नाम/Part Name", bgColor: colorWhite),
//                 rowTextBox(
//                     text: boothName, rightBorder: false, bgColor: colorWhite)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "मतदाता क्रमांक/Serial No"),
//                 rowTextBox(
//                     text: "${"profile.sl_no_part" ?? ""}", rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(
//                     text: "मतदान केंद्र/Polling Station", bgColor: colorWhite),
//                 rowTextBox(
//                     text: boothName, rightBorder: false, bgColor: colorWhite)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(text: "मतदान की तारीख/Polling Date"),
//                 rowTextBox(
//                     text: "${"profile.election_info?.voting_date" ?? "-"}",
//                     font: boldFont,
//                     rightBorder: false)
//               ]),
//               pw.Row(children: [
//                 rowTextBox(
//                     text: "मुद्रित तिथि/ //printed On", bgColor: colorWhite),
//                 rowTextBox(
//                     text:  //printedDate, rightBorder: false, bgColor: colorWhite)
//               ]),
//               pw.Container(
//                   alignment: pw.Alignment.centerLeft,
//                   decoration: pw.BoxDecoration(
//                       color: colorGray,
//                       border: pw.Border(bottom: borderSide(width: 1))),
//                   padding: commonPadding,
//                   child: pw.Text(
//                       "Note 1 : This output is computer generated and is provided only for the information to the voter.",
//                       style: pw.TextStyle(font: normalFont, fontSize: 9))),
//               pw.Container(
//                   alignment: pw.Alignment.centerLeft,
//                   decoration: pw.BoxDecoration(
//                       color: colorWhite,
//                       border: pw.Border(bottom: borderSide(width: 1))),
//                   padding: commonPadding,
//                   child: pw.Text("Note 2 : This is not an identity document.",
//                       style: pw.TextStyle(font: normalFont, fontSize: 9)))
//             ]),
//           ));
//         });

//     final pdf = pw.Document();
//     pdf.addPage(page);
//     //write to file
//     final saveLocation = await _getPdfFile( );
//     await saveLocation.writeAsBytes(await pdf.save());
//     return saveLocation;
//   }
// }

// class Future {
// }
