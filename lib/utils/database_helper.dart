import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:mla_connect/models/RoomList.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static final _databaseName = "roomList.db";
  static final _databaseVersion = 1;

  static final table = 'roomList';

  static final columnId = '_id';
  static final columnRoomId = 'roomId';
  static final columnRoomData = 'roomData';

  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    return await openDatabase(path,
        version: _databaseVersion, onCreate: _onCreate);
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
          CREATE TABLE $table (
            $columnRoomId TEXT PRIMARY KEY,
            $columnRoomData TEXT NOT NULL
          )
          ''');
  }

  Future<int> insert(RoomList roomList) async {
    Database db = await instance.database;
    var row = {
      columnRoomId: roomList.roomId,
      columnRoomData: jsonEncode(roomList.toJson())
    };
    return await db.insert(table, row);
  }

  Future<List<RoomList>> queryAllRows() async {
    Database db = await instance.database;
    List<Map<String, dynamic>> result = await db.query(table);
     //print(result);
    return result
        .map((e) => RoomList.fromJson(
            jsonDecode(e[columnRoomData]) as Map<String, dynamic>))
        .toList();
  }

  // Function to update a row
  Future<int> update(RoomList roomList) async {
    RoomList? _roomList = await queryById(roomList.roomId);
    if (_roomList != null &&
        _roomList.lastMessageTimestamp > roomList.lastMessageTimestamp) {
       //print("INISIDE IFF");
      return Future.value(0);
    }
     //print("Updating from DB");
    Database db = await instance.database;
    var row = {
      columnRoomId: roomList.roomId,
      columnRoomData: jsonEncode(roomList.toJson())
    };
    return await db.update(table, row,
        where: '$columnRoomId = ?', whereArgs: [roomList.roomId]);
  }

  // Function to update all rows
  Future<void> updateAllRows(List<RoomList> roomListData) async {
     //print("ROOMLIST");

    Database db = await instance.database;
    Batch batch = db.batch();

    for (var roomList in roomListData) {
      // Check if the row with the given roomId already exists
      int? count = Sqflite.firstIntValue(await db.rawQuery(
          'SELECT COUNT(*) FROM $table WHERE $columnRoomId = ?',
          [roomList.roomId]));

      if (count == 0) {
        // Insert a new row if it doesn't exist
        batch.insert(
          table,
          {
            columnRoomId: roomList.roomId,
            columnRoomData: jsonEncode(roomList.toJson()),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        // Update the existing row
        batch.update(
          table,
          {columnRoomData: jsonEncode(roomList.toJson())},
          where: '$columnRoomId = ?',
          whereArgs: [roomList.roomId],
        );
      }
    }

    await batch.commit(noResult: true);
  }

  Future<void> clearTable() async {
    Database db = await instance.database;
    await db.rawDelete('DELETE FROM $table');
  }

  Future<RoomList?> queryById(String roomId) async {
    Database db = await instance.database;
    List<Map<String, dynamic>> result =
        await db.query(table, where: '$columnRoomId = ?', whereArgs: [roomId]);
    if (result.isNotEmpty) {
      return RoomList.fromJson(
          jsonDecode(result.first[columnRoomData]) as Map<String, dynamic>);
    }
    return null;
  }

// Update a specific data by ID
  Future<int> updateRoomById(RoomList roomList) async {
     //print("updateRoomById");
    Database db = await instance.database;
    return await db.update(
      table,
      {columnRoomData: jsonEncode(roomList.toJson())},
      where: '$columnRoomId = ?',
      whereArgs: [roomList.roomId],
    );
  }
}
