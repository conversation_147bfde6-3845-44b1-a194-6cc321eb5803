import 'dart:collection';

import 'package:intl/intl.dart';

class TimeUtil {
  String formatedCreateTime(timeStamp) {
    try {
      final inputFormat = DateFormat('dd-MM-yyyy HH:mm');
      return inputFormat.format(DateTime.fromMillisecondsSinceEpoch(timeStamp));
    } catch (e) {
      return "";
    }
  }

  String formatTimestampDDMMM(String timestampString) {
    int timestamp = int.parse(timestampString);
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    String formattedDate = DateFormat('d MMM').format(date);
    String ordinalDate = addSuffix(int.parse(formattedDate.split(' ')[0])) +
        ' ' +
        formattedDate.split(' ')[1];
    return ordinalDate;
  }

  String addSuffix(int number) {
    int lastDigit = number % 10;
    int lastTwoDigits = number % 100;

    if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {
      return '${number}th';
    }

    switch (lastDigit) {
      case 1:
        return '${number}st';
      case 2:
        return '${number}nd';
      case 3:
        return '${number}rd';
      default:
        return '${number}th';
    }
  }

  Map<int, int> processDays(Map<int, bool> greetedDays) {
    SplayTreeMap<int, bool> sortedGreetedDays =
        SplayTreeMap<int, bool>.from(greetedDays);
    List<int> keys = sortedGreetedDays.keys.toList();

    Map<int, int> resultMap = {};

    // Find today's date
    int today = keys.last;
    resultMap[today] = sortedGreetedDays[today]! ? 1 : 0;

    // Remove today from keys
    keys.removeLast();

    // Process remaining days based on the existing conditions
    // Ensure to only add days until the length of resultMap is less than 5

    // If there's only 1 day left
    if (keys.length == 1 && resultMap.length < 5) {
      resultMap[keys.first] = sortedGreetedDays[keys.first]! ? 1 : -1;
    }

    // If there are 2 consecutive greeted days
    else if (keys.length > 1 &&
        sortedGreetedDays[keys[keys.length - 1]]! &&
        sortedGreetedDays[keys[keys.length - 2]]!) {
      if (resultMap.length < 5) resultMap[keys[keys.length - 1]] = 1;
      if (resultMap.length < 5) resultMap[keys[keys.length - 2]] = 1;
    }

    // If there are 2 non-consecutive greeted days with one day gap
    else if (keys.length > 2 &&
        sortedGreetedDays[keys[keys.length - 1]]! &&
        !sortedGreetedDays[keys[keys.length - 2]]! &&
        sortedGreetedDays[keys[keys.length - 3]]!) {
      if (resultMap.length < 5) resultMap[keys[keys.length - 1]] = 1;
      if (resultMap.length < 5) resultMap[keys[keys.length - 3]] = 1;
    }

    // If there are 3 or more consecutive greeted days
    else if (keys.length >= 3 &&
        sortedGreetedDays[keys[keys.length - 1]]! &&
        sortedGreetedDays[keys[keys.length - 2]]! &&
        sortedGreetedDays[keys[keys.length - 3]]!) {
      if (resultMap.length < 5) resultMap[keys[keys.length - 1]] = 1;
      if (resultMap.length < 5) resultMap[keys[keys.length - 2]] = 1;
      if (resultMap.length < 5) resultMap[keys[keys.length - 3]] = 1;
    }

    // For any other case, consider only the latest day
    else {
      if (keys.isNotEmpty && resultMap.length < 5) {
        resultMap[keys[keys.length - 1]] =
            sortedGreetedDays[keys[keys.length - 1]]! ? 1 : -1;
      }
    }

    // Fill in remaining days until the length of resultMap is 5
    int nextDayTimestamp =
        today + (24 * 60 * 60 * 1000); // Add 1 day in milliseconds
    while (resultMap.length < 5) {
      resultMap[nextDayTimestamp] = -1;
      nextDayTimestamp += 24 * 60 * 60 * 1000;
    }

    // Debug missing cases
    if (resultMap.length != 5) {
       //print(
            // 'Unexpected error: there are ${resultMap.length} days in the resultMap, but there should be exactly 5 days');
    }

    return resultMap;
  }

  Map<String, int> getGreetTimeline(Map<String, bool> resultData) {
    Map<String, int> dates = {};
     //print("DB DATES");
     //print(resultData);
    getParsedDate(element) {
      int timestamp = int.parse(element);
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      String formattedDate = DateFormat('d MMM').format(date);
      String ordinalDate =
          TimeUtil().addSuffix(int.parse(formattedDate.split(' ')[0])) +
              ' ' +
              formattedDate.split(' ')[1];
      return ordinalDate;
      // dates[ordinalDate] = element.value ? 1 : 0;
    }

    var lastDate = resultData.entries.toList().reversed;
    Map<int, int> filledDates = {};

    // for (int i = 0; i < 5; i++) {
    //   DateTime now = DateTime.now();
    //   DateTime todayMidnightLocal = DateTime(now.year, now.month, now.day);
    //   DateTime todayMidnightInDesiredZone =
    //       todayMidnightLocal.toUtc().add(Duration(hours: 5, minutes: 30));
    //   int timestamp = todayMidnightInDesiredZone.millisecondsSinceEpoch;
    //   filledDates[(timestamp - ((i - 2) * 24 * 60 * 60 * 1000))] = false;
    // }

    for (int i = 0; i < 5; i++) {
      DateTime now = DateTime.now();
      DateTime todayMidnightLocal = DateTime(now.year, now.month, now.day);
      DateTime todayMidnightInDesiredZone =
          todayMidnightLocal.toUtc().add(Duration(hours: 5, minutes: 30));
      int timestamp = todayMidnightInDesiredZone.millisecondsSinceEpoch;

      // Calculate the desired timestamp
      timestamp = timestamp - ((i - 1) * 24 * 60 * 60 * 1000);

      // Assign the correct value based on whether the date is in the past, present or future
      if (i < 3) {
        filledDates[timestamp] = 0;
      } else {
        filledDates[timestamp] = -1;
      }
    }

     //print(filledDates);
     //print(resultData);

    resultData.entries.forEach((entry) {
      filledDates.entries.forEach((fe) {
        if (entry.key.toString() == fe.key.toString()) {
          filledDates[fe.key] = entry.value ? 1 : 0;
        }
      });
    });
    //-----
     //print("Added Consecutive dates");
     //print(filledDates);

    // var value = processDays(filledDates);
     //print("value is");
    //  //print(value);
    Map<String, int> newMap = filledDates
        .map((key, value) => MapEntry(getParsedDate(key.toString()), value));

    return newMap;
  }

  String getTodayOrdinalDate() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('d MMM').format(now);
    String ordinalDate =
        TimeUtil().addSuffix(int.parse(formattedDate.split(' ')[0])) +
            ' ' +
            formattedDate.split(' ')[1];
    return ordinalDate;
  }
}
