import 'dart:convert';

import 'package:flutter_social_share_plugin/flutter_social_share.dart';
import 'package:mla_connect/controller/reward_controller.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/PosterV2.dart';
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/screens/poster/poster_util.dart';
 
    
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
 
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:social_share_plugin/social_share_plugin.dart';

class ShareUtil {
  static void sharePost(BuildContext context, Share type, Post post) async {
    String text = "";
    text = post.text ?? "" + " \n $SHARE_URL";

    final url = post.mediaurl;
    String? path;
    try {
      if ((post.postType == 'image' || post.postType == 'video') &&
          url != null) {
        final cm = DefaultCacheManager();
        final cf = await cm.getFileFromCache(url);
        if (cf?.file.path != null) {
          path = cf?.file.path;
        } else {
          final nf = await cm.downloadFile(url);
          path = nf.file.path;
        }
      }
    } catch (e) {}

//REWARDING SYSTEM
    RewardController? _rewardController = Get.isRegistered<RewardController>()
        ? Get.find<RewardController>()
        : Get.put(RewardController());
    var response;
    if (type == 'whatsapp')
      await FlutterSocialShare()
          .shareToWhatsApp(msg: text, imagePath: path ?? '')
          .then((value) async => response =
              await FirebaseCloud.instance.createReward("POST_SHARE", post.id));
    else if (type == "facebook") {
      // await SocialSharePlugin.shareToFeedFacebook(path: path!).then
      await FlutterSocialShare().shareToFacebook(msg: text, url: url ?? '').then(
          (value) async => response =
              await FirebaseCloud.instance.createReward("POST_SHARE", post.id));
    } else {
      if (path != null) {
        await Share.shareXFiles([XFile(path)], text: text);
      } else {
        await FlutterSocialShare().shareToSystem(msg: text);
      }

      response =
          await FirebaseCloud.instance.createReward("POST_SHARE", post.id);
    }
    _rewardController!.setRewardPoint(response['userPoints']);
  }

  static void sharePoster(
      Share type, PosterNetworkModelV2 model, SelfProfile profile) async {
    String text = model.caption + " \n $SHARE_URL";

    var profileUrl = profile.profileUrl!;
    var profileName = profile.name;

    // final preview = await PosterUtils.overlapPosterFromModel(
    //     model, profileUrl, profileName);

    // try {
    //   if (type == ShareType.Whatsapp)
    //     FlutterShareMe().shareToWhatsApp(
    //         imagePath: preview!, fileType: FileType.image, msg: text);
    //   else if (type == ShareType.Facebook) {
    //     await Share.shareXFiles([XFile(preview!)], text: text);
    //   } else {
    //     await Share.shareXFiles([XFile(preview!)], text: text);
    //   }
    // } catch (e) {
    // }

    // String? path;
    // try {
    //   final cm = DefaultCacheManager();
    //   final cf = await cm.getFileFromCache(url);
    //   if (cf?.file.path != null) {
    //     path = cf?.file.path;
    //   } else {
    //     final nf = await cm.downloadFile(url);
    //     path = nf.file.path;
    //   }
    // } catch (e) {}
    // if (type == ShareType.Whatsapp)
    //   await FlutterShareMe().shareToWhatsApp(msg: text, imagePath: path ?? '');
    // else if (type == ShareType.Facebook) {
    //   await FlutterShareMe().shareToFacebook(msg: text, url: url);
    // } else {
    //   await Share.shareXFiles([XFile(path!)], text: text);
    // }
  }
}
