import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ImageUtil {
  ImageUtil._();

  static Future<XFile?> pickImageFromFile() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? image =
        await _picker.pickImage(source: ImageSource.gallery, imageQuality: 60);
    return image;
  }

  static Future<XFile?> pickVideoFromFile() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
    return video;
  }

  static Future<bool> checkImageSize(String filePath, context) async {
    File imageFile = File(filePath);

    // Check the file size (in bytes)
    int fileSizeInBytes = await imageFile.length();
    int fileSizeInMB = fileSizeInBytes ~/ (1024 * 1024);
     //print("IMAGE SIZE IS");
     //print(fileSizeInMB);
     //print("I AM HERE BABY");

    if (fileSizeInMB > 30) {
       //print("NOW I AM INSIDE");
      // Show an error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              "The selected video is larger than 30MB. Please choose a smaller video or compress it."),
          duration: Duration(seconds: 3),
        ),
      );
      return false;
    }
    return true;
  }

  static Future<String?> selectImageAndCrop() async {
    String? imagePath;

    try {
      final file = await ImageUtil.pickImageFromFile();
      final path = file?.path;

      if (path == null) throw Exception("Image path not found");
      imagePath = path;
      CroppedFile? croppedFile = await ImageCropper().cropImage(
        sourcePath: imagePath,
        // aspectRatio: CropAspectRatio(ratioX: 4, ratioY: 3),
        uiSettings: [
          AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              toolbarColor: Colors.white,
              toolbarWidgetColor: Colors.black,
              initAspectRatio: CropAspectRatioPreset.ratio4x3,
              lockAspectRatio: false),
          IOSUiSettings(
            title: 'Crop Image',
          ),
        ],
      );
      if (croppedFile != null) {
         //print("file name is ");
         //print(croppedFile.path);
        return croppedFile.path;
      }
    } catch (e) {
       //print(e);
      return null;
    }
    return null;
  }
}
