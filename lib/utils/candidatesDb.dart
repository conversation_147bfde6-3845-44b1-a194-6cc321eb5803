import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:mla_connect/models/Canditate.dart';
import 'package:mla_connect/models/RoomList.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class CandidateDB {
  static final _databaseName = "candidatesList.db";
  static final _databaseVersion = 1;

  static final table = 'candidateList';

  static final columnId = '_id';
  static final columnCandidateId = 'epic_no';
  static final columnCandidateData = 'candidateData';

  CandidateDB._privateConstructor();
  static final CandidateDB instance = CandidateDB._privateConstructor();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    return await openDatabase(path,
        version: _databaseVersion, onCreate: _onCreate);
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
          CREATE TABLE $table (
            $columnCandidateId TEXT PRIMARY KEY,
            $columnCandidateData TEXT NOT NULL
          )
          ''');
  }

  insert(List<Candidate> candidate) async {
     //print("OPERATION DB GO");
    Database db = await instance.database;

    for (var i = 0; i < candidate.length; i += 50) {
      var end = (i + 50 < candidate.length) ? i + 50 : candidate.length;
      var chunk = candidate.sublist(i, end);

      var batch = db.batch();

      for (var candidate in chunk) {
        batch.insert(
          table,
          {
            columnCandidateId: candidate.epic_no,
            columnCandidateData: jsonEncode(candidate.toJson())
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit();
    }
     //print("OPERATION DB END");

    return true;
  }

  Future<double> getRowCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
    var count = result.first['count'];
     //print('Number of rows: $count');
    return double.parse(count.toString());
  }

  // Assume `database` is your reference to the database.
  Future<String?> getLastRow() async {
    final db = await database;
    final List<Map<String, dynamic>> maps =
        await db.rawQuery('SELECT * FROM $table ORDER BY ROWID DESC LIMIT 1');

    if (maps.length > 0) {
       //print("LASTTT ROWW MILGATYA OUEEE");

       //print(
          //  jsonDecode(maps.first[columnCandidateData]) as Map<String, dynamic>);

      var ccd = (Candidate.fromJson(
          jsonDecode(maps.first[columnCandidateData]) as Map<String, dynamic>));
      // Return the last row.

      return Candidate.fromJson(jsonDecode(maps.first[columnCandidateData])
              as Map<String, dynamic>)
          .epic_no;
    } else {
       //print("LASTTT ROWW MILGATYA OUEEE");
       //print("QUACHHH NOIII");
      // If no rows exist, return null or handle this case as you see fit.
      return null;
    }
  }

  Future<List<Candidate>> searchByName(String name) async {
    final db = await database;
    // Query the table for rows where the name column contains the provided name.
    final List<Map<String, dynamic>> rows = await db.rawQuery(
      "SELECT * FROM $table WHERE $columnCandidateData LIKE ?",
      ['%$name%'],
    );
    return rows
        .map((e) => Candidate.fromJson(
            jsonDecode(e[columnCandidateData]) as Map<String, dynamic>))
        .toList();
  }

  Future<List<Candidate>> loadFirst500Rows() async {
    final db = await database;
    final List<Map<String, dynamic>> rows = await db.query(
      table,
      limit: 500,
    );

    return rows
        .map((e) => Candidate.fromJson(
            jsonDecode(e[columnCandidateData]) as Map<String, dynamic>))
        .toList();
  }

  // Future<List<RoomList>> queryAllRows() async {
  //   Database db = await instance.database;
  //   List<Map<String, dynamic>> result = await db.query(table);
  //    //print(result);
  //   return result
  //       .map((e) => RoomList.fromJson(
  //           jsonDecode(e[columnRoomData]) as Map<String, dynamic>))
  //       .toList();
  // }

  // Function to update a row
  // Future<int> update(RoomList roomList) async {
  //   RoomList? _roomList = await queryById(roomList.roomId);
  //   if (_roomList != null &&
  //       _roomList.lastMessageTimestamp > roomList.lastMessageTimestamp) {
  //      //print("INISIDE IFF");
  //     return Future.value(0);
  //   }
  //    //print("Updating from DB");
  //   Database db = await instance.database;
  //   var row = {
  //     columnRoomId: roomList.roomId,
  //     columnRoomData: jsonEncode(roomList.toJson())
  //   };
  //   return await db.update(table, row,
  //       where: '$columnRoomId = ?', whereArgs: [roomList.roomId]);
  // }

  // Function to update all rows
  // Future<void> updateAllRows(List<RoomList> roomListData) async {
  //    //print("ROOMLIST");

  //   Database db = await instance.database;
  //   Batch batch = db.batch();

  //   for (var roomList in roomListData) {
  //     // Check if the row with the given roomId already exists
  //     int? count = Sqflite.firstIntValue(await db.rawQuery(
  //         'SELECT COUNT(*) FROM $table WHERE $columnRoomId = ?',
  //         [roomList.roomId]));

  //     if (count == 0) {
  //       // Insert a new row if it doesn't exist
  //       batch.insert(
  //         table,
  //         {
  //           columnRoomId: roomList.roomId,
  //           columnRoomData: jsonEncode(roomList.toJson()),
  //         },
  //         conflictAlgorithm: ConflictAlgorithm.replace,
  //       );
  //     } else {
  //       // Update the existing row
  //       batch.update(
  //         table,
  //         {columnRoomData: jsonEncode(roomList.toJson())},
  //         where: '$columnRoomId = ?',
  //         whereArgs: [roomList.roomId],
  //       );
  //     }
  //   }

  //   await batch.commit(noResult: true);
  // }

  Future<void> clearTable() async {
    Database db = await instance.database;
    await db.rawDelete('DELETE FROM $table');
  }

  Future<Candidate?> queryById(String epicNo) async {
    Database db = await instance.database;
    List<Map<String, dynamic>> result = await db
        .query(table, where: '$columnCandidateId = ?', whereArgs: [epicNo]);
    if (result.isNotEmpty) {
      return Candidate.fromJson(jsonDecode(result.first[columnCandidateData])
          as Map<String, dynamic>);
    }
    return null;
  }

// Update a specific data by ID
  Future<int> updateCandidateById(Candidate candidate) async {
     //print("updateRoomById");
    Database db = await instance.database;
    return await db.update(
      table,
      {columnCandidateData: jsonEncode(candidate.toJson())},
      where: '$columnCandidateId = ?',
      whereArgs: [candidate.epic_no],
    );
  }
}
