import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mla_connect/controller/namecontroller.dart';
import 'package:mla_connect/controller/service/login.dart';
 
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/new_ui/home/<USER>';
import 'package:mla_connect/screens/profile/profile_page.dart';
import 'package:mla_connect/utils/imagePickerUtil.dart';
import 'package:mla_connect/widgets/session_expired.dart';
 
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast.dart';
import 'package:sembast/sembast_io.dart';
 

import 'package:share_plus/share_plus.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../widgets/custom_textfield.dart';
 

class OnBoarding extends StatefulWidget {
  const OnBoarding({Key? key}) : super(key: key);

  @override
  State<OnBoarding> createState() => _OnBoardingState();
}

class _OnBoardingState extends State<OnBoarding> {
  TextEditingController nameController = new TextEditingController();
  TextEditingController numnerController = new TextEditingController();
  NameController nameCon = Get.put(NameController());
  LoginService loginService = LoginService();
  XFile? profileFile;
  String? profileUrl;

  bool isLoading = false;

  SelfProfile? profile;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
     //print("Welcome to ONBOARDING PAGE");
    // Future.microtask(() => getDashBoardData());
  }

  // bool pageLoading = true;
  // getDashBoardData() async {
  //   try {
  //     DashboardResponse dp = await FirebaseCloud.instance.getDashboardData();
  //     profile = dp.profile;
  //     setState(() {
  //       pageLoading = false;
  //       if (profile!.name != null || profile!.name != '') {
  //         nameController = TextEditingController(text: profile!.name);
  //       }
  //       if (profile!.profileUrl != null) {
  //         profileUrl = profile!.profileUrl;
  //       }
  //     });
  //   } catch (e) {}
  // }

  final _formKey = GlobalKey<FormState>();
  void startPicSelect() async {
    try {
      final XFile? image = await ImageUtil.pickImageFromFile();
      if (image != null)
        setState(() {
          this.profileFile = image;
        });

       //print(profileFile);
       //print(image);
       //print(image.runtimeType);
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Widget pic() {
    return Container(
      // padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Align(
        alignment: Alignment.center,
        child: Container(
          child: ClipOval(
            child: profileFile != null
                ? Image.file(
                    File(profileFile!.path),
                    height: 100,
                    width: 100,
                    fit: BoxFit.cover,
                  )
                : (profileUrl != null
                    ? CachedNetworkImage(
                        imageUrl: profileUrl!,
                        height: 100,
                        width: 100,
                        fit: BoxFit.cover)
                    : Image.asset("assets/userImage.png",
                        height: 100, width: 100, fit: BoxFit.contain)),
          ),
        ),
      ),
    );
  }
String _selectedGender = "MALE";
 DateTime _selectedDate =  DateTime.now();
  

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      
      context: context,
      initialDate:DateTime.now() ,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      initialDatePickerMode: DatePickerMode.day,
      builder: (BuildContext context, Widget? child) {
      return Theme(
        data: ThemeData.light().copyWith(
          primaryColor: Color(0xff4C3AB4), // Header background color
          // Selected date color
          colorScheme: ColorScheme.light(primary: Color(0xff4C3AB4)),
          buttonTheme: ButtonThemeData(
            textTheme: ButtonTextTheme.primary,
          ),
        ),
        child: child!,
      );
    },
  );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      
    }
  }
final TextEditingController dateTextController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xff4C3AB4),
        title: Text(
          "User Details",
          style:  TextStyle(fontFamily: 'Gilroy',
            color: Colors.white,
            fontSize: 24
          ),
        ),
        centerTitle: true,
      ),
      body:
          // pageLoading
          //     ? Center(child: CircularProgressIndicator())
          //     :
          SingleChildScrollView(
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height  ,
         
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: MediaQuery.of(context).size.height*0.06,),
                 pic(),
                 SizedBox(height: MediaQuery.of(context).size.height*0.02,),
                SizedBox(
                  height: MediaQuery.of(context).size.height*0.06,
                   width: MediaQuery.of(context).size.width*0.5,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
    backgroundColor: Color(0xff4C3AB4), // Change the color here
  ),
                      onPressed: startPicSelect, child: Text("Upload Image" ,style:  TextStyle( fontFamily: 'Gilroy-Bold', fontWeight: FontWeight.bold, color: Colors.white, ),)),
                ),
              SizedBox(height: MediaQuery.of(context).size.height*0.03,), CustomTextFormField(hint: 'e.g. Amit Kumar' ,label: 'ENTER NAME',         validator: (text) {
                        if (text != null && text.length < 2) {
                          return "Enter Valid Name";
                        }
                      }, controller: nameController , ) ,
                // Padding(
                //   padding: const EdgeInsets.symmetric(
                //     horizontal: 35.0,
                //     vertical: 30,
                //   ),
                //   child: TextFormField(
                //       validator: (text) {
                //         if (text != null && text.length < 2) {
                //           return "Enter Valid Name";
                //         }
                //       },
                //       controller: nameController,
                //       decoration: InputDecoration(
                //         border: OutlineInputBorder(
                //           borderSide: const BorderSide(
                //             color: Colors.black54,
                //             width: 2.0,
                //           ),
                //           borderRadius: BorderRadius.circular(25.0),
                //         ),
                //         hintText: "Name",
                //         prefixIcon: Icon(
                //           Icons.person,
                //         ),
                //       )),
                // ),
                SizedBox(height: MediaQuery.of(context).size.height*0.01,),
                 CustomTextFormField(hint: 'e.g. 8264849728' ,label: 'ENTER MOBILE NO.',        validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a mobile no.';
                        }
                        if (value.length != 10) {
      return 'Enter a 10 digit number';
    }
                        // Validate if the input is a valid number using a regular expression
                        if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                          return 'Please enter a valid number.';
                        }
                        return null;
                      }, controller: numnerController , ) ,
    //             Padding(
    //               padding: const EdgeInsets.symmetric(
    //                 horizontal: 35.0,
    //               ),
    //               child: TextFormField(
    //                   validator: (value) {
    //                     if (value == null || value.isEmpty) {
    //                       return 'Please enter a number.';
    //                     }
    //                     if (value.length != 10) {
    //   return 'Enter a 10 digit number';
    // }
    //                     // Validate if the input is a valid number using a regular expression
    //                     if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
    //                       return 'Please enter a valid number.';
    //                     }
    //                     return null;
    //                   },
    //                   controller: numnerController,
    //                   keyboardType: TextInputType.number,
    //                   decoration: InputDecoration(
    //                     border: OutlineInputBorder(
    //                       borderSide: const BorderSide(
    //                         color: Colors.black54,
    //                         width: 2.0,
    //                       ),
    //                       borderRadius: BorderRadius.circular(25.0),
    //                     ),
    //                     hintText: "Number",
    //                     prefixIcon: Icon(
    //                       Icons.person,
    //                     ),
    //                   )),
    //             ),
                SizedBox(height: MediaQuery.of(context).size.height*0.01,),
                Stack(
                  children: [
                    GestureDetector(
                      onTap:   () => _selectDate(context),
                      child:  
                          Column(
                            children: [
                              SizedBox(height:10,),
                              Container(
                                 margin: EdgeInsets.only(left: 20 ,right: 20),
                              width: MediaQuery.of(context).size.width,
                              padding: EdgeInsets.all(15),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                    border: Border.all(
                                    color: Color(0xFFE8E8E8), // Border color
                                    width: 1.0, // Border width
                                    ),
                                    borderRadius: BorderRadius.circular(5), // Optional: border radius
                                  ),
                                child: 
                                Row(children: [
                                //  Icon(
                                //       Icons.date_range,
                                //       color: Colors.grey,
                                //     ), 
                                //     SizedBox(width: 5,),
               Text(
                                           ' ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                  style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold ,fontSize: 16),
                                        ),
                                        Spacer(),
                                           SvgPicture.asset('assets/icons/dropdown.svg')
                                     
                                        // ElevatedButton(
                                        //   onPressed: () => _selectDate(context),
                                        //   child: Text('Select Date'),
                                        // ),
                                ],),
                              ),
                            ],
                          ),  ),
                     Positioned(
                        left: 25,
                        top: 5,
                        child: Container(
                         padding: EdgeInsets.only(left: 5 ,right: 5),
                          color: Colors.white,
                     child: Text("DATE OF BIRTH" , style:  TextStyle(fontFamily: 'Gilroy-SemiBold',fontWeight: FontWeight.bold , fontSize: 14  ),),
                        ),
                      )
                  ],
                ),
               
              SizedBox(height: MediaQuery.of(context).size.height*0.01,),  
              Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 20 ,right: 20 , top: 20),
                    width: MediaQuery.of(context).size.width,
                    alignment: Alignment.center,
                    
                    decoration: BoxDecoration(
        border: Border.all(
          color: Color(0xFFE8E8E8),// Border color
        width: 1.0, // Border width
        ),
        borderRadius: BorderRadius.circular(5), // Optional: border radius
        ),
                    child: Row(
                      children: [
                        SizedBox(width: 10,),
                        SizedBox(
                          width: MediaQuery.of(context).size.width*0.81,
                          child: DropdownButton<String>(
                              value: _selectedGender,
                              isExpanded: true,
                              underline: SizedBox(),
                              
                            hint: Text('Select Gender'),
                            iconDisabledColor: Colors.white,
                            icon: SvgPicture.asset('assets/icons/dropdown.svg') ,
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedGender = newValue!;
                              });

                               //print(_selectedGender);
                            },
                            items: <String>['MALE', 'FEMALE', 'OTHER']
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                          
                      ],
                    ),
                  ),
                   Positioned(
                        left: 25,
                        top: 13,
                        child: Container(
                         padding: EdgeInsets.only(left: 5 ,right: 5),
                          color: Colors.white,
                     child: Text("GENDER" , style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold , fontSize: 14  ),),
                        ),
                      )
                ],
              ),
                SizedBox(
                  height: 30.0,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20.0,
                    
                  ),
                  child: ElevatedButton(
                    style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all<Color>(
                   Color(0xff4C3AB4),
                    )),
                    onPressed:
                        
                        () async {
                           
 String formattedDate = DateFormat('yyyy-MM-dd').format(_selectedDate);
                       //print("sumb");
                      if (profileUrl == null && profileFile == null) {
                        context.snackBar("Profile picture is requierd");
                        return;
                      }
                       DateTime checkDate = DateTime.now() ;
                    String   formattedDates = DateFormat('yyyy-MM-dd').format(checkDate);
                      //  //print(  formattedDates == formattedDate);
                      //  //print(formattedDates);
                      //  //print(formattedDate);
                      //  //print(_selectedDate.day == DateTime.now());
                        if ((  formattedDates == formattedDate ) ) {
                        context.snackBar("Select valid DOB");
                        return;
                      }

                      if (_formKey.currentState!.validate()) {
                        setState(() {
                          isLoading = true;
                          nameCon.name = nameController.text;
                        });
                        
                        //  //print(" //print profile");
                        //  //print(profileFile!.path);
                        setState(() {
                          isLoading = true;
                        });
                        // String nameValue = nameController.text
                        //     .replaceAll(RegExp(r"[^\w]"), "");
                        String numberValue =
                            numnerController.text.replaceAll(RegExp(r"\s"), "");

                        var result = await loginService.makeRegistrationRequest(
                            context,
                            nameController.text,
                            numberValue,
                            profileFile!.path,
                            formattedDate , _selectedGender 
                             );
                              //print(result);
                        if (result["status"] == 200) {
                          
                          // context.pushAndRemoveWidget(
                          //     (context) => MyBottomBar(), (route) => false);
                       Navigator.of(context , rootNavigator: true )
                          .pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (BuildContext context) {
                            return MyBottomBar();
                          },
                        ),
                        (_) => false,
                      );

                        }else if (result["status"] == 403){
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                        } else {
                          context.snackBar("Something went wrong!");
                        }
                      }
                      setState(() {
                        isLoading = false;
                      });
                    },
                    child: Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(vertical: 15),
                      child: !isLoading
                          ? Text(
                              "SUBMIT",
                              style:  TextStyle(fontFamily: 'Gilroy-Bold',
                                fontSize: 18.0,
                                fontWeight: FontWeight.bold,
                                color: Colors.white
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.all(1),
                              child: CircularProgressIndicator(),
                            ),
                    ),
                  ),
                ),
                // if (isLoading) CircularProgressIndicator()
              ],
            ),
          ),
        ),
      ),
    );
  }

  void nameDb() async {
    //sembat database code
    try {
      var dbFactory = databaseFactoryIo;

      Directory appDocDir = await getApplicationDocumentsDirectory();
      String databasePath = appDocDir.path + '/database.db';
      var dbPath = databasePath;
      Database db = await dbFactory.openDatabase(dbPath);

      var store = StoreRef.main();
      await store.record('user_name').put(db, nameController.text);

      await db.close();
       //print("sucessfully stroeed $store");
    } catch (e) {
       //print("error :  $e");
    }
  }
}

class EditUSerProfile extends StatefulWidget {
  const EditUSerProfile({Key? key}) : super(key: key);

  @override
  State<EditUSerProfile> createState() => _EditUSerProfileState();
}

class _EditUSerProfileState extends State<EditUSerProfile> {
  TextEditingController nameController = new TextEditingController();
  TextEditingController numnerController = new TextEditingController();
  
  LoginService loginService = LoginService();
  XFile? profileFile;
  String? profileUrl;

  bool isLoading = false;

  SelfProfile? profile;
  String _selectedGender = "MALE";
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
     //print("Welcome to ONBOARDING PAGE");
    fetchData() ;
     dateTextController.text = _selectedDate.toString() ?? "";
    // Future.microtask(() => getDashBoardData());
  }
   var profileImage ;
  var name ;
   var number ;
   var dob;
 fetchData()async{
   final SharedPreferences prefs = await SharedPreferences.getInstance();
   dob= await prefs.getString('dob' );
   profileImage=  await prefs.getString('profileUrl' );
   name =   await prefs.getString('Name');
     number =   await prefs.getString('number' );
      //print(name + "edit name");
   setState(() {
     
   });
 }
  final _formKey = GlobalKey<FormState>();
  void startPicSelect() async {
    try {
      final XFile? image = await ImageUtil.pickImageFromFile();
      if (image != null)
        setState(() {
          this.profileFile = image;
        });

     
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Widget pic() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Align(
        alignment: Alignment.center,
        child: Container(
          child: ClipOval(
            child:
            // profileImage==null ?
              profileFile != null
            ?
            Image.file(
                    File(profileFile!.path),
                    height: 100,
                    width: 100,
                    fit: BoxFit.cover,
                  )
                : Image.asset("assets/userImage.png",
                        height: 100, width: 100, fit: BoxFit.contain)
                    // CachedNetworkImage(
                    //     imageUrl: profileImage!,
                    //     height: 140,
                    //     width: 140,
                    //     fit: BoxFit.cover)
                    
          ),
        ),
      ),
    );
  }

    DateTime _selectedDate =  DateTime.now();

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:DateTime.now() ,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      initialDatePickerMode: DatePickerMode.day,
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      
    }
  }
final TextEditingController dateTextController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    String dateText = _selectedDate?.toString() ?? "";
      
    return Scaffold(
      appBar: AppBar( backgroundColor: Color(0xff4C3AB4),
        leading: IconButton(onPressed:  () {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return ProfilePage() ;
          },)) ;
        }, icon: Icon(Icons.arrow_back_ios ,color: Colors.white,)),
        // backgroundColor: Colors.yellow,
        // title: Text(
        //   "User Details",
        //   style:  TextStyle(fontFamily: 'Gilroy',
        //     color: Colors.white,
        //   ),
        // ),
        // centerTitle: true,
      ),
      body:
          // pageLoading
          //     ? Center(child: CircularProgressIndicator())
          //     :
          SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
               SizedBox(height: MediaQuery.of(context).size.height*0.02,),
               pic(),
                SizedBox(height: MediaQuery.of(context).size.height*0.02,),
                SizedBox(
                  height: MediaQuery.of(context).size.height*0.06,
                   width: MediaQuery.of(context).size.width*0.5,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
    backgroundColor: Color(0xff4C3AB4), // Change the color here
  ),
                      onPressed: startPicSelect, child: Text("Upload Image" ,style:  TextStyle(fontFamily: 'Gilroy-Bold' , color: Colors.white
                      ),)),
                ),
                 SizedBox(height: MediaQuery.of(context).size.height*0.01,),
              // Pa
              // dding(
              //   padding: const EdgeInsets.symmetric(horizontal: 20 ,vertical: 30),
              //   child: ElevatedButton(
              //     onPressed: startPicSelect, child: Text("Upload Image")),
              // ),
              CustomTextFormField(hint: 'e.g. Amit Kumar' ,label: 'ENTER NAME',         validator: (text) {
                        if (text != null && text.length < 2) {
                          return "Enter Valid Name";
                        }
                      }, controller: nameController , ) ,
                        
              // Padding(
              //   padding: const EdgeInsets.symmetric(
              //     horizontal: 35.0,
              //     vertical: 30,
              //   ),
              //   child: TextFormField(
              //       validator: (text) {
              //         if (text != null && text.length < 2) {
              //           return "Enter Valid Name";
              //         }
              //       },
              //       controller: nameController,
              //       decoration: InputDecoration(
              //         border: OutlineInputBorder(
              //           borderSide: const BorderSide(
              //             color: Colors.black54,
              //             width: 2.0,
              //           ),
              //           borderRadius: BorderRadius.circular(25.0),
              //         ),
              //         hintText: "Name",
              //         prefixIcon: Icon(
              //           Icons.person,
              //         ),
              //       )),
              // ),
              CustomTextFormField(hint: 'e.g. 8264849728' ,label: 'ENTER NUMBER',        validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a number.';
                        }
                        if (value.length != 10) {
      return 'Enter a 10 digit number';
    }
                        // Validate if the input is a valid number using a regular expression
                        if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                          return 'Please enter a valid number.';
                        }
                        return null;
                      }, controller: numnerController , ) ,
              // Padding(
              //   padding: const EdgeInsets.symmetric(
              //     horizontal: 35.0,
              //   ),
              //   child: TextFormField(
              //       validator: (value) {
              //         if (value == null || value.isEmpty) {
              //           return 'Please enter a number.';
              //         }
              //         // Validate if the input is a valid number using a regular expression
              //         if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
              //           return 'Please enter a valid number.';
              //         }
              //         return null;
              //       },
              //       controller: numnerController,
              //       keyboardType: TextInputType.number,
              //       decoration: InputDecoration(
              //         border: OutlineInputBorder(
              //           borderSide: const BorderSide(
              //             color: Colors.black54,
              //             width: 2.0,
              //           ),
              //           borderRadius: BorderRadius.circular(25.0),
              //         ),
              //         hintText: "Number",
              //         prefixIcon: Icon(
              //           Icons.person,
              //         ),
              //       )),
              // ),
              
            //    Text(
            //   _selectedDate == null
            //       ? 'No date selected'
            //       : 'Selected Date: ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
            //       style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold ,fontSize: 16),
            // ),
            // SizedBox(height: 20),
            // ElevatedButton(
            //   onPressed: () => _selectDate(context),
            //   child: Text('Select Date'),
            // ),
             Stack(
                  children: [
                    GestureDetector(
                      onTap:   () => _selectDate(context),
                      child:  
                          Column(
                            children: [
                              SizedBox(height:10,),
                              Container(
                                 margin: EdgeInsets.only(left: 20 ,right: 20),
                              width: MediaQuery.of(context).size.width,
                              padding: EdgeInsets.all(15),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                    border: Border.all(
                                    color: Color(0xFFE8E8E8), // Border color
                                    width: 1.0, // Border width
                                    ),
                                    borderRadius: BorderRadius.circular(5), // Optional: border radius
                                  ),
                                child: 
                                Row(children: [
                                //  Icon(
                                //       Icons.date_range,
                                //       color: Colors.grey,
                                //     ), 
                                //     SizedBox(width: 5,),
               Text(
                                           ' ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                  style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold ,fontSize: 16),
                                        ),
                                        Spacer(),
                                           SvgPicture.asset('assets/icons/dropdown.svg')
                                     
                                        // ElevatedButton(
                                        //   onPressed: () => _selectDate(context),
                                        //   child: Text('Select Date'),
                                        // ),
                                ],),
                              ),
                            ],
                          ),
                         
                        
                     
                    ),
                     Positioned(
                        left: 25,
                        top: 5,
                        child: Container(
                         padding: EdgeInsets.only(left: 5 ,right: 5),
                          color: Colors.white,
                     child: Text("DATE OF BIRTH" , style:  TextStyle(fontFamily: 'Gilroy-SemiBold',fontWeight: FontWeight.bold , fontSize: 14  ),),
                        ),
                      )
                  ],
                ),
                
              // Padding(
              //   padding: const EdgeInsets.symmetric(
              //     horizontal: 35.0,
              //   ),
              //   child: 
                // TextFormField(
                //   readOnly: true,
                //     // controller: dateTextController ,
                //   onTap: () { _selectDate(context);},
                //   decoration: InputDecoration(
                //     border: OutlineInputBorder(
                //       borderSide: const BorderSide(
                //         color: Colors.black54,
                //         width: 2.0,
                //       ),
                //       borderRadius: BorderRadius.circular(25.0),
                //     ),

                //     hintText: "Birthday",
                //     prefixIcon: Icon(
                //       Icons.person,
                //     ),
                //   ),
                   
                //   initialValue: dateText,
                // ),
              // ),
              
               Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 20 ,right: 20 , top: 20),
                    width: MediaQuery.of(context).size.width,
                    alignment: Alignment.center,
                    
                    decoration: BoxDecoration(
        border: Border.all(
          color: Color(0xFFE8E8E8),// Border color
        width: 1.0, // Border width
        ),
        borderRadius: BorderRadius.circular(5), // Optional: border radius
      ),
                    child: Row(
                      children: [
                        SizedBox(width: 10,),
                        SizedBox(
                          width: MediaQuery.of(context).size.width*0.81,
                          child: DropdownButton<String>(
                              value: _selectedGender,
                              isExpanded: true,
                              underline: SizedBox(),
                            hint: Text('Select Gender'),
                            iconDisabledColor: Colors.white,
                            icon: SvgPicture.asset('assets/icons/dropdown.svg') ,
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedGender = newValue!;
                              });

                               //print(_selectedGender);
                            },
                            items: <String>['MALE', 'FEMALE', 'OTHER']
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                          
                      ],
                    ),
                  ),
                   Positioned(
                        left: 25,
                        top: 13,
                        child: Container(
                        //  padding: EdgeInsets.only(left: 5 ,right: 5),
                          color: Colors.white,
                     child: Text("GENDER" , style:  TextStyle(fontFamily: 'Gilroy-SemiBold',fontWeight: FontWeight.bold , fontSize: 14  ),),
                        ),
                      )
                ],
              ),
      //         Container(
      //           margin: EdgeInsets.only(left: 30 ,right: 30),
      //           width: MediaQuery.of(context).size.width,
      //           alignment: Alignment.center,
      //           decoration: BoxDecoration(
      //   border: Border.all(
      //   color: Colors.grey, // Border color
      //   width: 1.0, // Border width
      //   ),
      //   borderRadius: BorderRadius.circular(30), // Optional: border radius
      // ),
      //           child: DropdownButton<String>(
      //             value: _selectedGender,
      //             hint: Text('Select Gender'),
      //             onChanged: (String? newValue) {
      //               setState(() {
      //                 _selectedGender = newValue!;
      //               });

      //                //print(_selectedGender);
      //             },
      //             items: <String>['MALE', 'FEMALE', 'OTHER']
      //                 .map<DropdownMenuItem<String>>((String value) {
      //               return DropdownMenuItem<String>(
      //                 value: value,
      //                 child: Text(value),
      //               );
      //             }).toList(),
      //           ),
      //         ),
               SizedBox(
                height: 30.0,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                 
                ),
                child: ElevatedButton(
                  style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all<Color>(
                       Color(0xff4C3AB4),
                  )),
                  onPressed: () async {
                     //print("sumb");
                    if (profileUrl == null && profileFile == null) {
                      context.snackBar("Profile picture is requierd");
                      return;
                    }
                    if (_formKey.currentState!.validate()) {
                      setState((){
                        isLoading = true;
                      });
                      
                      String numberValue =
                          numnerController.text.replaceAll(RegExp(r"\s"), "");
                          String formattedDate = DateFormat('yyyy-MM-dd').format(_selectedDate);
  
                      var result = await loginService.EditUserRequest(
                          context,
                          nameController.text,
                          numberValue,
                           formattedDate ,_selectedGender ,profileFile!.path);
                            //print('error ${result}');
                      if (result['status'] == 200 ) {
                         //print("sucess");
                          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return ProfilePage() ;
          },)) ;
           } else if (result['status'] == 403 ){
 showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
           }else {
                        context.snackBar("Something went wrong!");
                      }
                    }
                    setState(() {
                      isLoading = false;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                 
                    child: !isLoading
                        ? Text(
                            "SUBMIT",
                            style:  TextStyle(fontFamily: 'Gilroy-Bold',
                              fontSize: 18.0,
                              fontWeight: FontWeight.bold,color: Colors.white,
                            ),
                          )
                        : CircularProgressIndicator(color: Colors.white,),
                  ),
                ),
              ),
              SizedBox(height: 50,)
              // if (isLoading) CircularProgressIndicator()
            ],
          ),
        ),
      ),
    );
  }

  void nameDb() async {
    //sembat database code
    try {
      var dbFactory = databaseFactoryIo;

      Directory appDocDir = await getApplicationDocumentsDirectory();
      String databasePath = appDocDir.path + '/database.db';
      var dbPath = databasePath;
      Database db = await dbFactory.openDatabase(dbPath);

      var store = StoreRef.main();
      await store.record('user_name').put(db, nameController.text);

      await db.close();
       //print("sucessfully stroeed $store");
    } catch (e) {
       //print("error :  $e");
    }
  }
}
