import 'package:mla_connect/models/reports.dart';
import 'package:mla_connect/screens/admin/report/report_widgets.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class PanchayatReportPage extends StatefulWidget {
  const PanchayatReportPage({Key? key}) : super(key: key);

  @override
  _PanchayatReportPageState createState() => _PanchayatReportPageState();
}

class _PanchayatReportPageState extends State<PanchayatReportPage> {
  final refreshKey = GlobalKey<RefreshIndicatorState>();

  final List<Panchayat> panch = [];
  List<Panchayat>? filterPanch;

  Future getdata() async {
    try {
      final res = await FirebaseCloud.instance.fetchPanchyats();
      setState(() {
        panch.clear();
        panch.addAll(res.panchayat);
      });
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Future<void> _onRefresh() async {
    await getdata();
  }

  void onSearchText(String? text) {
    setState(() {
      if (text == null || text.isEmpty) {
        filterPanch = null;
      } else {
        filterPanch = panch
            .where((p) => p.name.toLowerCase().contains(text.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => refreshKey.currentState?.show());
  }

  Widget card(Panchayat panchayat) {
    return GestureDetector(
      onTap: () => context.pushWidget((context) => _ReportScreen(
            panchayat: panchayat,
          )),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                panchayat.name,
              
              ),
              Text("Village : ${panchayat.village}",
                  style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black54)),
            ],
          ),
        ),
      ),
    );
  }

  Widget header() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextField(
                  onChanged: (text) => onSearchText(text),
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 18.0,
                    color: Colors.blueAccent,
                  ),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                    hintText: "Search Panchayat",
                    border: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: Colors.blueAccent, width: 32.0),
                        borderRadius: BorderRadius.circular(25.0)),
                  )),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final panch = filterPanch != null ? filterPanch! : this.panch;
    return Scaffold(
      appBar: AppBar(
        title: Text("Panchayat Reports"),
      ),
      body: Column(
        children: [
          header(),
          Expanded(
            child: RefreshIndicator(
                key: refreshKey,
                child: ListView.builder(
                    itemCount: panch.length,
                    itemBuilder: (context, index) {
                      return card(panch[index]);
                    }),
                onRefresh: _onRefresh),
          ),
        ],
      ),
    );
  }
}

class _ReportScreen extends StatefulWidget {
  final Panchayat panchayat;
  const _ReportScreen({Key? key, required this.panchayat}) : super(key: key);

  @override
  __ReportScreenState createState() => __ReportScreenState();
}

class __ReportScreenState extends State<_ReportScreen> {
  final refreshKey = GlobalKey<RefreshIndicatorState>();
  final List<AreaReport> reports = [];

  Future getdata() async {
    try {
      final res =
          await FirebaseCloud.instance.fetchPanchayatReports(widget.panchayat);
      setState(() {
        reports.clear();
        reports.addAll(res.reports);
      });
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Future<void> _onRefresh() async {
    await getdata();
  }

  void onDelete(AreaReport report) async {
    final deleted = await showDialog(
        context: context,
        builder: (context) {
          return DeleteReportDialog.panchayat(report, widget.panchayat);
        });
    if (deleted == true) {
      setState(() {
        reports.removeWhere((element) => element.id == report.id);
      });
    }
  }

  void startUploadSheet() async {
    final report = await showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        builder: (context) {
          return Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: UploadReportSheet.panchayat(widget.panchayat),
          );
        });
    if (report is AreaReport) {
      setState(() {
        reports.add(report);
      });
    }
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => refreshKey.currentState?.show());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(widget.panchayat.name),
          centerTitle: true,
        ),
        body: RefreshIndicator(
            key: refreshKey,
            child: ListView.builder(
                itemCount: reports.length,
                itemBuilder: (context, index) {
                  return ReportCard(
                    report: reports[index],
                    onDelete: onDelete,
                  );
                }),
            onRefresh: _onRefresh),
        floatingActionButton: FloatingActionButton.extended(
            backgroundColor: Colors.black,
            onPressed: () => startUploadSheet(),
            label: Text("Upload Report")));
  }
}
