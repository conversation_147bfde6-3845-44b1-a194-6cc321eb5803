import 'package:mla_connect/models/reports.dart';
import 'package:mla_connect/screens/admin/report/report_widgets.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class AssemblyReportPage extends StatefulWidget {
  const AssemblyReportPage({Key? key}) : super(key: key);

  @override
  _AssembltReportPageState createState() => _AssembltReportPageState();
}

class _AssembltReportPageState extends State<AssemblyReportPage> {
  final refreshKey = GlobalKey<RefreshIndicatorState>();

  final List<AreaReport> pdfs = [];

  Future getdata() async {
    try {
      final r = await FirebaseCloud.instance.fetchAssembyReports();
      setState(() {
        pdfs.clear();
        pdfs.addAll(r.reports);
      });
    } catch (e) {}
  }

  Future<void> _onRefresh() async {
    await getdata();
  }

  void onDelete(AreaReport report) async {
    final deleted = await showDialog(
        context: context,
        builder: (context) {
          return DeleteReportDialog.assembly(report);
        });
    if (deleted == true) {
      setState(() {
        pdfs.removeWhere((element) => element.id == report.id);
      });
    }
  }

  void startUploadSheet() async {
    final report = await showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        builder: (context) {
          return Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: UploadReportSheet.assembly(),
          );
        });
    if (report is AreaReport) {
      setState(() {
        pdfs.add(report);
      });
    }
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => refreshKey.currentState?.show());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Assembly Reports"),
        centerTitle: true,
      ),
      body: RefreshIndicator(
          key: refreshKey,
          child: ListView.builder(
              itemCount: pdfs.length,
              itemBuilder: (context, index) {
                return ReportCard(
                  report: pdfs[index],
                  onDelete: onDelete,
                );
              }),
          onRefresh: _onRefresh),
      floatingActionButton: FloatingActionButton.extended(
          backgroundColor: Colors.black,
          onPressed: () => startUploadSheet(),
          label: Text("Upload Report")),
    );
  }
}
