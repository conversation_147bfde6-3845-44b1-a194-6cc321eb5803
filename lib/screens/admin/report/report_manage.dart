import 'package:mla_connect/screens/admin/report/assembly_report.dart';
import 'package:mla_connect/screens/admin/report/panchayat_report.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class ManageReport extends StatefulWidget {
  const ManageReport({Key? key}) : super(key: key);

  @override
  _ManageReportState createState() => _ManageReportState();
}

class _ManageReportState extends State<ManageReport> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text("Manage Reports"),
        ),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton(
                  onPressed: () =>
                      context.pushWidget((context) => AssemblyReportPage()),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text("Assembly"),
                  )),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 32,
              ),
              ElevatedButton(
                  onPressed: () =>
                      context.pushWidget((context) => PanchayatReportPage()),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text("Panchayat"),
                  ))
            ],
          ),
        ));
  }
}
