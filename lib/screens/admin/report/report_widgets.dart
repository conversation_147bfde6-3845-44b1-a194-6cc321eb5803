import 'package:mla_connect/models/reports.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/pdf_view.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum REPORT_TYPE { ASSEMBLY, PANCHAYAT }

typedef ReportDeleteCallback = Function(AreaReport);

class ReportCard extends StatelessWidget {
  final AreaReport report;
  final ReportDeleteCallback onDelete;
  const ReportCard({Key? key, required this.report, required this.onDelete})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () =>
          context.pushWidget((context) => PdfUrlViewer(report.pdf_url)),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          child: Row(
            children: [
              Icon(
                Icons.picture_as_pdf_outlined,
                color: Colors.red[300],
                size: 40,
              ),
              SizedBox(
                width: 24,
              ),
              Expanded(child: Text(report.name)),
              TextButton(
                  onPressed: () => onDelete(report), child: Text("delete"))
            ],
          ),
        ),
      ),
    );
  }
}

class UploadReportSheet extends StatefulWidget {
  final REPORT_TYPE type;
  final Panchayat? panchayat;
  const UploadReportSheet._(
      {Key? key, required this.type, required this.panchayat})
      : super(key: key);

  const UploadReportSheet.assembly()
      : this._(type: REPORT_TYPE.ASSEMBLY, panchayat: null);

  const UploadReportSheet.panchayat(Panchayat panchayat)
      : this._(type: REPORT_TYPE.PANCHAYAT, panchayat: panchayat);

  @override
  __UploadStateSheet createState() => __UploadStateSheet();
}

class __UploadStateSheet extends State<UploadReportSheet> {
  final tc = TextEditingController();
  PlatformFile? file;
  bool isLoading = false;
  String? error;

  void selectpdf() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
    );
    if (result != null && result.count > 0)
      setState(() {
        file = result.files[0];
      });
  }

  void uploadPdf() async {
    try {
      final f = file;
      if (f == null) {
        setState(() {
          error = "Select A PDF File";
        });
        return;
      }
      setState(() {
        isLoading = true;
        error = null;
      });
      final url = await FirebaseCloud.instance.uploadPdf(f);
      final report = AreaReport(id: "", pdf_url: url, name: tc.text);
      late final ReportUploadResult res;
      if (widget.type == REPORT_TYPE.ASSEMBLY)
        res = await FirebaseCloud.instance.uploadAssemblyReport(report);
      else if (widget.type == REPORT_TYPE.PANCHAYAT)
        res = await FirebaseCloud.instance
            .uploadPanchayatReport(report, widget.panchayat!);

      Navigator.pop(context, res.report);
    } catch (e) {
      setState(() {
        error = e.toError();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 12,
        ),
        if (error != null)
          Text(
            error!,
            style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
          ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: tc,
            maxLength: 30,
            enabled: !isLoading,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            decoration:
                InputDecoration(counterText: "", hintText: "Report Name"),
          ),
        ),
        SizedBox(
          height: 24,
        ),
        TextButton(
            onPressed: isLoading ? null : () => selectpdf(),
            child: Text("select pdf")),
        SizedBox(
          height: 8,
        ),
        Text(file?.name ?? ''),
        SizedBox(
          height: 24,
        ),
        if (isLoading)
          SizedBox(
            height: 24,
            width: 24,
            child: CircularProgressIndicator(),
          ),
        ElevatedButton(
            onPressed: isLoading ? null : () => uploadPdf(),
            child: Text("Upload Report")),
        SizedBox(
          height: 12,
        ),
      ],
    );
  }
}

class DeleteReportDialog extends StatefulWidget {
  final REPORT_TYPE type;
  final Panchayat? panchayat;
  final AreaReport report;

  const DeleteReportDialog._(
      {Key? key,
      required this.type,
      required this.panchayat,
      required this.report})
      : super(key: key);

  const DeleteReportDialog.assembly(AreaReport report)
      : this._(type: REPORT_TYPE.ASSEMBLY, panchayat: null, report: report);

  const DeleteReportDialog.panchayat(AreaReport report, Panchayat panchayat)
      : this._(
            type: REPORT_TYPE.PANCHAYAT, panchayat: panchayat, report: report);

  @override
  _DeleteReportDialogState createState() => _DeleteReportDialogState();
}

class _DeleteReportDialogState extends State<DeleteReportDialog> {
  bool deleting = false;
  String? error;

  void delete() async {
    try {
      setState(() {
        deleting = true;
        error = null;
      });
      if (widget.type == REPORT_TYPE.ASSEMBLY)
        await FirebaseCloud.instance.deleteAssemblyReport(widget.report);
      else if (widget.type == REPORT_TYPE.PANCHAYAT)
        await FirebaseCloud.instance
            .deletePanchayatReport(widget.report, widget.panchayat!);
      Navigator.pop(context, true);
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Delete Report",
              
            ),
            SizedBox(
              height: 16,
            ),
            Text(widget.report.name),
            SizedBox(
              height: 16,
            ),
            if (deleting)
              SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(),
              ),
            Text(
              error ?? "",
              
            ),
            ElevatedButton(onPressed: () => delete(), child: Text("Delete"))
          ],
        ),
      ),
    );
  }
}
