import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:mla_connect/models/analytics.dart';

class AnalyticsPage extends StatefulWidget {
  AnalyticsPage({Key? key}) : super(key: key);

  @override
  _AnalyticsPageState createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends State<AnalyticsPage> {
  final refresh_key = GlobalKey<RefreshIndicatorState>();

  DashboardResponse? _dash;
  String? initError;

  List<AnalyticsItem> items = [];

  Future init() async {
    try {
      initError = null;
      setState(() {});
      final dashboardResponse = await FirebaseCloud.instance.getDashboardData();
      _dash = dashboardResponse;
      Future.delayed(
          Duration(milliseconds: 100), () => refresh_key.currentState?.show());
    } catch (e) {
      initError = e.toError();
    } finally {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  Future<void> _onRefresh() async {
    try {
      DashboardResponse dash = _dash!;
      final resp = await FirebaseCloud.instance.getAnalyticsHome();
      this.items = resp.items.map((e) {
        final role = dash.roles
            .firstWhere((r) => r.roleLevel == e.roleLevel)
            .nodes
            .map((e) => ItemSubCat(id: e.id, value: e.value))
            .toList();
        return e.copyWith(options: role);
      }).toList();

      setState(() {});
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Widget buildItem(AnalyticsItem item) {
    return ListTile(
      enableFeedback: true,
      onTap: () => context.pushWidget((context) => _AnalyticsItemSelect(
            item: item,
          )),
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      title: Text(
        item.name.capitalize(),
        style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18),
      ),
      subtitle: Text("Last Calculated : ${item.formatedCreateTime()}",
          style:  TextStyle(fontFamily: 'Gilroy',fontSize: 12)),
    );
  }

  Widget buildLogic() {
    if (initError != null)
      return Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Text(initError!),
        ),
      );
    if (_dash == null) return PI1();

    return RefreshIndicator(
        key: refresh_key,
        child: ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) => buildItem(items[index])),
        onRefresh: _onRefresh);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Analytics"),
        centerTitle: true,
      ),
      body: buildLogic(),
    );
  }
}

//
//
//

class _AnalyticsItemSelect extends StatefulWidget {
  final AnalyticsItem item;
  const _AnalyticsItemSelect({Key? key, required this.item}) : super(key: key);

  @override
  _AnalyticsItemSelectState createState() => _AnalyticsItemSelectState();
}

class _AnalyticsItemSelectState extends State<_AnalyticsItemSelect> {
  AnalyticsItem get item => widget.item;

  List<ItemSubCat>? filtered;

  Widget buildItem(ItemSubCat cat) {
    return ListTile(
      key: ValueKey(cat.id),
      enableFeedback: true,
      onTap: () => context
          .pushWidget((context) => _AnalyticsDetails(item: item, cat: cat)),
      contentPadding: EdgeInsets.symmetric(horizontal: 32, vertical: 8),
      title: Text(
        "${cat.value}",
        // textAlign: TextAlign.center,
        style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18),
      ),
    );
  }

  Widget header() {
    void onSearchText(String? text) {
      setState(() {
        if (text == null || text.isEmpty) {
          filtered = null;
        } else {
          filtered = item.options
              .where((p) => p.value.toLowerCase().contains(text.toLowerCase()))
              .toList();
        }
      });
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextField(
                  onChanged: (text) => onSearchText(text),
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 18.0,
                    color: Colors.blueAccent,
                  ),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                    hintText: "Search ${item.name.capitalize()}",
                    border: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: Colors.blueAccent, width: 32.0),
                        borderRadius: BorderRadius.circular(25.0)),
                  )),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    List<ItemSubCat> current = filtered != null ? filtered! : item.options;

    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text("Select ${item.name.capitalize()}"),
        ),
        body: Column(
          children: [
            header(),
            Expanded(
              child: ListView.builder(
                itemBuilder: (context, index) => buildItem(current[index]),
                itemCount: current.length,
              ),
            ),
          ],
        ));
  }
}

//
//
//

class _AnalyticsDetails extends StatefulWidget {
  final AnalyticsItem item;
  final ItemSubCat cat;
  const _AnalyticsDetails({Key? key, required this.item, required this.cat})
      : super(key: key);

  @override
  __AnalyticsDetailsState createState() => __AnalyticsDetailsState();
}

class __AnalyticsDetailsState extends State<_AnalyticsDetails> {
  final refresh_key = GlobalKey<RefreshIndicatorState>();

  bool initialised = false;
  String? initError;

  Map<String, CastePref> casteIdMap = {};
  Map<String, VotingPref> votingIdMap = {};
  AnalyticsData? analyticsData;

  void initPage() async {
    try {
      initialised = false;
      initError = null;
      setState(() {});
      final dash = await FirebaseCloud.instance.getDashboardData();

      dash.castes.forEach((c) {
        casteIdMap[c.id] = c;
      });
      dash.voting_pref.forEach((v) {
        votingIdMap[v.id] = v;
      });
      initialised = true;
      Future.delayed(
          Duration(milliseconds: 100), () => refresh_key.currentState?.show());
    } catch (e) {
      initError = e.toError();
    } finally {
      setState(() {});
    }
  }

  Future fetchAnalytics() async {
    try {
      final res = await FirebaseCloud.instance
          .fetchAnalytics(widget.item.name, widget.cat.id);
      setState(() {
        analyticsData = res.data;
      });
    } catch (e) {
       //print(e.toString());
      context.snackBar(e.toError());
    }
  }

  Future<void> _onRefresh() async {
    await fetchAnalytics();
  }

  Widget _analytics(AnalyticsData data) {
    Widget casteTile(CasteData data, {bool padding = true}) {
      String name = casteIdMap[data.id]?.text ?? 'Unknown';
      return ListTile(
        contentPadding: !padding
            ? null
            : const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
        title: Text(name),
        trailing: Text(data.count.toString()),
      );
    }

    Widget votingTile(VotingData data) {
      String name = votingIdMap[data.id]?.text ?? 'Unknown';
      return ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
        title: Text(name),
        trailing: Text(data.count.toString()),
        subtitle: ExpansionTile(
          backgroundColor: Colors.black12,
          title: Text(
            "By Caste",
            style:  TextStyle(fontFamily: 'Gilroy',color: Colors.blue, fontSize: 14),
          ),
          children:
              data.caste_dis.map((e) => casteTile(e, padding: false)).toList(),
        ),
      );
    }

    final style =  TextStyle(fontFamily: 'Gilroy',fontSize: 18, fontWeight: FontWeight.w500);
    return ListView(children: [
      ExpansionTile(
        title: Text("Caste", style: style),
        children: data.caste.map((e) => casteTile(e)).toList(),
      ),
      ExpansionTile(
        title: Text("Voting", style: style),
        children: data.votes.map((e) => votingTile(e)).toList(),
      ),
    ]);
  }

  Widget buildAnalytics(AnalyticsData? data) {
    return RefreshIndicator(
      key: refresh_key,
      onRefresh: _onRefresh,
      child: data == null ? Container() : _analytics(data),
    );
  }

  Widget buildBody() {
    if (!initialised) return PI1();
    if (initError != null)
      return Center(
          child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        child: Text(initError!),
      ));
    return buildAnalytics(analyticsData);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Report"),
        centerTitle: true,
      ),
      body: buildBody(),
    );
  }

  @override
  void initState() {
    super.initState();
    initPage();
  }
}
