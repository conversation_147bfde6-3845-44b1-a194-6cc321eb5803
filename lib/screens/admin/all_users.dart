import 'package:mla_connect/models/TeamMemberProfile.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/profile_pic.dart';
import 'package:flutter/material.dart';

class AllUsersPage extends StatefulWidget {
  AllUsersPage({Key? key}) : super(key: key);

  @override
  _AllUsersPageState createState() => _AllUsersPageState();
}

class _AllUsersPageState extends State<AllUsersPage> {
  final refreshKey = GlobalKey<RefreshIndicatorState>();
  final _controller = ScrollController();
  final List<UserProfile> profiles = [];
  UserFetchResult? _last;
  bool isFetchingBottom = false;

  Future fetchData({refresh = false}) async {
    try {
      if (refresh)
        _last = null;
      else {
        setState(() {
          isFetchingBottom = true;
        });
      }
      final res = await FirebaseCloud.instance.fetchUsers(_last);
      setState(() {
        if (refresh) {
          profiles.clear();
        }
        profiles.addAll(res.profiles);
        _last = res;
        isFetchingBottom = false;
      });
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  _scrollListener() {
    final last = _last;
    if (last != null &&
        _controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && !last.pageEnd) {
        fetchData();
      }
    }
  }

  Future<void> _refresh() async {
    await fetchData(refresh: true);
  }

  @override
  void initState() {
    super.initState();
    _controller.addListener(_scrollListener);
    Future.microtask(() => refreshKey.currentState?.show());
  }

  void scrollToTop() {
    _controller.animateTo(0,
        duration: Duration(milliseconds: 400), curve: Curves.bounceIn);
  }

  @override
  Widget build(BuildContext context) {
    int itemCount = profiles.length + (isFetchingBottom ? 1 : 0);

    return Scaffold(
      appBar: AppBar(
        title: Text("Users"),
        centerTitle: true,
      ),
      body: RefreshIndicator(
          key: refreshKey,
          child: ListView.builder(
              controller: _controller,
              itemCount: itemCount,
              itemBuilder: (context, index) {
                if (isFetchingBottom && index == itemCount - 1) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                } else {
                  return UserCard(
                    profile: profiles[index],
                    index: index,
                  );
                }
              }),
          onRefresh: _refresh),
      floatingActionButton: FloatingActionButton.small(
        backgroundColor: Colors.black87,
        onPressed: () => scrollToTop(),
        child: Icon(
          Icons.arrow_upward,
        ),
      ),
    );
  }
}

class UserCard extends StatelessWidget {
  final int index;
  final UserProfile profile;

  const UserCard({Key? key, required this.profile, required this.index})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
        child: Container(
      padding: EdgeInsets.all(8),
      child: Row(
        children: [
          Text("#${index + 1}", style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black38)),
          SizedBox(
            width: 8,
          ),
          ProfilePicRounded(url: profile.profileUrl, size: 60),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(profile.name),
                  SizedBox(
                    height: 8,
                  ),
                  Text(profile.phoneNumber,
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black38))
                ],
              ),
            ),
          )
        ],
      ),
    ));
  }
}
