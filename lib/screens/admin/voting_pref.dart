import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VotingPrefPage extends StatefulWidget {
  const VotingPrefPage({Key? key}) : super(key: key);

  @override
  _VotingPrefPageState createState() => _VotingPrefPageState();
}

class _VotingPrefPageState extends State<VotingPrefPage> {
  List<VotingPref> prefs = [];

  Future getDashBoard({refresh = false}) async {
    try {
      final dashboardResponse =
          await FirebaseCloud.instance.getDashboardData(forceNetwork: refresh);
      prefs = dashboardResponse.voting_pref;
    } catch (e) {
    } finally {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    getDashBoard();
  }

  void editPref(VotingPref pref) async {
    final res = await showDialog(
        context: context,
        builder: (context) {
          return Dialog(
              child: _AddPref(
            pref: pref,
          ));
        });
    if (res is bool && res == true) {
      setState(() {
        this.prefs.remove(pref);
      });
    } else if (res is VotingPref) {
      final index = this.prefs.indexOf(pref);
      if (index != -1) {
        setState(() {
          this.prefs.removeAt(index);
          this.prefs.insert(index, res);
        });
      }
    }
  }

  void createPref() async {
    final res = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return Dialog(child: _AddPref());
        });
    if (res is VotingPref) prefs.add(res);
    setState(() {});
  }

  Future<void> _onRefresh() async {
    await getDashBoard(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Voting Preference"),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: ListView(
          children: prefs.map((e) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ListTile(
                    title: Text(e.text),
                    // subtitle: e.count > 0 ? Text("${e.count} Votes") : null,
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextButton(
                            onPressed: () => editPref(e), child: Text('edit'))
                      ],
                    ),
                  ),
                ),
                Divider()
              ],
            );
          }).toList(),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
          backgroundColor: Colors.black,
          onPressed: () => createPref(),
          label: Text('Create')),
    );
  }
}

class _AddPref extends StatefulWidget {
  final VotingPref? pref;
  const _AddPref({Key? key, this.pref}) : super(key: key);

  @override
  __AddPrefState createState() => __AddPrefState();
}

class __AddPrefState extends State<_AddPref> {
  late TextEditingController tc;
  bool loadin = false;

  void createPref() async {
    try {
      setState(() {
        loadin = true;
      });
      final text = tc.text.trim();
      final res = await FirebaseCloud.instance.createVotingPref(text);
      Navigator.pop(context, res.pref);
    } catch (e) {
      setState(() {
        loadin = false;
      });
    }
  }

  void deletePref() async {
    try {
      setState(() {
        loadin = true;
      });
      await FirebaseCloud.instance.deleteVotingPref(widget.pref!);
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        loadin = false;
      });
    }
  }

  void editPref() async {
    try {
      setState(() {
        loadin = true;
      });
      final edit = widget.pref!.copyWith(text: tc.text.trim());
      await FirebaseCloud.instance.editVotingPref(edit);
      Navigator.pop(context, edit);
    } catch (e) {
      setState(() {
        loadin = false;
      });
    }
  }

  @override
  void dispose() {
    tc.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    tc = TextEditingController(text: widget.pref?.text);
  }

  @override
  Widget build(BuildContext context) {
     
    final isEditing = widget.pref != null;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isEditing ? "Edit Voting Preference" : "Create Voting Preference",
            
          ),
          if (loadin)
            Center(
              child: SizedBox(
                  width: 20, height: 20, child: CircularProgressIndicator()),
            ),
          TextField(
            controller: tc,
            maxLength: 50,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
          ),
          if (isEditing)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: loadin ? null : () => deletePref(),
                    child: Text(
                      "Delete",
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
                    )),
                TextButton(
                    onPressed: loadin ? null : () => Navigator.pop(context),
                    child: Text("Cancel")),
                TextButton(
                    onPressed: loadin ? null : () => editPref(),
                    child: Text("Save"))
              ],
            ),
          if (!isEditing)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: loadin ? null : () => Navigator.pop(context),
                    child: Text("Cancel")),
                TextButton(
                    onPressed: loadin ? null : () => createPref(),
                    child: Text("Create"))
              ],
            )
        ],
      ),
    );
  }
}
