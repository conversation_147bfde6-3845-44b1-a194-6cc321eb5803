import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/issues.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class ManageAdminIssue extends StatelessWidget {
  const ManageAdminIssue({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: Text('Manage Issues'),
          centerTitle: true,
          bottom: TabBar(
            tabs: [
              Tab(text: "UnResolved"),
              Tab(text: "Resolved"),
              Tab(text: "Fake")
            ],
          ),
        ),
        body: Tab<PERSON>ar<PERSON>iew(
          children: [
            _ManageIssue(
                key: ValueKey("UnResolved"), resolved: RESOLUTION.UNRESOLVED),
            _ManageIssue(
                key: <PERSON><PERSON><PERSON>("Resolved"), resolved: RESOLUTION.RESOLVED),
            _ManageIssue(key: ValueKey("Fake"), resolved: RESOLUTION.FAKE)
          ],
        ),
      ),
    );
  }
}

class _ManageIssue extends StatefulWidget {
  final RESOLUTION resolved;
  const _ManageIssue({Key? key, required this.resolved}) : super(key: key);

  @override
  _ManageAdminIssueState createState() => _ManageAdminIssueState();
}

class _ManageAdminIssueState extends State<_ManageIssue>
    with AutomaticKeepAliveClientMixin {
  final refreshKey = GlobalKey<RefreshIndicatorState>();
  final _controller = ScrollController();
  final List<Issue> issues = [];
  IssueFetchResult? _last;
  bool isFetchingBottom = false;

  StreamSubscription? _sub;

  _scrollListener() {
    final last = _last;
    if (last != null &&
        _controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && !last.pageEnd) {
        fetchData();
      }
    }
  }

  Future fetchData({refresh = false}) async {
    try {
      if (refresh) _last = null;
      final res = await FirebaseCloud.instance
          .fetchIssues(widget.resolved, _last, myFeed: false);
      setState(() {
        if (refresh) {
          issues.clear();
        }
        issues.addAll(res.issues);
        _last = res;
      });
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Future<void> _refresh() async {
    await fetchData(refresh: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _sub?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller.addListener(_scrollListener);
    Future.microtask(() => refreshKey.currentState?.show());

    _sub = FirebaseCloud.instance.issueStream().listen((event) {
      final issue = event.issue;
      if (issue != null) {
        if (event.type == EventType.NEW_POST) {
          setState(() {
            issues.insert(0, issue);
          });
        } else if (event.type == EventType.DELETE) {
          setState(() {
            issues.removeWhere((e) => e.id == issue.id);
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    int itemCount = issues.length + (isFetchingBottom ? 1 : 0);
    return Scaffold(
      body: RefreshIndicator(
        key: refreshKey,
        onRefresh: _refresh,
        child: ListView.builder(
            itemCount: itemCount,
            itemBuilder: (context, index) {
              if (isFetchingBottom && index == itemCount - 1) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                );
              } else {
                return AdminIssueCard(
                  issue: issues[index],
                );
              }
            }),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class AdminIssueCard extends StatefulWidget {
  final Issue issue;
  const AdminIssueCard({Key? key, required this.issue}) : super(key: key);

  @override
  _AdminIssueCardState createState() => _AdminIssueCardState();
}

class _AdminIssueCardState extends State<AdminIssueCard> {
  Issue get issue => widget.issue;

  void confirmResolve() async {
    final confirm = await showDialog(
        context: context,
        builder: (context) {
          return _ResolveConfirm(
            issue: issue,
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      child: Stack(
        children: [
          if (widget.issue.state == RESOLUTION.UNRESOLVED)
            Align(
              alignment: Alignment.topRight,
              child: TextButton(
                child: Text("Resolve"),
                onPressed: () => confirmResolve(),
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "#${widget.issue.ticket_no}",
                    style:  TextStyle(fontFamily: 'Gilroy',
                        color: Colors.black54, fontWeight: FontWeight.w600),
                  ),
                ),
                Row(
                  children: [
                    ClipOval(
                      child: CachedNetworkImage(
                        height: 70,
                        width: 70,
                        fit: BoxFit.cover,
                        imageUrl: issue.authorProfileUrl ?? '',
                        errorWidget: (context, url, error) =>
                            Icon(Icons.person),
                      ),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(issue.authorName ?? "",
                             ),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            issue.formatedCreateTime(),
                           
                          )
                        ],
                      ),
                    )
                  ],
                ),
                
                Text(
                  issue.text,
                 
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ResolveConfirm extends StatefulWidget {
  final Issue issue;
  const _ResolveConfirm({Key? key, required this.issue}) : super(key: key);

  @override
  __ResolveConfirmState createState() => __ResolveConfirmState();
}

class __ResolveConfirmState extends State<_ResolveConfirm> {
  bool isLoading = false;
  String? error;

  void resolve(RESOLUTION state) async {
    try {
      setState(() {
        error = null;
        isLoading = true;
      });
      await FirebaseCloud.instance.resolveIssue(widget.issue, state);
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        error = e.toError();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Confirm Resolve Issue"),
      content: error != null
          ? Text(
              error!,
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
            )
          : (isLoading
              ? SizedBox(
                  height: 20, width: 20, child: CircularProgressIndicator())
              : null),
      actions: [
        TextButton(
            onPressed: isLoading ? null : () => Navigator.pop(context),
            child: Text("Cancel")),
        TextButton(
            onPressed: isLoading ? null : () => resolve(RESOLUTION.RESOLVED),
            child: Text("Resolve")),
        TextButton(
            onPressed: isLoading ? null : () => resolve(RESOLUTION.FAKE),
            child: Text("Fake")),
      ],
    );
  }
}
