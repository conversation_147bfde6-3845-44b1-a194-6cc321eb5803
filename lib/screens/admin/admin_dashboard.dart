import 'package:mla_connect/screens/admin/all_users.dart';
import 'package:mla_connect/screens/admin/analytics_page.dart';
import 'package:mla_connect/screens/admin/caste_pref.dart';
import 'package:mla_connect/screens/admin/issue_manage.dart';
import 'package:mla_connect/screens/admin/report/report_manage.dart';
import 'package:mla_connect/screens/admin/sector/sector_manage.dart';
import 'package:mla_connect/screens/admin/voting_pref.dart';
import 'package:mla_connect/screens/new_voter/application_list.dart';
import 'package:mla_connect/screens/poster/poster_admin_page.dart';
import 'package:flutter/material.dart';
import 'package:mla_connect/services/firebase_cloud.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({Key? key}) : super(key: key);

  @override
  _AdminDashboardState createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  Widget item(
    Function ontap,
    Widget image,
    String name,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: GestureDetector(
          onTap: () => ontap(),
          child: Container(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                ClipOval(
                  child: image,
                ),
                SizedBox(height: 12),
                Text(
                  name,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text("Admin Controls"),
          centerTitle: true,
        ),
        body: GridView.count(
          crossAxisCount: 3,
          children: [
            item(
                () => context.pushWidget((context) => VotingPrefPage()),
                Icon(
                  Icons.how_to_vote_rounded,
                  size: 70,
                ),
                "Voting Preference"),
            item(
                () => context.pushWidget((context) => CastePrefPage()),
                Icon(
                  Icons.people,
                  size: 70,
                ),
                "Castes"),
            item(
                () => context.pushWidget((context) => ManageAdminIssue()),
                Icon(
                  Icons.task,
                  size: 70,
                ),
                "Issues"),
            item(
                () => context.pushWidget((context) => SectorManagePage()),
                Icon(
                  Icons.add_road,
                  size: 70,
                ),
                "Sectors"),
            item(
                () => context.pushWidget((context) => ManageReport()),
                Icon(
                  Icons.report,
                  size: 70,
                ),
                "Reports"),
            item(
                () => context.pushWidget((context) => AllUsersPage()),
                Icon(
                  Icons.account_circle_outlined,
                  size: 70,
                ),
                "Users"),
            item(
                () => context.pushWidget((context) => VoterApplicationPage(
                      isAdminPage: true,
                    )),
                Icon(
                  Icons.file_present,
                  size: 70,
                ),
                "New Voters"),
            item(
                () => context.pushWidget((context) => AnalyticsPage()),
                Icon(
                  Icons.analytics,
                  size: 70,
                ),
                "Analytics"),
            // item(() => context.pushWidget((context) => PosterAdminPage()),
            //     Icon(Icons.podcasts_rounded, size: 70), "Poster")
          ],
        ));
  }
}
