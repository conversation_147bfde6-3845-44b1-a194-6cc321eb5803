import 'package:mla_connect/models/sector.dart';
import 'package:mla_connect/screens/admin/sector/add_sector.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class SectorManagePage extends StatefulWidget {
  const SectorManagePage({Key? key}) : super(key: key);

  @override
  _SectorManagePageState createState() => _SectorManagePageState();
}

class _SectorManagePageState extends State<SectorManagePage> {
  final refresh_key = GlobalKey<RefreshIndicatorState>();

  SectorFetchResult? result;

  fetchData() async {
    try {
      result = await FirebaseCloud.instance.fetchSectors();
      setState(() {});
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  void createSec(SectorFetchResult result) async {
    final res = await context.pushWidget((context) => AddSector(
          availableBooths: result.availableBooths,
        ));
    if (res is SectorCreateResult) {
      refresh_key.currentState?.show();
    }
  }

  void editSector(SectorFetchResult result, SectorData data) async {
    final res = await context.pushWidget((context) => AddSector(
          availableBooths: result.availableBooths,
          sector: data,
        ));
    if (res is SectorCreateResult) {
      refresh_key.currentState?.show();
    }
  }

  void deleteConfirm(SectorData data) async {
    final res = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return _DeleteDialog(
            data: data,
          );
        });
    if (res is bool && res) {
      refresh_key.currentState?.show();
    }
  }

  Future<void> _refresh() async {
    await fetchData();
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => refresh_key.currentState?.show());
  }

  Widget card(SectorData data) {
    return ListTile(
      title: Text(data.name),
      subtitle: Text("Booth count: ${data.booths.length}"),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextButton(
            child: Text('edit'),
            onPressed: () => editSector(result!, data),
          ),
          TextButton(
            child: Text(
              'delete',
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
            ),
            onPressed: () => deleteConfirm(data),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final sec = result?.sectors;
    return Scaffold(
        appBar: AppBar(
          title: Text("Manage Sectors"),
          centerTitle: true,
        ),
        body: RefreshIndicator(
          onRefresh: _refresh,
          key: refresh_key,
          child: sec != null
              ? ListView(
                  children: sec.map((e) => card(e)).toList(),
                )
              : Container(),
        ),
        floatingActionButton: FloatingActionButton.extended(
            backgroundColor: Colors.black,
            onPressed: result != null ? () => createSec(result!) : null,
            label: Text("Create Sector")));
  }
}

class _DeleteDialog extends StatefulWidget {
  final SectorData data;
  _DeleteDialog({Key? key, required this.data}) : super(key: key);

  @override
  __DeleteDialogState createState() => __DeleteDialogState();
}

class __DeleteDialogState extends State<_DeleteDialog> {
  bool isDeleting = false;
  String? error;

  void delete() async {
    setState(() {
      isDeleting = true;
      error = null;
    });
    try {
      await FirebaseCloud.instance.deleteSector(widget.data.id);
      Navigator.pop(context, true);
    } catch (e) {
      error = e.toError();
    } finally {
      setState(() {
        isDeleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("Confirm Delete",
             ),
            if (isDeleting)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(),
                ),
              ),
            if (error != null)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(error!, style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red)),
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text("Cancel")),
                TextButton(onPressed: () => delete(), child: Text("Delete"))
              ],
            )
          ],
        ),
      ),
    );
  }
}
