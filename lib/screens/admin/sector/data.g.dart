// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$BoothDataCWProxy {
  BoothData node(Node node);

  BoothData selected(bool selected);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `BoothData(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// BoothData(...).copyWith(id: 12, name: "My name")
  /// ````
  BoothData call({
    Node? node,
    bool? selected,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfBoothData.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfBoothData.copyWith.fieldName(...)`
class _$BoothDataCWProxyImpl implements _$BoothDataCWProxy {
  const _$BoothDataCWProxyImpl(this._value);

  final BoothData _value;

  @override
  BoothData node(Node node) => this(node: node);

  @override
  BoothData selected(bool selected) => this(selected: selected);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `BoothData(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// BoothData(...).copyWith(id: 12, name: "My name")
  /// ````
  BoothData call({
    Object? node = const $CopyWithPlaceholder(),
    Object? selected = const $CopyWithPlaceholder(),
  }) {
    return BoothData(
      node: node == const $CopyWithPlaceholder() || node == null
          // ignore: unnecessary_non_null_assertion
          ? _value.node!
          // ignore: cast_nullable_to_non_nullable
          : node as Node,
      selected: selected == const $CopyWithPlaceholder() || selected == null
          // ignore: unnecessary_non_null_assertion
          ? _value.selected!
          // ignore: cast_nullable_to_non_nullable
          : selected as bool,
    );
  }
}

extension $BoothDataCopyWith on BoothData {
  /// Returns a callable class that can be used as follows: `instanceOfBoothData.copyWith(...)` or like so:`instanceOfBoothData.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$BoothDataCWProxy get copyWith => _$BoothDataCWProxyImpl(this);
}
