import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/sector.dart';
import 'package:mla_connect/screens/admin/sector/booth_select.dart';
import 'package:mla_connect/screens/admin/sector/data.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AddSector extends StatefulWidget {
  final List<Node> availableBooths;
  final SectorData? sector;
  const AddSector({Key? key, required this.availableBooths, this.sector})
      : super(key: key);

  @override
  _AddSectorState createState() => _AddSectorState();
}

class _AddSectorState extends State<AddSector> {
  late TextEditingController tc;

  List<Node> selectedBooths = [];

  bool hasInit = false;
  String? initError;
  bool isCreating = false;

  final List<Node> availableBooth = [];
  bool get isEdit => widget.sector != null;

  void init() async {
    try {
      setState(() {
        hasInit = false;
        initError = null;
      });
      final dash = await FirebaseCloud.instance.getDashboardData();
      final booths =
          dash.roles.firstWhere((e) => e.roleLevel == BOOTH_ROLE).nodes;
      final List<Node> allBooths = [];
      allBooths.addAll(booths);
      hasInit = true;

      availableBooth.clear();
      availableBooth.addAll(widget.availableBooths);
      selectedBooths.clear();

      final edit = widget.sector;
      if (edit != null) {
        tc = TextEditingController(text: edit.name);
        //assuming admin will receive all booths
        final sb = allBooths.where((e) => edit.booths.contains(e.id)).toList();
        selectedBooths.addAll(sb);

        final avSet = availableBooth.map((e) => e.id).toSet();
        sb.forEach((e) {
          if (!avSet.contains(e.id)) {
            availableBooth.add(e);
          }
        });

        //sort
        availableBooth.sort((a, b) {
          return int.parse(a.value) - int.parse(b.value);
        });
      } else {
        tc = TextEditingController();
      }
    } catch (e) {
      initError = e.toError();
    } finally {
      setState(() {});
    }
  }

  void create() async {
    if (tc.text.isEmpty) {
      context.snackBar("Sector Name cannot be empty");
      return;
    }
    if (selectedBooths.isEmpty) {
      context.snackBar("Select atleast 1 Booth");
      return;
    }
    try {
      setState(() {
        isCreating = true;
      });
      if (isEdit) {
        final result = await FirebaseCloud.instance.editSector(
            widget.sector!.id,
            tc.text,
            selectedBooths.map((e) => e.id).toList());
        Navigator.pop(context, result);
      } else {
        final result = await FirebaseCloud.instance
            .createSector(tc.text, selectedBooths.map((e) => e.id).toList());
        Navigator.pop(context, result);
      }
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      setState(() {
        isCreating = false;
      });
    }
  }

  void showSelectBooth() async {
    final idSet = selectedBooths.map((e) => e.id).toSet();
    final bData = availableBooth
        .map((b) => BoothData(node: b, selected: idSet.contains(b.id)))
        .toList();
    final result = await showDialog(
        context: context,
        builder: (context) {
          return SelectSectorBooth(booths: bData);
        });
    if (result is List<BoothData>)
      setState(() {
        final sN = result.where((e) => e.selected).map((e) => e.node).toList();
        selectedBooths.clear();
        selectedBooths.addAll(sN);
      });
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  Widget boothCard(Node booth) {
    return ListTile(
        title: Text("Booth No. ${booth.value}"),
        trailing: TextButton(
          onPressed: isCreating
              ? null
              : () {
                  setState(() {
                    selectedBooths.remove(booth);
                  });
                },
          child: Text("delete"),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text("Create Sector"),
          centerTitle: true,
          actions: hasInit
              ? [
                  IconButton(
                      onPressed: isCreating ? null : () => create(),
                      icon: Icon(Icons.done))
                ]
              : [],
        ),
        body: hasInit
            ? Column(
                // padding: const EdgeInsets.only(top: 16),
                children: [
                  if (isCreating) ...[
                    SizedBox(
                        height: 30,
                        width: 30,
                        child: CircularProgressIndicator()),
                  ],
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: tc,
                      enabled: !isCreating,
                      maxLength: 50,
                      maxLengthEnforcement: MaxLengthEnforcement.enforced,
                      decoration: InputDecoration(
                          counterText: "", hintText: "Sector Name"),
                    ),
                  ),
                  SizedBox(
                    height: 32,
                  ),
                  ElevatedButton(
                      onPressed: isCreating ? null : () => showSelectBooth(),
                      child: Text("Select Booths")),
                  SizedBox(
                    height: 32,
                  ),
                  Text(
                    "Booths Included",
                    
                  ),
                  Expanded(
                    child: ListView.separated(
                      padding: const EdgeInsets.all(16),
                      separatorBuilder: (context, index) {
                        return Divider();
                      },
                      itemCount: selectedBooths.length,
                      itemBuilder: (context, index) {
                        return boothCard(selectedBooths[index]);
                      },
                    ),
                  )
                ],
              )
            : Center(
                child: initError != null
                    ? Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(initError!),
                      )
                    : CircularProgressIndicator(),
              ));
  }
}
