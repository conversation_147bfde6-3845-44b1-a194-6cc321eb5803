import 'package:mla_connect/screens/admin/sector/data.dart';
import 'package:flutter/material.dart';

class SelectSectorBooth extends StatefulWidget {
  final List<BoothData> booths;
  const SelectSectorBooth({Key? key, required this.booths}) : super(key: key);

  @override
  _SelectSectorBoothState createState() => _SelectSectorBoothState();
}

class _SelectSectorBoothState extends State<SelectSectorBooth> {
  List<BoothData> edits = [];

  @override
  void initState() {
    super.initState();
    edits = widget.booths;
  }

  void setSelected(BoothData data, bool select) {
    final List<BoothData> newList = [];
    edits.forEach((e) {
      if (e.node.id == data.node.id) {
        newList.add(e.copyWith(selected: select));
      } else {
        newList.add(e);
      }
    });
    setState(() {
      edits = newList;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Column(
        children: [
          Expanded(
              child: Scrollbar(
            thumbVisibility: true,
            child: ListView.separated(
                itemBuilder: (context, index) {
                  final e = edits[index];
                  return InkWell(
                    onTap: () {
                      setSelected(e, !e.selected);
                    },
                    child: Row(
                      children: [
                        Checkbox(
                            value: e.selected,
                            onChanged: (value) {
                              if (value != null) {
                                setSelected(e, value);
                              }
                            }),
                        Text(
                          "Booth No. ${e.node.value}",
                          
                        ),
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return Divider();
                },
                itemCount: edits.length),
          )),
          Row(
            children: [
              Expanded(
                  child: TextButton(
                child: Text("Cancel"),
                onPressed: () => Navigator.pop(context),
              )),
              Expanded(
                  child: TextButton(
                child: Text("Add"),
                onPressed: () => Navigator.pop(context, edits),
              ))
            ],
          )
        ],
      ),
    );
  }
}
