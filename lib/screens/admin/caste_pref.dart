import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CastePrefPage extends StatefulWidget {
  const CastePrefPage({Key? key}) : super(key: key);

  @override
  _CastePrefPageState createState() => _CastePrefPageState();
}

class _CastePrefPageState extends State<CastePrefPage> {
  List<CastePref> prefs = [];
  final refresh_key = GlobalKey<RefreshIndicatorState>();

  Future getDashBoard({refresh = false}) async {
    try {
      final dashboardResponse =
          await FirebaseCloud.instance.getDashboardData(forceNetwork: refresh);
      prefs = dashboardResponse.castes;
    } catch (e) {
    } finally {
      setState(() {});
    }
  }

  void editPref(CastePref pref) async {
    final res = await showDialog(
        context: context,
        builder: (context) {
          return Dialog(
              child: _AddPref(
            pref: pref,
          ));
        });
    if (res is bool && res == true) {
      setState(() {
        this.prefs.remove(pref);
      });
    } else if (res is CastePref) {
      final index = this.prefs.indexOf(pref);
      if (index != -1) {
        setState(() {
          this.prefs.removeAt(index);
          this.prefs.insert(index, res);
        });
      }
    }
  }

  void createPref() async {
    final res = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return Dialog(child: _AddPref());
        });
    if (res is CastePref) prefs.add(res);
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => refresh_key.currentState?.show());
  }

  Future<void> _onRefresh() async {
    await getDashBoard(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Manage Castes"),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        key: refresh_key,
        onRefresh: _onRefresh,
        child: ListView(
          children: prefs.map((e) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ListTile(
                    title: Text(e.text),
                    // subtitle: e.count > 0 ? Text("${e.count} Votes") : null,
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextButton(
                            onPressed: () => editPref(e), child: Text('edit'))
                      ],
                    ),
                  ),
                ),
                Divider()
              ],
            );
          }).toList(),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
          backgroundColor: Colors.black,
          onPressed: () => createPref(),
          label: Text('Create')),
    );
  }
}

class _AddPref extends StatefulWidget {
  final CastePref? pref;
  const _AddPref({Key? key, this.pref}) : super(key: key);

  @override
  __AddPrefState createState() => __AddPrefState();
}

class __AddPrefState extends State<_AddPref> {
  late TextEditingController tc;
  bool loadin = false;

  void createPref() async {
    try {
      setState(() {
        loadin = true;
      });
      final text = tc.text.trim();
      final res = await FirebaseCloud.instance.createCastePref(text);
      Navigator.pop(context, res.caste);
    } catch (e) {
      setState(() {
        loadin = false;
      });
    }
  }

  void deletePref() async {
    try {
      setState(() {
        loadin = true;
      });
      await FirebaseCloud.instance.deleteCastePref(widget.pref!);
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        loadin = false;
      });
    }
  }

  void editPref() async {
    try {
      setState(() {
        loadin = true;
      });
      final edit = widget.pref!.copyWith(text: tc.text.trim());
      await FirebaseCloud.instance.editCastePref(edit);
      Navigator.pop(context, edit);
    } catch (e) {
      setState(() {
        loadin = false;
      });
    }
  }

  @override
  void dispose() {
    tc.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    tc = TextEditingController(text: widget.pref?.text);
  }

  @override
  Widget build(BuildContext context) {
     
    final isEditing = widget.pref != null;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isEditing ? "Edit Voting Preference" : "Create Voting Preference",
            
          ),
          if (loadin)
            Center(
              child: SizedBox(
                  width: 20, height: 20, child: CircularProgressIndicator()),
            ),
          TextField(
            controller: tc,
            maxLength: 50,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
          ),
          if (isEditing)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: loadin ? null : () => deletePref(),
                    child: Text(
                      "Delete",
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
                    )),
                TextButton(
                    onPressed: loadin ? null : () => Navigator.pop(context),
                    child: Text("Cancel")),
                TextButton(
                    onPressed: loadin ? null : () => editPref(),
                    child: Text("Save"))
              ],
            ),
          if (!isEditing)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: loadin ? null : () => Navigator.pop(context),
                    child: Text("Cancel")),
                TextButton(
                    onPressed: loadin ? null : () => createPref(),
                    child: Text("Create"))
              ],
            )
        ],
      ),
    );
  }
}
