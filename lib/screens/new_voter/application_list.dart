import 'dart:async';

import 'package:mla_connect/models/issues.dart';
import 'package:mla_connect/models/new_voter.dart';
import 'package:mla_connect/screens/new_voter/create_voter_app.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class VoterApplicationPage extends StatelessWidget {
  final bool isAdminPage;
  const VoterApplicationPage({Key? key, this.isAdminPage = false})
      : super(key: key);

  void createNewVoter(BuildContext context) async {
    context.pushWidget((context) => NewVoterCreatePage());
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
              isAdminPage ? 'Manage New Voters' : 'New Voter Applications'),
          centerTitle: true,
          bottom: TabBar(
            tabs: [Tab(text: "UnResolved"), Tab(text: "Resolved")],
          ),
        ),
        body: TabBarView(
          children: [
            _NewVoterPage(
                key: ValueKey("UnResolved"),
                isAdminPage: isAdminPage,
                resolution: VOTER_RESOLUTION.UNRESOLVED),
            _NewVoterPage(
                key: ValueKey("Resolved"),
                isAdminPage: isAdminPage,
                resolution: VOTER_RESOLUTION.RESOLVED),
          ],
        ),
        floatingActionButton: isAdminPage
            ? null
            : FloatingActionButton.extended(
                backgroundColor: Colors.black,
                onPressed: () => createNewVoter(context),
                label: Text("Add Voter")),
      ),
    );
  }
}

class _NewVoterPage extends StatefulWidget {
  final bool isAdminPage;
  final VOTER_RESOLUTION resolution;
  _NewVoterPage({Key? key, required this.resolution, required this.isAdminPage})
      : super(key: key);

  @override
  _NewVoterPageState createState() => _NewVoterPageState();
}

class _NewVoterPageState extends State<_NewVoterPage>
    with AutomaticKeepAliveClientMixin {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _controller = ScrollController();

  List<NewVoter> voters = [];
  NewApplicationResult? _last;
  bool isFetchingBottom = false;

  StreamSubscription? _sub;

  String? selfUserId;

  void deleteConfirm(NewVoter voter) async {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return _DeleteDialog(
            voter: voter,
          );
        });
  }

  void resolve(NewVoter voter) async {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return _ResolveDialog(
            voter: voter,
          );
        });
  }

  _scrollListener() {
    if (_controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && _last?.pageEnd != true) {
        _startDataFetch();
      }
    }
  }

  Future _startDataFetch({bool refresh = false}) async {
    try {
      if (refresh) {
        _last = null;
      } else {
        setState(() {
          isFetchingBottom = true;
        });
      }
      final call = VoterFetchCall.normal(
          resolution: widget.resolution,
          startOffset: _last?.nextOffset,
          adminPage: widget.isAdminPage);
      final res = await FirebaseCloud.instance.fetchVoterApplications(call);
      if (refresh) {
        voters.clear();
      }
      voters.addAll(res.voters);
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      setState(() {
        isFetchingBottom = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    if (mounted) {
      await _startDataFetch(refresh: true);
    }
  }

  void setup() async {
    final das = await FirebaseCloud.instance.getDashboardData();
    selfUserId = das.profile.id;
  }

  @override
  void initState() {
    super.initState();
    setup();
    _controller.addListener(_scrollListener);
    Future.microtask(() => _refreshIndicatorKey.currentState?.show());
    _sub = FirebaseCloud.instance.newVoterStream().listen((event) {
      if (event.voter.resolution == widget.resolution) {
        if (event.type == VOTER_EVENT_TYPE.CREATE) {
          setState(() {
            voters.insert(0, event.voter);
          });
        } else if (event.type == VOTER_EVENT_TYPE.DELETE) {
          setState(() {
            voters.removeWhere((v) => v.id == event.voter.id);
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _sub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    int itemCount = voters.length + (isFetchingBottom ? 1 : 0);

    return RefreshIndicator(
      key: _refreshIndicatorKey,
      onRefresh: _onRefresh,
      child: ListView.builder(
          itemCount: itemCount,
          itemBuilder: (context, index) {
            if (isFetchingBottom && index == itemCount - 1) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
            } else {
              return card(voters[index]);
            }
          }),
    );
  }

  Widget card(NewVoter v) {
    Widget _IT(String a, String b) {
      return Padding(
        padding: const EdgeInsets.all(2.0),
        child: Row(
          children: [
            Expanded(
              child: Text(
                a,
                style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black87),
              ),
              flex: 1,
            ),
            Expanded(
              child: Text(b),
              flex: 2,
            )
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Stack(
        children: [
          Card(
            child: Container(
                padding: const EdgeInsets.all(8),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _IT("Name", v.name),
                    _IT("Phone", v.phone),
                    _IT("Aadhar No.", v.adharNumber),
                    _IT("Booth No.", v.boothNumber),
                    if (widget.isAdminPage)
                      Align(
                        alignment: Alignment.bottomRight,
                        child: TextButton(
                          onPressed: () => resolve(v),
                          child: Text("Action"),
                        ),
                      )
                  ],
                )),
          ),
          if (selfUserId == v.memberUserId || widget.isAdminPage)
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                  onPressed: () => deleteConfirm(v),
                  icon: Icon(
                    Icons.delete,
                    size: 18,
                  )),
            ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class _DeleteDialog extends StatefulWidget {
  final NewVoter voter;
  _DeleteDialog({Key? key, required this.voter}) : super(key: key);

  @override
  __DeleteDialogState createState() => __DeleteDialogState();
}

class __DeleteDialogState extends State<_DeleteDialog> {
  bool isDeleting = false;
  String? error;

  void delete() async {
    setState(() {
      isDeleting = true;
      error = null;
    });
    try {
      await FirebaseCloud.instance.deleteNewVoter(widget.voter);
      Navigator.pop(context);
    } catch (e) {
      error = e.toError();
    } finally {
      setState(() {
        isDeleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("Confirm Delete",
                ),
            if (isDeleting)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(),
                ),
              ),
            if (error != null)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(error!, style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red)),
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text("Cancel")),
                TextButton(onPressed: () => delete(), child: Text("Delete"))
              ],
            )
          ],
        ),
      ),
    );
  }
}

class _ResolveDialog extends StatefulWidget {
  final NewVoter voter;
  _ResolveDialog({Key? key, required this.voter}) : super(key: key);

  @override
  __ResolveDialogState createState() => __ResolveDialogState();
}

class __ResolveDialogState extends State<_ResolveDialog> {
  bool isResolving = false;
  String? error;

  void resolve(VOTER_RESOLUTION resoltion) async {
    setState(() {
      isResolving = true;
      error = null;
    });
    try {
      final vot = widget.voter.copyWith(resolution: resoltion);
      await FirebaseCloud.instance.resolveNewVoter(vot);
      Navigator.pop(context);
    } catch (e) {
      error = e.toError();
    } finally {
      setState(() {
        isResolving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("Resolve Application",
                ),
            if (isResolving)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(),
                ),
              ),
            if (error != null)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(error!, style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red)),
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text("Cancel")),
                if (widget.voter.resolution == VOTER_RESOLUTION.UNRESOLVED)
                  TextButton(
                      onPressed: () => resolve(VOTER_RESOLUTION.RESOLVED),
                      child: Text("Resolve")),
                if (widget.voter.resolution == VOTER_RESOLUTION.RESOLVED)
                  TextButton(
                      onPressed: () => resolve(VOTER_RESOLUTION.UNRESOLVED),
                      child: Text("UnResolve"))
              ],
            )
          ],
        ),
      ),
    );
  }
}
