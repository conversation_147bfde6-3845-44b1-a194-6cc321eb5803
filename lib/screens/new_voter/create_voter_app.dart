import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/new_voter.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NewVoterCreatePage extends StatefulWidget {
  NewVoterCreatePage({Key? key}) : super(key: key);

  @override
  _NewVoterCreatePageState createState() => _NewVoterCreatePageState();
}

class _NewVoterCreatePageState extends State<NewVoterCreatePage> {
  final name_c = TextEditingController();
  final adhar_c = TextEditingController();
  final phone_c = TextEditingController();
  Node? selectedBooth;

  final List<Node> boothList = [];

  bool isLoading = true;
  String? error;

  bool isCreating = false;

  void getDashboard() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });
      final das = await FirebaseCloud.instance.getDashboardData();
      final boothRole = das.roles.last;
      boothList.addAll(boothRole.nodes);
    } catch (e) {
      error = e.toError();
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void createVoter() async {
    setState(() {
      isCreating = true;
    });
    try {
      final name = name_c.text;
      final phone = phone_c.text;
      final aadhar = adhar_c.text;
      final boothNumber = selectedBooth?.value;

      if (phone.length != 10 || int.tryParse(phone) == null)
        throw Exception("Invalid Phone Number");
      if (aadhar.length != 12) throw Exception("Invalid Aadhar");

      if (boothNumber == null) throw Exception("Booth Not Selected");

      final voter = NewVoter(
          id: "",
          name: name,
          phone: "+91$phone",
          adharNumber: aadhar,
          boothNumber: boothNumber,
          resolution: VOTER_RESOLUTION.UNRESOLVED,
          memberUserId: "");
      await FirebaseCloud.instance.newVoterApplication(voter);
      Navigator.pop(context);
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      setState(() {
        isCreating = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => getDashboard());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text("Add New Voter"),
          centerTitle: true,
          actions: [
            IconButton(
                onPressed: isCreating ? null : () => createVoter(),
                icon: Icon(Icons.done))
          ],
        ),
        body: isLoading
            ? Center(child: CircularProgressIndicator())
            : (error != null
                ? Center(
                    child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(error!),
                  ))
                : SingleChildScrollView(
                    child: Column(
                      children: [
                        gap(),
                        IT(
                            'Name',
                            TextField(
                              controller: name_c,
                              decoration: InputDecoration(counterText: ""),
                            )),
                        gap(),
                        IT(
                            'Aadhar No.',
                            TextField(
                              controller: adhar_c,
                              maxLength: 12,
                              textCapitalization: TextCapitalization.characters,
                              maxLengthEnforcement:
                                  MaxLengthEnforcement.enforced,
                              decoration: InputDecoration(counterText: ""),
                            )),
                        gap(),
                        IT(
                            'Phone No.',
                            Row(
                              children: [
                                Text("+91 "),
                                Expanded(
                                  child: TextField(
                                    controller: phone_c,
                                    maxLength: 10,
                                    keyboardType: TextInputType.phone,
                                    maxLengthEnforcement:
                                        MaxLengthEnforcement.enforced,
                                    decoration:
                                        InputDecoration(counterText: ""),
                                  ),
                                ),
                              ],
                            )),
                        gap(),
                        IT(
                            'Booth No.',
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8.0),
                              child: DropdownButton<Node>(
                                  value: selectedBooth,
                                  onChanged: (value) {
                                    setState(() {
                                      selectedBooth = value;
                                    });
                                  },
                                  items: boothList.map((n) {
                                    return DropdownMenuItem<Node>(
                                        value: n, child: Text(n.value));
                                  }).toList()),
                            )),
                        gap(),
                        if (isCreating)
                          Center(
                            child: SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator()),
                          )
                      ],
                    ),
                  )));
  }

  Widget gap() {
    return SizedBox(
      height: 16,
    );
  }

  Widget IT(String name, Widget inp) {
    return Row(
      children: [
        Expanded(
            child: Text(
              name,
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black54),
            ),
            flex: 1),
        Expanded(child: inp, flex: 2),
      ],
    );
  }
}
