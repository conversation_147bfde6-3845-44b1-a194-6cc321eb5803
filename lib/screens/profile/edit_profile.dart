// import 'dart:convert';
// import 'dart:io';
// import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/services/firebase_cloud.dart';
// import 'package:mla_connect/utils/imagePickerUtil.dart';
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:intl/intl.dart';

// class ProfileEdit extends StatefulWidget {
//   // final SelfProfile profile;
//   const ProfileEdit({Key? key,
//   // required this.profile
//   }) : super(key: key);

//   @override
//   _ProfileEditState createState() => _ProfileEditState();
// }

// class _ProfileEditState extends State<ProfileEdit> {
//   XFile? profileFile;
//   String? profileUrl;
//   String? gender;
//   late TextEditingController name;
//   late TextEditingController epic;
//   // late TextEditingController address;
//   late TextEditingController about;
//   String? dob;
//   Map? address;

//   bool isSaving = false;
//   String task = '';
//   String? fromMumbai;

//   Map<String, String> acsToWards = {
//     'Borivali': 'A',
//     'Dahisar': 'B',
//     'Magathane': 'C',
//     'Mulund': 'D',
//     'Vikhroli': 'E',
//     'Bhandup West': 'F – North',
//     'Jogeshwari East': 'F – South',
//     'Dindoshi': 'G – North',
//     'Kandivali East': 'G – South',
//     'Charkop': 'H – East',
//     'Malad West': 'H – West',
//     'Goregaon': 'K – East',
//     'Versova': 'K – West',
//     'Andheri West': 'L',
//     'Andheri East': 'M – East',
//     'Vile Parle': 'M – West',
//     'Chandivali': 'N',
//     'Ghatkopar West': 'P – North',
//     'Ghatkopar East': 'P – South',
//     'Mankhurd Shivaji Nagar': 'R – Central',
//     'Anushakti Nagar': 'R – North',
//     'Chembur': 'R – South',
//     'Kurla': 'S',
//     'Kalina': 'T',
//   };
//   String? selectedArea = 'Borivali';
//   String? selectedDistrict = 'A';

//   void startPicSelect() async {
//     try {
//       final XFile? image = await ImageUtil.pickImageFromFile();
//       if (image != null)
//         setState(() {
//           this.profileFile = image;
//         });
//     } catch (e) {
//       context.snackBar(e.toError());
//     }
//   }

//   void saveChanges() async {
//     Map<String, String> address = {
//       "district": selectedDistrict ?? "",
//       "area": selectedArea ?? ""
//     };
//     try {
//       setState(() {
//         isSaving = true;
//       });
//       if (profileFile != null) {
//         setState(() {
//           task = 'Uploading Image ...';
//         });
//         profileUrl = await FirebaseCloud.instance
//             .uploadFileToFirebaseStorage(File(profileFile!.path));
//       }
//       setState(() {
//         task = 'Saving Details ...';
//       });
//       final editProfile = widget.profile.copyWith(
//           name: name.text,
//           epicNo: epic.text,
//           address: fromMumbai == 'Yes' ? jsonEncode(address) : '',
//           about: about.text,
//           dob: dob,
//           gender: gender,
//           profileUrl: profileUrl);
//        //print("DOB IS" + dob!);
//       await FirebaseCloud.instance.updateSelfProfile(editProfile);
//       Navigator.pop(context, true);
//     } catch (e) {
//       setState(() {
//         isSaving = false;
//       });
//       context.snackBar(e.toError());
//     }
//   }

//   @override
//   void dispose() {
//     name.dispose();
//     epic.dispose();
//     about.dispose();
//     super.dispose();
//   }

//   @override
//   void initState() {
//     super.initState();
//      //print("DATA CAME IS");
//      //print(widget.profile.toJson());
//     name = TextEditingController(text: widget.profile.name);
//     epic = TextEditingController(text: widget.profile.epicNo);
//     address = address;
//     about = TextEditingController(text: widget.profile.about);
//     profileUrl = widget.profile.profileUrl;
//     dob = widget.profile.dob;
//     gender = widget.profile.gender;
//     if (widget.profile.address != null &&
//         widget.profile.address != "null" &&
//         widget.profile.address != "") {
//       address = jsonDecode(widget.profile.address!);
//       if (address!['district'] != null) selectedDistrict = address!['district'];
//       if (address!['area'] != null) selectedArea = address!['area'];
//     }
//     setState(() {});
//   }

//   Widget pic() {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 40),
//       child: Stack(
//         children: [
//           Align(
//             alignment: Alignment.center,
//             child: Container(
//               child: ClipOval(
//                 child: profileFile != null
//                     ? Image.file(
//                         File(profileFile!.path),
//                         height: 140,
//                         width: 140,
//                         fit: BoxFit.cover,
//                       )
//                     : (profileUrl != null
//                         ? CachedNetworkImage(
//                             imageUrl: profileUrl!,
//                             height: 140,
//                             width: 140,
//                             fit: BoxFit.cover)
//                         : Image.asset("assets/userImage.png",
//                             height: 140, width: 140, fit: BoxFit.contain)),
//               ),
//             ),
//           ),
//           Align(
//             alignment: Alignment.centerRight,
//             child: TextButton(
//               child: Text("Change"),
//               onPressed: () => startPicSelect(),
//             ),
//           )
//         ],
//       ),
//     );
//   }

//   Widget pro_edit(String name, TextEditingController cn) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
//       child: Row(
//         children: [
//           Expanded(
//             child: Text(name),
//             flex: 1,
//           ),
//           Expanded(
//               flex: 2,
//               child: TextField(
//                 controller: cn,
//               ))
//         ],
//       ),
//     );
//   }

//   Widget newDOBShow() {
//     return Column(
//       // mainAxisAlignment: MainAxisAlignment.center,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text("Date of Birth"),
//         SizedBox(height: 10),
//         GestureDetector(
//           child: Container(
//               width: MediaQuery.of(context).size.width * .8,
//               child: Row(
//                 children: [
//                   Container(
//                       height: 50,
//                       width: 70,
//                       decoration: BoxDecoration(
//                           border: Border.all(),
//                           borderRadius: BorderRadius.circular(10)),
//                       child: Center(
//                           child: Text(
//                         (dob == null || dob == "") ? "NA" : dob!.split("-")[0],
//                         style:  TextStyle(fontFamily: 'Gilroy',
//                             fontSize: 16, fontWeight: FontWeight.w200),
//                       ))),
//                   SizedBox(
//                     width: 20,
//                   ),
//                   Container(
//                       height: 50,
//                       width: 70,
//                       decoration: BoxDecoration(
//                           border: Border.all(),
//                           borderRadius: BorderRadius.circular(10)),
//                       child: Center(
//                           child: Text(
//                         (dob == null || dob == "") ? "NA" : dob!.split("-")[1],
//                         style:  TextStyle(fontFamily: 'Gilroy',
//                             fontSize: 16, fontWeight: FontWeight.w200),
//                       ))),
//                   SizedBox(
//                     width: 20,
//                   ),
//                   Container(
//                       height: 50,
//                       width: 70,
//                       decoration: BoxDecoration(
//                           border: Border.all(),
//                           borderRadius: BorderRadius.circular(10)),
//                       child: Center(
//                           child: Text(
//                         (dob == null || dob == "") ? "NA" : dob!.split("-")[2],
//                         style:  TextStyle(fontFamily: 'Gilroy',
//                             fontSize: 16, fontWeight: FontWeight.w200),
//                       )))
//                 ],
//               )),
//           onTap: () async {
//             DateTime? pickedDate = await showDatePicker(
//                 context: context,
//                 initialDate: DateTime.now(),
//                 firstDate: DateTime(1950),
//                 //DateTime.now() - not to allow to choose before today.
//                 lastDate: DateTime.now());

//             if (pickedDate != null) {
//                //print(
//                   pickedDate); //pickedDate output format => 2021-03-10 00:00:00.000
//               String formattedDate =
//                   DateFormat('dd-MM-yyyy').format(pickedDate);
//                //print(formattedDate);
//               //formatted date output using intl package =>  2021-03-16
//               setState(() {
//                 dob = formattedDate; //set output date to TextField value.
//               });
//                //print("DOBB");
//                //print(dob);
//             } else {}
//           },
//         ),
//         SizedBox(height: 20)
//       ],
//     );
//   }

//   Widget pro_edit_new(String title, TextEditingController cn) {
//     return Column(
//       // mainAxisAlignment: MainAxisAlignment.center,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(title),
//         SizedBox(
//           height: 10,
//         ),
//         Container(
//             width: MediaQuery.of(context).size.width * .8,
//             decoration: BoxDecoration(
//                 border: Border.all(), borderRadius: BorderRadius.circular(10)),
//             // color:
//             //  Colors.white,
//             // backgroundColor:
//             // Color(0Xfff5f5f5),
//             height: 60,
//             child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: <Widget>[
//                   Expanded(
//                       child: TextField(
//                           controller: cn,
//                           decoration: InputDecoration(
//                               border: InputBorder.none,
//                               contentPadding: EdgeInsets.all(20.0),
//                               errorBorder: UnderlineInputBorder(
//                                   borderSide:
//                                       BorderSide(color: Colors.white))))),
//                 ])),
//         SizedBox(
//           height: 20,
//         ),
//       ],
//     );
//   }

//   Widget genderSelect() {
//     return Column(
//       // mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text("Gender"),
//         SizedBox(height: 10),
//         Container(
//             width: MediaQuery.of(context).size.width * .8,
//             child: Row(
//               children: [
//                 GestureDetector(
//                   child: Container(
//                       height: 50,
//                       width: 70,
//                       decoration: BoxDecoration(
//                           color: gender == "Male"
//                               ? Color(0XFF434343)
//                               : Colors.white,
//                           border: Border.all(),
//                           // boxShadow: [
//                           //   BoxShadow(
//                           //     offset: Offset(2, 2),
//                           //     blurRadius: 20,
//                           //     color: Color.fromRGBO(0, 0, 0, 0.16),
//                           //   )
//                           // ],
//                           borderRadius: BorderRadius.circular(10)),
//                       child: Center(
//                           child: Text(
//                         "Male",
//                         style:  TextStyle(fontFamily: 'Gilroy',
//                           fontSize: 16,
//                           fontWeight: FontWeight.w200,
//                           color: gender != "Male" ? Colors.black : Colors.white,
//                         ),
//                       ))),
//                   onTap: () {
//                     setState(() {
//                       gender = 'Male';
//                     });
//                   },
//                 ),
//                 SizedBox(
//                   width: 20,
//                 ),
//                 GestureDetector(
//                     child: Container(
//                         height: 50,
//                         width: 70,
//                         decoration: BoxDecoration(
//                             color: gender == "Female"
//                                 ? Color(0XFF434343)
//                                 : Colors.white,
//                             border: Border.all(),
//                             borderRadius: BorderRadius.circular(10)),
//                         child: Center(
//                             child: Text(
//                           "Female",
//                           style:  TextStyle(fontFamily: 'Gilroy',
//                             fontSize: 16,
//                             fontWeight: FontWeight.w200,
//                             color: gender != "Female"
//                                 ? Colors.black
//                                 : Colors.white,
//                           ),
//                         ))),
//                     onTap: () {
//                       setState(() {
//                         gender = 'Female';
//                       });
//                     }),
//                 SizedBox(
//                   width: 20,
//                 ),
//                 GestureDetector(
//                     child: Container(
//                         height: 50,
//                         width: 70,
//                         decoration: BoxDecoration(
//                             color: gender == "Other"
//                                 ? Color(0XFF434343)
//                                 : Colors.white,
//                             border: Border.all(),
//                             borderRadius: BorderRadius.circular(10)),
//                         child: Center(
//                             child: Text(
//                           "Other",
//                           style:  TextStyle(fontFamily: 'Gilroy',
//                             fontSize: 16,
//                             fontWeight: FontWeight.w200,
//                             color:
//                                 gender != "Other" ? Colors.black : Colors.white,
//                           ),
//                         ))),
//                     onTap: () {
//                       setState(() {
//                         gender = 'Other';
//                       });
//                     }),
//               ],
//             )),
//         SizedBox(height: 20)
//       ],
//     );
//   }

//   Widget locationToggle() {
//     return Container(
//         width: MediaQuery.of(context).size.width * .8,
//         child: Column(
//             mainAxisAlignment: MainAxisAlignment.start,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text("Are you located in Mumbai"),
//               SizedBox(height: 10),
//               FlutterToggleTab(
//                 width: 50,
//                 selectedBackgroundColors: [Colors.white],
//                 unSelectedBackgroundColors: [Color(0XFF434343)],
//                 borderRadius: 10,
//                 selectedIndex: fromMumbai == 'Yes' ? 0 : 1,
//                 selectedTextStyle:  TextStyle(fontFamily: 'Gilroy',
//                     color: Colors.black,
//                     fontSize: 18,
//                     fontWeight: FontWeight.w200),
//                 unSelectedTextStyle:  TextStyle(fontFamily: 'Gilroy',
//                     color: Colors.white,
//                     fontSize: 14,
//                     fontWeight: FontWeight.w400),
//                 labels: ['Yes', 'No'],
//                 selectedLabelIndex: (index) {
//                   setState(() {
//                     index == 0 ? fromMumbai = 'Yes' : fromMumbai = 'No';
//                   });
//                    //print("FromMumbai");
//                    //print(fromMumbai);
//                 },
//               ),
//               SizedBox(height: 20)
//             ]));
//   }

//   Widget districtAndAssembly() {
//     return Column(
//       children: [
//         Container(
//             width: MediaQuery.of(context).size.width * .8,
//             child: Column(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text("Select Area"),
//                   SizedBox(height: 10),
//                   Container(
//                       width: 250,
//                       child: DropdownButtonFormField<String>(
//                           dropdownColor: Color(0XFF434343),
//                           decoration: InputDecoration(
//                               border: OutlineInputBorder(
//                                 borderRadius: const BorderRadius.all(
//                                   const Radius.circular(10.0),
//                                 ),
//                               ),
//                               filled: true,
//                               hintStyle:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
//                               hintText: "Choose",
//                               fillColor: Color(0XFF434343)),
//                           value: selectedArea,
//                           items: acsToWards.keys.map<DropdownMenuItem<String>>(
//                             (String key) {
//                               return DropdownMenuItem(
//                                 child: Container(
//                                     color: Color(0XFF434343),
//                                     child: Text(
//                                       key,
//                                       style:  TextStyle(fontFamily: 'Gilroy',
//                                         color: Colors.white,
//                                       ),
//                                       overflow: TextOverflow.ellipsis,
//                                     )),
//                                 value: key,
//                               );
//                               // return DropdownMenuItem<String>(
//                               //   child: Container(
//                               //     color: Color(0XFF434343),
//                               //     child: Text(
//                               //       key,
//                               //       style:  TextStyle(fontFamily: 'Gilroy',
//                               //         color: Colors.white,
//                               //       ),
//                               //     ),
//                               //   ),
//                               //   value: key,
//                               // );
//                             },
//                           ).toList(),

//                           //  [],
//                           onChanged: (value) {
//                             setState(() {
//                               selectedArea = value;
//                             });
//                           })),
//                   SizedBox(
//                     height: 20,
//                   ),
//                   Container(
//                       width: 250,
//                       child: DropdownButtonFormField<String>(
//                           dropdownColor: Color(0XFF434343),
//                           decoration: InputDecoration(
//                               border: OutlineInputBorder(
//                                 borderRadius: const BorderRadius.all(
//                                   const Radius.circular(10.0),
//                                 ),
//                               ),
//                               filled: true,
//                               hintStyle:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
//                               hintText: "Choose",
//                               fillColor: Color(0XFF434343)),
//                           value: selectedDistrict,
//                           items: acsToWards.values
//                               .map<DropdownMenuItem<String>>((String key) {
//                             return DropdownMenuItem(
//                               child: Container(
//                                   color: Color(0XFF434343),
//                                   child: Text(
//                                     key,
//                                     style:  TextStyle(fontFamily: 'Gilroy',
//                                       color: Colors.white,
//                                     ),
//                                   )),
//                               value: key,
//                             );
//                           }).toList(),
//                           onChanged: (value) {
//                             setState(() {
//                               selectedDistrict = value;
//                             });
//                           }))
//                 ]))
//       ],
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBar(
//           title: Text("Edit Profile"),
//           leading: IconButton(
//             onPressed: () {
//               Navigator.pop(context, true);
//             },
//             icon: Icon(Icons.arrow_back),
//             color: Colors.black,
//           ),
//           centerTitle: true,
//           actions: [
//             TextButton(onPressed: () => saveChanges(), child: Text("Save"))
//           ],
//           elevation: 0,
//         ),
//         body: SingleChildScrollView(
//             child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//               // Center(
//               // child: ListView(
//               // shrinkWrap: true,
//               // children: [
//               if (isSaving) ...[
//                 SizedBox(
//                   height: 8,
//                 ),
//                 Center(
//                     child: SizedBox(
//                         height: 20,
//                         width: 20,
//                         child: CircularProgressIndicator())),
//                 SizedBox(
//                   height: 8,
//                 ),
//                 Center(
//                     child:
//                         Text(task,  ))
//               ],
//               // Center(child: Text("Sfedsfs")),
//               pic(),
//               pro_edit_new(" Name", name),
//               newDOBShow(),
//               genderSelect(),
//               locationToggle(),
//               fromMumbai == 'Yes' ? districtAndAssembly() : Container(),
//               SizedBox(
//                 height: 50,
//               )
//               // pro_edit("Name", name),
//               // pro_edit("EPIC No", epic),
//               // pro_edit("Address", address),
//               // pro_edit("About", about),
//               // ],
//               // ))
//             ])));
//   }
// }
