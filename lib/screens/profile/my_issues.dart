import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/issues.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class MyIssues extends StatelessWidget {
  const MyIssues({Key? key}) : super(key: key);

  void createNewIssue(BuildContext context) async {
    context.pushWidget((context) => CreateIssuePage());
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: Text('My Issues'),
          bottom: TabBar(
            tabs: [
              Tab(text: "UnResolved"),
              Tab(text: "Resolved"),
              Tab(text: "Fake")
            ],
          ),
        ),
        body: TabBarView(
          children: [
            MyIssuePage(
                key: <PERSON><PERSON><PERSON>("UnResolved"), resolved: RESOLUTION.UNRESOLVED),
            MyIssuePage(
                key: Value<PERSON>ey("Resolved"), resolved: RESOLUTION.RESOLVED),
            MyIssuePage(key: ValueK<PERSON>("Fake"), resolved: RESOLUTION.FAKE)
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
            backgroundColor: Colors.black,
            onPressed: () => createNewIssue(context),
            label: Text("Create Issue")),
      ),
    );
  }
}

class MyIssuePage extends StatefulWidget {
  final RESOLUTION resolved;
  const MyIssuePage({Key? key, required this.resolved}) : super(key: key);

  @override
  _MyIssuePageState createState() => _MyIssuePageState();
}

class _MyIssuePageState extends State<MyIssuePage>
    with AutomaticKeepAliveClientMixin {
  final refreshKey = GlobalKey<RefreshIndicatorState>();
  final _controller = ScrollController();
  final List<Issue> issues = [];
  IssueFetchResult? _last;
  bool isFetchingBottom = false;
  StreamSubscription? _sub;

  _scrollListener() {
    final last = _last;
    if (last != null &&
        _controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && !last.pageEnd) {
        fetchData();
      }
    }
  }

  Future fetchData({refresh = false}) async {
    try {
      if (refresh) _last = null;
      final res =
          await FirebaseCloud.instance.fetchIssues(widget.resolved, _last);
      setState(() {
        if (refresh) {
          issues.clear();
        }
        issues.addAll(res.issues);
        _last = res;
      });
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  Future<void> _refresh() async {
    await fetchData(refresh: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _sub?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller.addListener(_scrollListener);
    Future.microtask(() => refreshKey.currentState?.show());

    _sub = FirebaseCloud.instance.issueStream().listen((event) {
      final issue = event.issue;
      if (issue != null) {
        if (event.type == EventType.NEW_POST &&
            issue.state == widget.resolved) {
          setState(() {
            issues.insert(0, issue);
          });
        } else if (event.type == EventType.DELETE) {
          setState(() {
            issues.removeWhere((e) => e.id == issue.id);
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    int itemCount = issues.length + (isFetchingBottom ? 1 : 0);
    return RefreshIndicator(
      key: refreshKey,
      onRefresh: _refresh,
      child: ListView.builder(
          itemCount: itemCount,
          itemBuilder: (context, index) {
            if (isFetchingBottom && index == itemCount - 1) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
            } else {
              return IssueCard(
                issue: issues[index],
                showDelete: widget.resolved == RESOLUTION.UNRESOLVED,
              );
            }
          }),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class IssueCard extends StatefulWidget {
  final Issue issue;
  final bool showDelete;
  const IssueCard({Key? key, required this.issue, required this.showDelete})
      : super(key: key);

  @override
  State<IssueCard> createState() => _IssueCardState();
}

class _IssueCardState extends State<IssueCard> {
  void confirmDelete(BuildContext context) async {
    showDialog(
        context: context,
        builder: (context) {
          return _AlertDelete(
            issue: widget.issue,
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      child: Stack(
        children: [
          if (widget.showDelete)
            Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: Icon(Icons.delete),
                  color: Colors.grey,
                  onPressed: () => confirmDelete(context),
                )),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "#${widget.issue.ticket_no}",
                    style:  TextStyle(fontFamily: 'Gilroy',
                        color: Colors.black54, fontWeight: FontWeight.w600),
                  ),
                ),
                Row(
                  children: [
                    ClipOval(
                      child: CachedNetworkImage(
                        height: 70,
                        width: 70,
                        fit: BoxFit.cover,
                        imageUrl: widget.issue.authorProfileUrl ?? '',
                        errorWidget: (context, url, error) =>
                            Icon(Icons.person),
                      ),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(widget.issue.authorName ?? "",
                              ),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            widget.issue.formatedCreateTime(),
                            
                          )
                        ],
                      ),
                    )
                  ],
                ),
                
                Text(
                  widget.issue.text,
                  
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CreateIssuePage extends StatefulWidget {
  const CreateIssuePage({Key? key}) : super(key: key);

  @override
  _CreateIssuePageState createState() => _CreateIssuePageState();
}

class _CreateIssuePageState extends State<CreateIssuePage> {
  final tc = TextEditingController();
  bool isLoading = false;

  void createIssue() async {
    try {
      setState(() {
        isLoading = true;
      });
      final body = CreateIssueBody(type: ISSUE_TYPE.TEXT, text: tc.text);
      final i = await FirebaseCloud.instance.createIssue(body);
      Navigator.pop(context, i);
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      context.snackBar(e.toError());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Create Issue"),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            if (isLoading)
              Center(
                  child: SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(),
              )),
            Container(
              padding: const EdgeInsets.all(8),
              child: TextField(
                controller: tc,
                keyboardType: TextInputType.text,
                maxLines: null,
                decoration: InputDecoration(
                    counterText: "",
                    hintText: "Write Issue",
                    border: OutlineInputBorder()),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton(
                  child: Text("Create"),
                  onPressed: isLoading ? null : () => createIssue()),
            )
          ],
        ),
      ),
    );
  }
}

class _AlertDelete extends StatefulWidget {
  final Issue issue;
  const _AlertDelete({Key? key, required this.issue}) : super(key: key);

  @override
  __AlertDeleteState createState() => __AlertDeleteState();
}

class __AlertDeleteState extends State<_AlertDelete> {
  bool isDeleting = false;
  String? err;

  void deleteIssue() async {
    try {
      setState(() {
        isDeleting = true;
        err = null;
      });
      FirebaseCloud.instance.deleteIssue(widget.issue);
      Navigator.pop(context);
    } catch (e) {
      err = e.toError();
    } finally {
      setState(() {
        isDeleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Confirm Delete Issue"),
      content: err != null
          ? Text(
              err!,
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
            )
          : (isDeleting
              ? SizedBox(
                  height: 20, width: 20, child: CircularProgressIndicator())
              : null),
      actions: [
        TextButton(
            onPressed: () => Navigator.pop(context), child: Text("Cancel")),
        TextButton(onPressed: () => deleteIssue(), child: Text("Confirm")),
      ],
    );
  }
}
