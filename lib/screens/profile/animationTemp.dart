import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';

class AnimationTempClass extends StatefulWidget {
  final BuildContext mycontext;

  const AnimationTempClass({Key? key, required this.mycontext})
      : super(key: key);

  @override
  State<AnimationTempClass> createState() => AnimationTempClassState();
}

class AnimationTempClassState extends State<AnimationTempClass> {
  late Offset _end;
  late ConfettiController _controllerCenter;

  bool _playAnimation = false;

  @override
  void initState() {
    super.initState();
    _end = Offset(MediaQuery.of(widget.mycontext).size.width * 0.8, 0);
    _controllerCenter =
        ConfettiController(duration: const Duration(seconds: 1));
  }

  void playAnimation() {
    _controllerCenter.play();
    setState(() {
      _playAnimation = true;
    });
  }

  @override
  void dispose() {
    _controllerCenter.dispose();

    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (_playAnimation)
          TweenAnimationBuilder(
            tween: BezierTween(
              begin: Offset(MediaQuery.of(context).size.width * 0.2,
                  MediaQuery.of(context).size.height * 0.4),
              control: Offset(0, 200),
              end: _end,
            ),
            duration: Duration(milliseconds: 1500),
            builder: (BuildContext context, Offset value, Widget? child) {
              double progress = (value - Offset.zero).distance / _end.distance;
              double scale = 1.0 + 0.5 * (1 - (2 * (progress - 0.5)).abs());

              return Positioned(
                  left: value.dx,
                  top: value.dy,
                  child:
                      Transform.scale(scale: scale, child: _animatedWidget()));
            },
            onEnd: () {
              setState(() {
                _playAnimation = false;
              });
            },
          ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _controllerCenter,
            blastDirectionality: BlastDirectionality.explosive,
            gravity: 0.4,
            numberOfParticles: 30,

            // don't specify a direction, blast randomly
            // shouldLoop:
            //     true, // start again as soon as the animation is finished
            // manually specify the colors to be used
            // createParticlePath: drawStar, // define a custom shape/path.
          ),
        ),
      ],
    );
  }
}

class BezierTween extends Tween<Offset> {
  final Offset begin;
  final Offset end;
  final Offset control;

  BezierTween({required this.begin, required this.end, required this.control})
      : super(begin: begin, end: end);

  @override
  Offset lerp(double t) {
    final t1 = 1 - t;
    return begin * t1 * t1 + control * 2 * t1 * t + end * t * t;
  }
}

_animatedWidget() {
  return Container(
      width: 90,
      height: 40,
      padding: EdgeInsets.symmetric(vertical: 3),
      decoration: BoxDecoration(
          color: Colors.green, borderRadius: BorderRadius.circular(10)),
      child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(children: [
            WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Image.asset(
                  'assets/icons/1.png',
                  width: 30,
                  fit: BoxFit.cover,
                )),
            TextSpan(
                text: " +5  ",
                style:  TextStyle(fontFamily: 'Gilroy',
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.bold))
          ])));
}
// Widget _congratulate(BuildContext myContext) {
//   Offset _end = Offset(MediaQuery.of(myContext).size.width * 0.8, 0);

//   return Stack(children: [
//     TweenAnimationBuilder(
//       tween: BezierTween(
//         begin: Offset(MediaQuery.of(myContext).size.width * 0.2,
//             MediaQuery.of(myContext).size.height * 0.4),
//         control: Offset(0, 200),
//         end: _end,
//       ),
//       duration: Duration(seconds: 1),
//       builder: (BuildContext context, Offset value, Widget? child) {
//         double progress = (value - Offset.zero).distance / _end.distance;
//         double scale = 1.0 + 1 * (1 - (2 * (progress - 0.5)).abs());

//         return Positioned(
//             left: value.dx,
//             top: value.dy,
//             child: Transform.scale(
//               scale: scale,
//               child: FlutterLogo(),
//             ));
//       },
//     ),
//   ]);
// }
