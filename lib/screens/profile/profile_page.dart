 
import 'dart:io';
import 'dart:typed_data';
 
import 'package:flutter_social_share_plugin/flutter_social_share.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/main.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/new_ui/onboarding/phoneInput.dart';
import 'package:mla_connect/screens/onBoarding_page.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:mla_connect/widgets/idCard.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
 
import 'package:get/get.dart';
 
 
import 'package:path_provider/path_provider.dart';
import 'dart:ui' as ui;

 
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/Post.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    with AutomaticKeepAliveClientMixin {
  final refreshKey = GlobalKey<RefreshIndicatorState>();
  final cardKey = GlobalKey();
LoginService login =LoginService() ;
  Uint8List? _imageFile;
 

  DashboardResponse? dashboard;
  VoterProfile? voterProfile;
// final FirebaseAuth _auth = FirebaseAuth.instance;
@override
  void initState() {
    super.initState();
    fetchdetails () ;
    // Future.microtask(() => fetchSelfProfile());
  }

  var nameSP ;
   var numberSP ;
   var profileURlSP ;
   var positionSP ; 
   var dobSP ;
   var gender;
    var netaName  ;
    var netaImage ;
     var dob ;
void fetchdetails () async{
   final SharedPreferences prefs = await SharedPreferences.getInstance();
   nameSP =   prefs.getString("Name")??"";
    numberSP =   prefs.getString("number")??"";
    profileURlSP =    prefs.getString("profileUrl")??'';
    positionSP =    prefs.getString("ROLEUSERAPI")??"";
    dobSP =    prefs.getString("dob")??"";
    gender =    prefs.getString("gender")??"";
     netaName =   prefs.getString("netaName")??"";
      netaImage =   prefs.getString("netaImage")??"";
   dob  =    prefs.getString("dob") ?? ' ' ;

    setState(() {
      
    });
     }
  @override
  Widget build(BuildContext context) {
     
       fetchdetails ();

    return Scaffold(
      appBar: AppBar( backgroundColor:Colors.white,centerTitle: true,
        title: Text("Profile",style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.black,fontSize: 26)), elevation: 0, actions: [
         IconButton(onPressed:  () {
               Navigator.pushReplacement(context,
                    MaterialPageRoute(builder: (BuildContext context) {
                  return  EditUSerProfile( );
                }));
           }, icon: Icon(Icons.edit , color:  Color(0xff4C3AB4),)) ,
             PopupMenuButton<String>(
                icon: Icon(Icons.more_vert,
                    color:  Color(0xff4C3AB4)), // Added icon color

                onSelected: (String result) async {

                   //print("result $result");
                  if (result == 'Logout')  {
                    try {
                    Navigator.of(context, rootNavigator: true)
                          .pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (BuildContext context) {
                            return PhoneInput();
                          },
                        ),
                        (_) => false,
                      );
                    } catch (e) {
                      context.snackBar(e.toError());
                    }
                  }else if(result == 'Delete your account') {
                   
    var deleteUser = await login.deleteuser(context ) ;
     //print("deleteUser  $deleteUser") ;
 if(deleteUser['status']==200){
  SharedPreferences prefs = await SharedPreferences.getInstance();
    // prefs.setString('message', res['message'].toString() );
    // prefs.setString('userId', res['data']['userId']);
    // prefs.remove('userId');
    // prefs.remove('message');
    // prefs.remove('role');
      
  await prefs.clear();
  
   Navigator.of(context , rootNavigator: true )
                          .pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (BuildContext context) {
                            return PhoneInput();
                          },
                        ),
                        (_) => false,
                      );
 }else if (deleteUser["status"] == 403){
  showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
                        }else{
  final snackBar = SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
          content: const Text('Something went wrong!'),
        );
        ScaffoldMessenger.of(context).showSnackBar(snackBar);
                      }
  
                    }
                },
                itemBuilder: (BuildContext context) => [
                  PopupMenuItem<String>(
                    value: 'Logout',
                    child: Text(
                      'Logout',
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black),
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'Delete your account',
                    child: Text(
                      'Delete your account',
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
                    ),
                  ),
                ],
              )
           
          , 
            
      ]),
      body: ListView(
        // padding: const EdgeInsets.symmetric(horizontal:10, vertical: 8),
        children: [
          
         
          SizedBox(
            height: 5,
          ),
          
          Center(
              child: IDCard(
                  cardKey: cardKey,
                  position: netaName??'' ,
                  profilePicUrl: profileURlSP ?? "",
                  showAssetProfilePic:
                      profileURlSP == null ? true : false,
                  name: nameSP ?? "" ,
                  // netaprofilePicUrl:  netaImage?? ,
                   dob:  dob??"", number: numberSP??'',)),

          
          Padding(
            padding:   EdgeInsets.symmetric(horizontal:20 ),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
    backgroundColor: Color(0xff4C3AB4) , // Change the color here
  ),
              onPressed: () {
               sharePoster("whatsapp", cardKey);
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(5),
                    child: Image.asset("assets/icons/whatsapp.png",
                        height: 32, width: 32),
                  ),
                  Text(
                    "Share",
                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18, color: Colors.white, fontWeight: FontWeight.bold),
                  )
                ],
              ),
            ),
          ),
          
          SizedBox(
            height: 50,
          ),
          
             newProfileItem("NAME", nameSP??""  ),  newProfileItem("GENDER", gender==null ? "null" : gender),
             newProfileItem("DATE OF BIRTH", dobSP ??""),
             newProfileItem("PHONE", numberSP ??""),

            // newDOBShow(),
        
            

            
              SizedBox(
                height: 16,
              ),

             
//               ElevatedButton(
// style: ElevatedButton.styleFrom(
//     backgroundColor: Color(0xff4C3AB4) , // Change the color here
//   ),
                
//                   onPressed: () async {
//                     Navigator.pushReplacement(context,
//                     MaterialPageRoute(builder: (BuildContext context) {
//                   return  EditUSerProfile( );
//                 }));
//                     // startEdit(this.dashboard!.profile);
//                   },
//                   child: Text("Edit Profile")),

              SizedBox(height: 30)
             
            ]
          
        //  ],
      ),
    );
    
  }
  sharePoster(String type ,GlobalKey card) async {
     //print("CLICKED");
    try {
      final boundary = card.currentContext?.findRenderObject();
      double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      if (boundary is RenderRepaintBoundary) {
        ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
        ByteData? byteData =
            await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List? pngBytes = byteData?.buffer.asUint8List();

        final directory =
            (await getTemporaryDirectory()).path; //from path_provide package
        final fileName = "poster.png";
        final path = '$directory/$fileName';

        await File(path).writeAsBytes(pngBytes!, flush: true);

        if (type == 'facebook') {
          await Share.shareXFiles([XFile(path)], text:"ID CARD");
        } else if (type == 'whatsapp') {
           await Share.shareXFiles([XFile(path)], text:"ID CARD");
          // FlutterSocialShare().shareToWhatsApp(
          //     msg:
          //         "ID CARD \n Download your poster now $SHARE_URL",
          //     imagePath: "$path");
        } else {
          await Share.shareXFiles([XFile(path)], text: " ID CARD");
        }
      }
    } catch (e) {
       //print("ERROR COMING INSIDE CATCH");
      // context.snackBar(e.toError());

    }
    //  //print("Sd");
      // ShareUtil.sharePoster(type, widget.model!, widget.profileURl);
  }
 
   
 
  

  void shareIdCard() async {
    try {
      final boundary = cardKey.currentContext?.findRenderObject();
      double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      if (boundary is RenderRepaintBoundary) {
        ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
        ByteData? byteData =
            await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List? pngBytes = byteData?.buffer.asUint8List();

        final directory =
            (await getTemporaryDirectory()).path; //from path_provide package
        final fileName = "id_card.png";
        final path = '$directory/$fileName';

        await File(path).writeAsBytes(pngBytes!, flush: true);
        // final appLink = await FirebaseCloud.instance.createDynamicLink();

      //   FlutterShareMe()
      //       .shareToWhatsApp(msg: "ID Card \n $appLink", imagePath: "$path");
        }
    } catch (e) {
      context.snackBar(e.toError());
    }
  }
  
   

  @override
  void dispose() {
    super.dispose();
  }

  

  Widget newProfileItem(String title, String content) {
  
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
         mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style:   TextStyle(fontFamily: 'Gilroy',color: Color(0xff000000).withOpacity(0.5) , fontSize: 14.5 , fontWeight: FontWeight.bold),),
          Container(
              margin: EdgeInsets.only(top: 10),
               
              height: 40,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      content,
                      style:  TextStyle(fontFamily: 'Gilroy',fontSize: 22, fontWeight: FontWeight.bold, color: Color(0xff000000)),
                    )
                  ]))
        ],
      ),
    );
  }

  // Widget newDOBShow() {
    
    
  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.start,
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Text("Date of Birth"),
  //       SizedBox(width: 10),
  //     dobSP!=null ?  Text(
  //              ":  "+ dobSP ,
  //                 style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16, fontWeight: FontWeight.w200),
  //               ):Text(
  //              "null",
  //                 style:  TextStyle(fontFamily: 'Gilroy',fontSize: 16, fontWeight: FontWeight.w200),
  //               )
        
  //     ],
  //   );
  // }

  Widget item(String t1, String t2) {
     
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 1,
            child: Text(
              "$t1",
               
            ),
          ),
          Expanded(
              flex: 3,
              child: Text(
                t2,
                 
              )),
        ],
      ),
    );
  }


  @override
  bool get wantKeepAlive => true;
}
