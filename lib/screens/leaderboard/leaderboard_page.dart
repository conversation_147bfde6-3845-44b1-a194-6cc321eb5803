import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/LeaderboardResult.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
 

class LeaderBoardPage extends StatefulWidget {
  const LeaderBoardPage({Key? key}) : super(key: key);

  @override
  State<LeaderBoardPage> createState() => _LeaderBoardPageState();
}

class _LeaderBoardPageState extends State<LeaderBoardPage> {
  LeaderboardResult? _leaderboardResult;
  DashboardResponse? _dashboardResponse;

  selfScoreBottomModal(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return ListTile(
            leading: CachedNetworkImage(imageUrl: ""),
            title: Text("NAME"),
            subtitle: Text("phone number"),
            trailing: Text("Points"),
          );
        });
  }

  bool loading = true;
  // RewardController? _rewardController;
  getleaderBoardData() async {
    _leaderboardResult = await FirebaseCloud.instance.getLeaderboard();
    _dashboardResponse = await FirebaseCloud.instance.getDashboardData();

    // _rewardController = Get.isRegistered<RewardController>()
    //     ? Get.find<RewardController>()
    //     : Get.put(RewardController());

    setState(() {
      loading = false;
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getleaderBoardData();
  }

  LeaderboardResult res = new LeaderboardResult(leaderBoard: []);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        bottomSheet: loading ? null : _myPoints(),
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("Leaderboard"),
              GestureDetector(
                  onTap: () async {
                    // final appLink =
                    //     await FirebaseCloud.instance.createDynamicLink();
                    // FlutterShareMe().shareToWhatsApp(
                    //     msg: "Download ${APP_NAME} App Now \n $appLink");
                    // _sendToPosterPage(_selectedIndex);
                  },
                  child: Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                          // border: Border.all(),
                          color: Color.fromARGB(255, 42, 193, 97),
                          borderRadius: BorderRadius.circular(10)),
                      child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(children: [
                            WidgetSpan(
                                alignment: PlaceholderAlignment.middle,
                                child: Icon(
                                  Icons.share,
                                  color: Colors.white,
                                )),
                            TextSpan(
                                text: "  Share and earn points  ",
                                style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white))
                          ]))))
            ],
          ),
          elevation: 0,
          // actions: [
          //   GestureDetector(
          //       onTap: () {
          //         // _sendToPosterPage(_selectedIndex);
          //       },
          //       child: Container(
          //           height: 5,
          //           padding: EdgeInsets.all(2),
          //           decoration: BoxDecoration(
          //               // border: Border.all(),
          //               color: Color.fromARGB(255, 42, 193, 97),
          //               borderRadius: BorderRadius.circular(10)),
          //           child: RichText(
          //               textAlign: TextAlign.center,
          //               text: TextSpan(children: [
          //                 WidgetSpan(
          //                     alignment: PlaceholderAlignment.middle,
          //                     child: Icon(
          //                       Icons.share,
          //                       color: Colors.white,
          //                     )),
          //                 TextSpan(
          //                     text: " Share  ",
          //                     style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white))
          //               ]))))
          // ],
        ),
        body:

            // SingleChildScrollView(
            // physics: NeverScrollableScrollPhysics(),
            // child:
            loading
                ? Center(child: PI1())
                : SingleChildScrollView(
                    child: Column(
                    children: [
                      _leaderboardChart(),
                      // _myPoints(),
                      ListView.separated(
                        physics: NeverScrollableScrollPhysics(),
                        separatorBuilder: ((context, index) => Divider(
                              thickness: 1,
                            )),
                        itemCount: _leaderboardResult!.leaderBoard!.length - 3,
                        shrinkWrap: true,
                        itemBuilder: (context, index) => ListTile(
                          leading: Wrap(
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: [
                              Text(
                                " " + (index + 4).toString() + "  ",
                                style:  TextStyle(fontFamily: 'Gilroy',fontSize: 20),
                              ),
                              CircleAvatar(
                                  radius: 30,
                                  backgroundImage: CachedNetworkImageProvider(
                                    _leaderboardResult!.leaderBoard![index + 3]
                                            .profileUrl ??
                                        USERIMAGE_LINK,
                                  ))
                            ],
                          ),
                          title: Text(
                              _leaderboardResult!.leaderBoard![index + 3].name),
                          subtitle: Text(_leaderboardResult!
                              .leaderBoard![index + 3].phoneNumber),
                          trailing: Text(_leaderboardResult!
                              .leaderBoard![index + 3].points
                              .toString()),
                        ),
                      ),
                      SizedBox(height: 120)
                      //ListView of all the members
                      //fixed bottom sheet
                    ],
                  )));
  }

  Widget _myPoints() {
    return
        // Positioned(
        // child:
        Container(
      height: 100,
      decoration: BoxDecoration(
          border: Border.fromBorderSide(BorderSide()),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      child: Column(
        children: [
          Text(
            "My Share Points",
            style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18),
          ),
          ListTile(
            leading: Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                // Text(_dashboardResponse!.profile.name),
                CircleAvatar(
                    radius: 30,
                    backgroundImage: CachedNetworkImageProvider(
                      _dashboardResponse?.profile.profileUrl ?? USERIMAGE_LINK,
                    ))
              ],
            ),
            title: Text(_dashboardResponse!.profile.name),
            subtitle: Text(_dashboardResponse!.profile.phoneNumber),
            trailing: Text(_dashboardResponse!.profile.totalPoints.toString()),
          )
        ],
      ),
    );
  }

  Widget _leaderboardChart() {
    return Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: 280,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Stack(alignment: Alignment.bottomCenter, children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.3,
                  height: 120,
                  decoration: BoxDecoration(
                      color: PRIMARY_COLOR_ORANGE.withOpacity(0.6),
                      borderRadius: BorderRadius.only(
                          topRight: Radius.circular(20),
                          topLeft: Radius.circular(20))),
                ),
                Positioned(
                    top: -40,
                    left: MediaQuery.of(context).size.width * 0.04,
                    child: CircleAvatar(
                        radius: 45,
                        backgroundColor: PRIMARY_COLOR_ORANGE,
                        child: CircleAvatar(
                            child: CircleAvatar(
                                radius: 42,
                                backgroundImage: CachedNetworkImageProvider(
                                  _leaderboardResult!
                                          .leaderBoard![1].profileUrl ??
                                      USERIMAGE_LINK,
                                )),
                            radius: 40,
                            // minRadius: 20,
                            backgroundImage: CachedNetworkImageProvider(
                              _leaderboardResult!.leaderBoard![0].profileUrl ??
                                  USERIMAGE_LINK,
                            )))),
                Positioned(
                    top: 25,
                    left: 45,
                    child: Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: PRIMARY_COLOR_ORANGE,
                      ),
                      child: Text(
                        "2",
                        style:  TextStyle(fontFamily: 'Gilroy',
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold),
                      ),
                    )),
                Positioned(
                    top: 65,
                    child: Container(
                        width: MediaQuery.of(context).size.width * 0.3,
                        child: Column(
                          children: [
                            Padding(
                                padding: EdgeInsets.all(1),
                                child: Text(
                                  _leaderboardResult!.leaderBoard![1].name,
                                  style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
                                )),
                            Text(
                              _leaderboardResult!.leaderBoard![1].points
                                  .toString(),
                              style:  TextStyle(fontFamily: 'Gilroy',
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold),
                            )
                          ],
                        )))
              ],
            ),
            Stack(clipBehavior: Clip.none, children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.3,
                height: 200,
                decoration: BoxDecoration(
                    color: PRIMARY_COLOR_ORANGE.withOpacity(0.88),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(20),
                        topLeft: Radius.circular(20))),
              ),
              Positioned(
                  top: -45,
                  left: MediaQuery.of(context).size.width * 0.05,
                  child: CircleAvatar(
                      radius: 45,
                      backgroundColor: PRIMARY_COLOR_ORANGE,
                      child: CircleAvatar(
                          radius: 42,
                          backgroundImage: CachedNetworkImageProvider(
                            _leaderboardResult!.leaderBoard![0].profileUrl ??
                                USERIMAGE_LINK,
                          )))),
              Positioned(
                  top: 22,
                  left: 47,
                  child: Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: PRIMARY_COLOR_ORANGE,
                    ),
                    child: Text(
                      "1",
                      style:  TextStyle(fontFamily: 'Gilroy',
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold),
                    ),
                  )),
              Positioned(
                  top: 65,
                  child: Container(
                      width: MediaQuery.of(context).size.width * 0.3,
                      child: Column(
                        children: [
                          Padding(
                              padding: EdgeInsets.all(1),
                              child: Text(
                                _leaderboardResult!.leaderBoard![0].name,
                                style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
                              )),
                          Text(
                            _leaderboardResult!.leaderBoard![0].points
                                .toString(),
                            style:  TextStyle(fontFamily: 'Gilroy',
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          )
                        ],
                      )))
            ]),
            Stack(
                alignment: Alignment.bottomRight,
                clipBehavior: Clip.none,
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width * 0.3,
                    height: 120,
                    decoration: BoxDecoration(
                        color: PRIMARY_COLOR_ORANGE.withOpacity(0.6),
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(20),
                            topLeft: Radius.circular(20))),
                  ),
                  Positioned(
                      top: -40,
                      left: MediaQuery.of(context).size.width * 0.05,
                      child: CircleAvatar(
                          radius: 45,
                          backgroundColor: PRIMARY_COLOR_ORANGE,
                          child: CircleAvatar(
                              child: CircleAvatar(
                                  radius: 42,
                                  backgroundImage: CachedNetworkImageProvider(
                                    _leaderboardResult!
                                            .leaderBoard![2].profileUrl ??
                                        USERIMAGE_LINK,
                                  )),
                              radius: 40,
                              // minRadius: 20,
                              backgroundImage: CachedNetworkImageProvider(
                                _leaderboardResult!
                                        .leaderBoard![0].profileUrl ??
                                    USERIMAGE_LINK,
                              )))),
                  Positioned(
                      top: 25,
                      left: 49,
                      child: Container(
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: PRIMARY_COLOR_ORANGE,
                        ),
                        child: Text(
                          "3",
                          style:  TextStyle(fontFamily: 'Gilroy',
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold),
                        ),
                      )),
                  Positioned(
                      top: 65,
                      child: Container(
                          width: MediaQuery.of(context).size.width * 0.3,
                          child: Column(
                            children: [
                              Padding(
                                  padding: EdgeInsets.all(1),
                                  child: Text(
                                    _leaderboardResult!.leaderBoard![2].name,
                                    style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),
                                  )),
                              Text(
                                _leaderboardResult!.leaderBoard![2].points
                                    .toString(),
                                style:  TextStyle(fontFamily: 'Gilroy',
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold),
                              )
                            ],
                          )))
                ]),
          ],
        ));
  }
}
