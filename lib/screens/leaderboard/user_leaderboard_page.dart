import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../widgets/user_leaderboard.dart';

class UserLeaderboardPage extends StatefulWidget {
  var homeData ;
    UserLeaderboardPage({Key? key , required this.homeData}) : super(key: key);

  @override
  State<UserLeaderboardPage> createState() => _UserLeaderboardPageState();
}

class _UserLeaderboardPageState extends State<UserLeaderboardPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff6F6F6).withOpacity(0.96),
      appBar: AppBar(
        backgroundColor: Colors.black,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20 ,right: 20 ,top: 10),
        child: SingleChildScrollView(
          child: Column(
            children: [
        UserLeaderboard(homeData:widget.homeData,isfirst: false,),
        widget.homeData["posterLeaderBoard"].length<=4 ? SizedBox():   Container(
                                            padding: EdgeInsets.only(top: 10),
                                            height: MediaQuery.of(context)
                                                      .size
                                                      .height  ,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                             child:ListView.builder(
                                                physics: NeverScrollableScrollPhysics(),
        
                                                itemCount:
                                                 widget.homeData["posterLeaderBoard"]
                                                        .length-4,
                                                itemBuilder: (context, ind) {
                                                  var userleaderb = widget.homeData[
                                                      "posterLeaderBoard"][ind+4 ] ;
                                                   //print(userleaderb);
                                                  return Container(
                                                      // height:
                                                      //     MediaQuery.of(context)
                                                      //             .size
                                                      //             .height *
                                                      //         0.2,
                                                      padding: EdgeInsets.all(10),
                                                       margin: EdgeInsets.only(bottom:10),
                                                      // alignment: Alignment.topLeft,
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        // border: Border.all(
                                                        //   color: Colors
                                                        //       .grey, // Border color
                                                        //   width:
                                                        //       1.0, // Border width
                                                        // ),
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                               5), // Optional: border radius
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                              (ind+4).toString() ,
                                                            style:  TextStyle(fontFamily: 'Gilroy',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold),
                                                          ),
                                          SizedBox(
                                                            width: 10,
                                                          ),               
                 Container(
                  width:  MediaQuery.of(context).size.width*0.11  ,
                  height:MediaQuery.of(context).size.width*0.11 ,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    // border: Border.all(width: 2, color:  Colors.black),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider( userleaderb[
                     'profileImgUrl']  ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                                                    
                                                          SizedBox(
                                                            width: 30,
                                                          ),
                                                          Text(
                                                            '${userleaderb['name'].toString()}',
                                                            style:  TextStyle(fontFamily: 'Gilroy',color: Color(0xff5A5A5A),fontSize: 18,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold),
                                                          ),
                                                          
                                                          // Text('Number: ${kk['users'][index]['number']}'),
                                                          SizedBox(
                                                            height: 3,
                                                          ), Spacer(),
                                                           Text('${userleaderb['count'].toString()} pts  ' ,style:  TextStyle(fontFamily: 'Gilroy',color: Colors.grey, fontSize: 20 , fontWeight: FontWeight.bold) ),],
                                                              ));
//                                                            Container( 
//                       height:20,
//                       decoration: BoxDecoration( color: Color.fromARGB(255, 74, 73, 73), borderRadius: BorderRadius.circular(10)),
//                      padding: EdgeInsets.symmetric(horizontal: 10),
//                       child: Row(
//                         children: [
//                           Stack(
//                             children: [
//                                Icon(Icons.circle_outlined ,color:  Colors.grey, size:15,),SizedBox(width: 10,),
// Positioned(right:0,left: 5,
//   child: Icon(Icons.circle_outlined ,color:  Colors.grey, size: 15,)),SizedBox(width: 30,),
                              
//                             ],
//                           ) ,
                         
//                           Text('${userleaderb['count'].toString()}  ' ,style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white),),
//                         ],
//                       ),
//                     ),],
//                                                               ));
                                                },
                                              ),)
            ],
          ),
        ),
      ),
    );
  }
}