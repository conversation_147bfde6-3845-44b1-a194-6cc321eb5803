import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/screens/admin/admin_dashboard.dart';
import 'package:mla_connect/screens/cadre/cadre.dart';
 
import 'package:mla_connect/screens/new_voter/application_list.dart';
 
import 'package:mla_connect/screens/voters/voter_search.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';

class ElectionHomePage extends StatefulWidget {
  const ElectionHomePage({Key? key}) : super(key: key);

  @override
  _ElectionHomePageState createState() => _ElectionHomePageState();
}

class _ElectionHomePageState extends State<ElectionHomePage>
    with AutomaticKeepAliveClientMixin {
  DashboardResponse? data;

  void fetchData() async {
    try {
      data = await FirebaseCloud.instance.getDashboardData();
       //print("DATA IS");
       //print(data);
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      setState(() {});
    }
  }

  Widget gridItem(
      {ImageProvider? image,
      Icon? icon,
      required String text,
      required WidgetBuilder builder}) {
    return GestureDetector(
        onTap: () {
          context.pushWidget(builder);
        },
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              if (image != null)
                CircleAvatar(backgroundImage: image, radius: 40),
              if (icon != null)
                CircleAvatar(
                  child: icon,
                  radius: 40,
                  backgroundColor: Colors.white,
                ),
              SizedBox(height: 12),
              Text(text, textAlign: TextAlign.center),
            ],
          ),
        ));
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => fetchData());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final dasboardData = data;
    if (dasboardData == null) {
      return Scaffold(
        body: PI1(),
      );
    }
    return Scaffold(
        body: GridView.count(crossAxisCount: 3, children: [
      if (dasboardData.isMember) ...[
        gridItem(
            image: AssetImage("assets/icons/ER.png"),
            text: "Electoral Role",
            builder: (context) => VoterSearchPage()),
        gridItem(
            image: AssetImage("assets/icons/BA.png"),
            text: "New Voter Applications",
            builder: (context) => VoterApplicationPage()),
        gridItem(
            image: AssetImage("assets/icons/Cadre.jpeg"),
            text: "Cadre",
            builder: (context) => Cadre())
      ],
      if (dasboardData.isAdmin)
        gridItem(
            image: AssetImage("assets/icons/admin.png"),
            text: "Admin Tab",
            builder: (context) => AdminDashboard()),
      // gridItem(
      //     icon: Icon(
      //       Icons.feed,
      //       size: 80,
      //     ),
      //     text: "Feed",
      //     builder: (context) => Feedpage(
      //           appBar: true,
      //         ))
    ]));
  }

  @override
  bool get wantKeepAlive => true;
}
