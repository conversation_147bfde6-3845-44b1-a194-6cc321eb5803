 
 
 
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({Key? key}) : super(key: key);

  @override
  _SettingPageState createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  void logout() async {
    try {
      await FirebaseCloud.instance.logout();
      setUserBoarded(false);
      // context.pushAndRemoveWidget(
      //     (context) => SigninScreen(), (route) => false);
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  void clearCache() async {
    try {
      final pref = await SharedPreferences.getInstance();
      await pref.clear();
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  // void shareLink() async {
  //   await FirebaseCloud.instance.createDynamicLink();
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Settings"),
        centerTitle: true,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ElevatedButton(
                child: Text("Logout"),
                onPressed: () => logout(),
              ),
              ElevatedButton(
                child: Text("Clear Cache"),
                onPressed: () => clearCache(),
              ),
              // ElevatedButton(
              //   child: Text("Share Link"),
              //   onPressed: () => shareLink(),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
