 
import 'package:mla_connect/controller/poster_controller.dart';
import 'package:mla_connect/models/PosterV2.dart';
 
import 'package:mla_connect/new_ui/create/createPoster.dart';
 
import 'package:mla_connect/screens/poster/poster_list_widget.dart';
import 'package:mla_connect/utils/colorConstants.dart';
 
import 'package:flutter/material.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:get/instance_manager.dart';

class PosterAdminPage extends StatefulWidget {
  const PosterAdminPage({Key? key}) : super(key: key);

  @override
  _PosterAdminPageState createState() => _PosterAdminPageState();
}

class _PosterAdminPageState extends State<PosterAdminPage> {
  final refresh_key = GlobalKey<RefreshIndicatorState>();

  void startPosterCreate() async {
    final result = await context.pushWidget((context) => CreatePoster());
    if (result is PosterNetworkModelV2) {
      refresh_key.currentState?.show();
      var _posterController = Get.find<PosterController>();
      _posterController.addPoster(result);
    }

    return;
    // try {
    //   final file = await ImageUtil.pickImageFromFile();
    //   final path = file?.path;
    //   if (path == null) throw Exception("Image path not found");
    //   final res = await context
    //       .pushWidget((context) => CreatePosterPage(posterPath: path));
    //   if (res is PosterNetworkModel) {
    //     refresh_key.currentState?.show();
    //     var _posterController = Get.find<PosterController>();
    //     _posterController.addPoster(res);
    //   }
    // } catch (e) {
    //   context.snackBar(e.toError());
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        backgroundColor: Color(0xff4C3AB4),
          title: Text("Manage Posters"),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            onPressed: () {
              Navigator.pop(context, true);
            },
            icon: Icon(Icons.arrow_back),
            color: Colors.white,
             
          )),
      body: PosterListWidget(refreshKey: refresh_key, deleteEnabled: true),
      
      floatingActionButton: FloatingActionButton.extended(
          backgroundColor: Color(0xff4C3AB4),
          onPressed: () => startPosterCreate(),

          label: Text(
            "Create",
            style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.white),
          )),
    );
  }
}
