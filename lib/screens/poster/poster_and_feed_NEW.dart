//OBSOLETE



// import 'dart:async';

// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/models/FeedResult.dart';
// import 'package:mla_connect/models/Post.dart';
// import 'package:mla_connect/models/poster.dart';
// import 'package:mla_connect/screens/feed/create_post.dart';
// import 'package:mla_connect/screens/feed/post_card.dart';
// import 'package:mla_connect/screens/poster/poster_for_feed.dart';
// import 'package:mla_connect/screens/poster/preview_page.dart';
// import 'package:mla_connect/services/firebase_cloud.dart';
// import 'package:mla_connect/widgets/progress.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/src/foundation/key.dart';
// import 'package:flutter/src/widgets/container.dart';
// import 'package:flutter/src/widgets/framework.dart';

// class PosterAndFeedNew extends StatefulWidget {
//   const PosterAndFeedNew({Key? key}) : super(key: key);

//   @override
//   State<PosterAndFeedNew> createState() => _PosterAndFeedNewState();
// }

// class _PosterAndFeedNewState extends State<PosterAndFeedNew> {
//   final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
//   final _controller = ScrollController();
//   StreamSubscription<PostEvent>? _subscription;
//   bool isAdmin = false;
//   SelfProfile? profile;

//   @override
//   void dispose() {
//     _subscription?.cancel();
//     super.dispose();
//   }

//   @override
//   void initState() {
//     super.initState();
//     _controller.addListener(_scrollListener);
//     Future.microtask(() => getData());
//     Future.microtask(() => _refreshIndicatorKey.currentState?.show());

//     _subscription = FirebaseCloud.instance.postStream().listen((event) {
//       final post = event.post;
//       if (event.type == EventType.DELETE) {
//         if (post != null) {
//           setState(() {
//             posts.removeWhere((element) => element.id == post.id);
//           });
//         }
//       } else if (event.type == EventType.NEW_POST) {
//         if (post != null) {
//           setState(() {
//             posts.insert(0, post);
//           });
//         }
//       }
//     });
//   }

//   getData() async {
//     try {
//       final data = await FirebaseCloud.instance.getDashboardData();
//       await _startPosterFetch();
//       setState(() {
//         profile = data.profile;
//         currentUID = data.profile.id;
//         isAdmin = data.isAdmin;
//       });
//     } catch (e) {
//        //print(e);
//     } finally {}
//   }

//   bool isFetchingBottom = false;
//   List<Post> posts = [];
//   FeedResult? lastResult;
//   PosterFetchResult? _lastPoster;

//   String? currentUID;
//   final List<PosterNetworkModel> posters = [];

//   _scrollListener() {
//     final last = lastResult;
//     if (last != null &&
//         _controller.offset >= (_controller.position.maxScrollExtent - 50) &&
//         !_controller.position.outOfRange) {
//       if (!isFetchingBottom && !last.pageEnd) {
//         _startDataFetch();
//       }
//     }
//   }

//   Future _startPosterFetch() async {
//     final poster_result = await FirebaseCloud.instance.fetchPoster(_lastPoster);
//     setState(() {
//       posters.addAll(poster_result.posters);
//     });
//   }

//   Future _startDataFetch({bool refresh = false}) async {
//     if (!refresh) {
//       setState(() {
//         isFetchingBottom = true;
//       });
//     }
//     if (refresh) {
//       lastResult = null;
//     }
//     try {
//       final res = await FirebaseCloud.instance
//           .fetchPosts(lastResult: lastResult); // profile return
//       setState(() {
//         if (refresh) posts.clear();
//         posts.addAll(res.posts);
//         lastResult = res;
//         isFetchingBottom = false;
//       });
//     } catch (e) {
//       context.snackBar(e.toError());
//     } finally {
//       setState(() {
//         isFetchingBottom = false;
//       });
//     }
//   }

//   Future<void> _onRefresh() async {
//     await _startDataFetch(refresh: true);
//   }

//   var feedAndPoster = [];
//   void sortedMerge(List<PosterNetworkModel> a, List<Post> b) {
//     //   if(a.isEmpty && b.isEmpty){
//     //     return;
//     //   }else if(a.isEmpty){
//     //   feedAndPoster.addAll(b);
//     //  }else if(b.isEmpty){
//     //   feedAndPoster.addAll(a);
//     //  }

//     int i = 0, j = 0, k = 0;
//     while (i < a.length && j < b.length) {
//       if (a[i].createTimeMs! <= b[j].createTimestamp) {
//         feedAndPoster.add(a[i]);
//         i += 1;
//         k += 1;
//       } else {
//         feedAndPoster.add(b[j]);
//         j += 1;
//         k += 1;
//       }
//     }
//     while (i < a.length) {
//       // b.lengtherging reb.lengthaining
//       // eleb.lengthents of a[] (if any)
//       feedAndPoster.add(a[i]);
//       i += 1;
//       k += 1;
//     }
//     while (j < b.length) {
//       // Merging remaining
//       // elements of b[] (if any)
//       feedAndPoster.add(b[j]);
//       j += 1;
//       k += 1;
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     int itemCount = posts.length + (isFetchingBottom ? 1 : 0) + posters.length;

//     sortedMerge(posters, posts);
//     // posters.forEach((element) {element.createTimeMs! >})

//     // feedAndPoster.addAll(posters);
//     // feedAndPoster.addAll(posts);
//     return Scaffold(
//       appBar: false //widget.appBar
//           ? AppBar(
//               title: Text("Feed"),
//               centerTitle: true,
//             )
//           : null,
//       body: RefreshIndicator(
//           key: _refreshIndicatorKey,
//           onRefresh: _onRefresh,
//           child: ListView.builder(
//             cacheExtent: 9999,
//             controller: _controller,
//             physics: const AlwaysScrollableScrollPhysics(),
//             itemCount: itemCount,
//             itemBuilder: (context, index) {
//                //print("TIME");
//               feedAndPoster[index] is PosterNetworkModel
//                   ?  //print(feedAndPoster[index].createTimeMs)
//                   :  //print(feedAndPoster[index].createTimestamp);
//               if (isFetchingBottom && index == itemCount - 1) {
//                 return Center(
//                   child: Padding(
//                     padding: const EdgeInsets.all(16.0),
//                     child: CircularProgressIndicator(),
//                   ),
//                 );
//               } else {
//                 return profile == null
//                     ? PI1()
//                     : feedAndPoster[index] is PosterNetworkModel
//                         ? GestureDetector(
//                             child: PosterForFeed(
//                               model: feedAndPoster[index],
//                               profile: profile!,
//                             ),
//                             onTap: () =>
//                                 context.pushWidget((context) => PosterPreview(
//                                       profileUrl: profile!.profileUrl!,
//                                       profileName: profile!.name,
//                                       model: feedAndPoster[index],
//                                     )))
//                         : PostCard(
//                             post: feedAndPoster[index],
//                             showDelete: isAdmin,
//                             onDelete: onDelete);
//               }
//             },
//           )),
//       floatingActionButton: isAdmin
//           ? FloatingActionButton(
//               backgroundColor: Colors.black,
//               onPressed: () => context.pushWidget((context) => CreatePost()),
//               child: Icon(Icons.create))
//           : null,
//     );
//   }

//   void onDelete(Post post) {
//     setState(() {
//       posts.removeWhere((element) => element.id == post.id);
//     });
//   }
// }
