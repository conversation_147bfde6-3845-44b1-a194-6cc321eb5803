import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/bottomBarController.dart';
import 'package:mla_connect/controller/poster_controller.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/poster.dart';
 
import 'package:mla_connect/new_ui/poster/presets/preset1.dart';
 
import 'package:mla_connect/screens/poster/poster_util.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/utils/textConstants.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';

import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';
import 'package:infinite_carousel/infinite_carousel.dart';
import 'package:share_plus/share_plus.dart';

class HomePosterCarousel extends StatefulWidget {
  const HomePosterCarousel({Key? key}) : super(key: key);

  @override
  State<HomePosterCarousel> createState() => _HomePosterCarouselState();
}

class _HomePosterCarouselState extends State<HomePosterCarousel> {
  bool _loop = true;

  // Scroll controller for carousel
  late InfiniteScrollController _controller;

  // Maintain current index of carousel
  int _selectedIndex = 0;

  // Get screen width of viewport.
  double get screenWidth => MediaQuery.of(context).size.width;
  // final List<PosterNetworkModel> posters = [];
  SelfProfile? profile;

  PosterController _posterController = Get.put(PosterController());
  @override
  void initState() {
    super.initState();
    _controller = InfiniteScrollController(initialItem: _selectedIndex);
    getData();
  }

  getData() async {
    try {
      final data = await FirebaseCloud.instance.getDashboardData();
      setState(() {
        profile = data.profile;
      });
    } catch (e) {
       //print(e);
    } finally {}
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  void sharePoster(Share type, PosterNetworkModel model) async {
    var profileUrl = profile!.profileUrl!;
    var profileName = profile!.name;

    // final preview = await PosterUtils.overlapPosterFromModel(
    //     model, profileUrl, profileName);

    // try {
    //   if (type == ShareType.Whatsapp)
    //     FlutterShareMe()
    //         .shareToWhatsApp(imagePath: preview!, fileType: FileType.image);
    //   else if (type == ShareType.Any)
    //     Share.share(preview!, subject: "Share Poster");
    // } catch (e) {
    //   context.snackBar(e.toError());
    // }
  }

  // Map<int, bool> showOverlay = {};
  // handleClickPosterClick(int itemIndex) {
  //   setState(() {
  //     if (showOverlay[itemIndex] == true) {
  //       showOverlay[itemIndex] = false;
  //     } else {
  //       showOverlay[itemIndex] = true;
  //     }
  //   });
  // }

  Future<void> _refresh() async {
    await getData();
  }

  _sendToPosterPage(int itemIndex) {
    if (profile?.profileUrl == null) {
      AlertDialog alert = AlertDialog(
        title: Text("Upload profile picture"),
        content: Text("Please upload your profile picture to get your poster"),
        actions: [
          TextButton(
            child: Text("Upload"),
            onPressed: () {
              Navigator.of(context).pop();
              BottomBarController _bottomBarController =
                  Get.find<BottomBarController>();
              _bottomBarController.setCurrentPage(4);
            },
          ),
        ],
      );
      showDialog(
        context: context,
        useRootNavigator: false,
        builder: (BuildContext context) {
          return alert;
        },
      );
    } else {
      // context.pushWidget((context) => PosterView(
      //       model: _posterController.posters[itemIndex],
      //       profile: profile,
      //     ));
    }
  }

  int currentPosterIndex = 0;
  @override
  Widget build(BuildContext context) {
    
    return Obx(() {
      return RefreshIndicator(
          onRefresh: _refresh,
          key: _posterController.refreshIndicatorKey,
          child: 
          // _posterController.posters.isEmpty
          //     ? Center(child: Container()) //PI1())
          //     : 
              Container(
                  decoration: BoxDecoration(
                    // color: Color.fromARGB(255, 168, 234, 219),
                    borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(40.0),
                        bottomLeft: Radius.circular(40.0)),
                  ),
                  child: Column(
                    children: [
                      Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  POSTER_READY_TEXT_MA,
                                  style:  TextStyle(fontFamily: 'Gilroy',
                                    fontSize: 16,
                                  ),
                                ),
                                GestureDetector(
                                    onTap: () {
                                      _sendToPosterPage(_selectedIndex);
                                    },
                                    child: Container(
                                        padding: EdgeInsets.all(2),
                                        decoration: BoxDecoration(
                                            // border: Border.all(),
                                            color: Color.fromARGB(
                                                255, 42, 193, 97),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: RichText(
                                            textAlign: TextAlign.center,
                                            text: TextSpan(children: [
                                              WidgetSpan(
                                                  alignment:
                                                      PlaceholderAlignment
                                                          .middle,
                                                  child: Icon(
                                                    Icons.share,
                                                    color: Colors.white,
                                                  )),
                                              TextSpan(
                                                  text: " Share  ",
                                                  style:  TextStyle(fontFamily: 'Gilroy',
                                                      color: Colors.white))
                                            ]))))
                              ])),
                      SizedBox(height: 10),
                      SizedBox(
                        height: 250,
                        child: InfiniteCarousel.builder(
                          key: Key("posterCarousal"),
                          itemCount: _posterController.posters.length,
                          itemExtent: screenWidth - 50,
                          loop: _loop,
                          controller: _controller,
                          onIndexChanged: (index) {
                            if (_selectedIndex != index) {
                              setState(() {
                                _selectedIndex = index;
                              });
                            }
                          },
                          itemBuilder: (context, itemIndex, realIndex) {
                              //print(_posterController.posters.length) ;
                            return Column(
                              key: Key(_posterController.posters[itemIndex].id),
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Align(
                                    alignment: Alignment.topLeft,
                                    child: Text(
                                      "" +
                                          _posterController
                                              .posters[itemIndex].caption,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    )),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 10.0),
                                  child: GestureDetector(
                                    onTap: () {
                                      currentPosterIndex = realIndex;
                                      _controller.animateToItem(realIndex);
                                    },
                                    child: Container(
                                      height: 225,
                                      width: screenWidth,
                                      child: profile == null
                                          ? Container(
                                              child: PI1(),
                                              height: 50,
                                              width: 50,
                                            )
                                          : GestureDetector(
                                              onTap: () {
                                                _sendToPosterPage(itemIndex);
                                              },
                                              child: Container(
                                                 child:
                                                       Stack(
                                                children: [
                                                  CachedNetworkImage(
                                                      width: double.infinity,
                                                      imageUrl:  
                                                          _posterController
                                                              .posters[
                                                                  itemIndex]
                                                              .posterUrl),
                                                  Center(
                                                      child: AspectRatio(
                                                          aspectRatio: 10 / 9,
                                                          child: profile!
                                                                      .profileUrl ==
                                                                  null
                                                              ? Preset1(
                                                                  minimize:
                                                                      true,
                                                                  name: profile!
                                                                      .name)
                                                              : Preset1(
                                                                  minimize:
                                                                      true,
                                                                  profileUrl:
                                                                      profile!
                                                                          .profileUrl!,
                                                                  name: profile!
                                                                      .name))),
                                                 
                                                ],
                                              ))
                                              
                                              ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: _posterController.posters
                            .asMap()
                            .entries
                            .map((entry) {
                          return GestureDetector(
                              onTap: () => _controller.animateToItem(entry.key),
                              child: Container(
                                  // color: Colors.black38,
                                  width:
                                      _selectedIndex == entry.key ? 16 : 12.0,
                                  height:
                                      _selectedIndex == entry.key ? 16 : 12.0,
                                  margin: EdgeInsets.symmetric(
                                      vertical: 8.0, horizontal: 4.0),
                                  decoration: BoxDecoration(
                                      // shape: BoxShape.circle,
                                      borderRadius: BorderRadius.circular(100),
                                      // borderRadius: BorderRadius.circular(2),
                                      color: _selectedIndex == entry.key
                                          ? PRIMARY_COLOR_ORANGE
                                              .withOpacity(1.0)
                                          : Colors.black.withOpacity(0.4))));
                        }).toList(),
                      ),
                    ],
                  )));
    });
  }
}
