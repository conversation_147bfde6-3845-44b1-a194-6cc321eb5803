import 'dart:io';

import 'package:flutter_social_share_plugin/file_type.dart';
import 'package:flutter_social_share_plugin/flutter_social_share.dart';
import 'package:mla_connect/models/Post.dart';
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/screens/poster/models.dart';
import 'package:mla_connect/screens/poster/poster_util.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';
 
import 'package:photo_view/photo_view.dart';
import 'package:share_plus/share_plus.dart';

class PosterPreview extends StatefulWidget {
  final ViewportData? data;
  final PosterNetworkModel? model;
  final String profileUrl;
  final String profileName;
  PosterPreview(
      {Key? key,
      this.data,
      required this.profileUrl,
      this.model,
      required this.profileName})
      : super(key: key);

  @override
  _PosterPreviewState createState() => _PosterPreviewState();
}

class _PosterPreviewState extends State<PosterPreview> {
  bool generating = true;
  late String previewPath;
  String? error;
  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  void sharePoster(String type) async {
    try {
      if (type == "whatsapp")
        FlutterSocialShare()
            .shareToWhatsApp(imagePath: previewPath, fileType: FileType.image);
      else if (type == "whatsapp")
        Share.share(previewPath, subject: "Share Poster");
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  void generatePreviewFromViewport(ViewportData data) async {
    try {
      setState(() {
        error = null;
        generating = true;
      });
      final res = await PosterUtils.convertToAbsolute(data);
      if (res != null) {
        final profileImg =
            await PosterUtils.getCacheImagePath(widget.profileUrl);
        final ovr = await PosterUtils.overlapPoster(
            res, profileImg, widget.profileName);
        if (ovr != null) {
          generating = false;
          previewPath = ovr;
        } else {
          error = "Error Creating Preview";
        }
      } else {
        error = "Error Reading Image";
      }
    } catch (e) {
      error = e.toError();
    } finally {
      setState(() {});
    }
  }

  void generatePreviewFromModel(PosterNetworkModel model) async {
    try {
      setState(() {
        error = null;
        generating = true;
      });

      final preview = await PosterUtils.overlapPosterFromModel(
          model, widget.profileUrl, widget.profileName);

      if (preview != null) {
        generating = false;
        previewPath = preview;
      } else {
        error = "Error Creating Preview";
      }
    } catch (e) {
      error = e.toError();
    } finally {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    final viewData = widget.data;
    final model = widget.model;
    if (viewData != null)
      Future.microtask(() => generatePreviewFromViewport(viewData));
    else if (model != null) {
      Future.microtask(() => generatePreviewFromModel(model));
    } else {
      Future.microtask(() => () {
            setState(() {
              generating = false;
              error = "No Data to generate Preview";
            });
          });
    }
  }

  Widget loading() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(),
          SizedBox(
            height: 8,
          ),
          Text("Generating Preview"),
          SizedBox(
            height: 2,
          ),
          Text(
            "( This may take time )",
            style:  TextStyle(fontFamily: 'Gilroy',fontSize: 12),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
        data: ThemeData.dark(),
        child: Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              Center(
                  child: error != null
                      ? Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(error!),
                        )
                      : (generating
                          ? loading()
                          : PhotoView(
                              imageProvider: FileImage(File(previewPath))))),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      size: 40,
                    )),
              ),
              if (!generating && widget.model != null)
                Align(
                    alignment: Alignment.bottomCenter,
                    child: buildShareBottom())
            ],
          ),
        ));
  }

  Widget buildShareBottom() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      color: Colors.black87,
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
        IconButton(
          icon: Image.asset('assets/icons/whatsapp.png'),
          iconSize: 30,
          onPressed: () => sharePoster('whatsapp'),
        ),
        IconButton(
          icon: Icon(Icons.share),
          iconSize: 30,
          onPressed: () => sharePoster('whatsapp'),
        )
      ]),
    );
  }
}
