part of 'create_poster.dart';

enum _Control { IMAGE, TEXT }

extension TextAlignExtension on PosterTextAlign {
  Alignment getAlignment() {
    switch (this) {
      case PosterTextAlign.Right:
        return Alignment.centerRight;
      case PosterTextAlign.Left:
        return Alignment.centerLeft;
      default:
        return Alignment.center;
    }
  }

  String get alignName {
    switch (this) {
      case PosterTextAlign.Left:
        return "Left";
      case PosterTextAlign.Right:
        return "Right";
      default:
        return "Center";
    }
  }
}

extension FontExtention on PosterTextFont {
  double get fontSize {
    return 24;
    // switch (this) {
    //   case PosterTextFont.Arial_14:
    //     return 14;
    //   case PosterTextFont.Arial_24:
    //     return 24;
    //   case PosterTextFont.Arial_48:
    //     return 48;
    //   default:
    //     return 14;
    // }
  }
}
