import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/poster_controller.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/PosterV2.dart';
 
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/screens/poster/preview_page.dart';
 
import 'package:mla_connect/services/firebase_cloud.dart';
 
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
 

class PosterListWidget extends StatefulWidget {
  final GlobalKey<RefreshIndicatorState> refreshKey;
  final bool deleteEnabled;
  final bool shareEnabled;

  const PosterListWidget(
      {Key? key,
      required this.refreshKey,
      this.deleteEnabled = false,
      this.shareEnabled = false})
      : super(key: key);

  @override
  _PosterListWidgetState createState() => _PosterListWidgetState();
}

class _PosterListWidgetState extends State<PosterListWidget> {
  final _controller = ScrollController();
  final List<PosterNetworkModelV2> posters = [];
  PosterFetchResultV2? _last;
  bool isFetchingBottom = false;

  SelfProfile? profile;

  GlobalKey<RefreshIndicatorState> get refreshKey => widget.refreshKey;

  _scrollListener() {
    final last = _last;
    if (last != null &&
        _controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && !last.pageEnd) {
        fetchData();
      }
    }
  }

  Future fetchData({bool refresh = false}) async {
    try {
      if (!refresh) {
        setState(() {
          isFetchingBottom = true;
        });
      } else {
        _last = null;
      }
      final result = await FirebaseCloud.instance.fetchPoster(_last);
      if (refresh) posters.clear();
      posters.addAll(result.posters);
      _last = result;
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      if (mounted)
        setState(() {
          isFetchingBottom = false;
        });
    }
  }

  Future<void> _onRefresh() async {
    await fetchData(refresh: true);
  }

  getData({refresh = false}) async {
    try {
      final data =
          await FirebaseCloud.instance.getDashboardData(forceNetwork: refresh);
      setState(() {
        profile = data.profile;
      });
    } catch (e) {
       //print(e);
    } finally {}
  }

  void createPreview(
      String profileUrl, String profileName, PosterNetworkModel model) async {
    context.pushWidget((context) => PosterPreview(
          profileUrl: profileUrl,
          profileName: profileName,
          model: model,
        ));
  }

  void deletePoster(PosterNetworkModelV2 model) async {
    final res = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return _AlertDelete(
            poster: model,
          );
        });
    if (res is bool && res) {
      PosterController _posterController = Get.find<PosterController>();

      setState(() {
        posters.removeWhere((element) => element.id == model.id);
      });
      _posterController.deletePoster(model.id);
    }
  }

  @override
  void initState() {
    super.initState();
    _controller.addListener(_scrollListener);
    // Future.microtask(() => getData());
    // Future.microtask(() => refreshKey.currentState?.show());
    fetchHomeData() ;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();
    super.dispose();
  }
 var homeData ;
 var posterData ;
 LoginService login =LoginService() ;
  fetchHomeData()async {
    var res =await login.fetchHomeData(context) ;
     homeData = res['response'];
    // config =  await login.fetchConfig(context) ;
     //print("homedata $homeData");
    posterData = homeData['posters'] ;
   setState(() {
     
   });
  }

  @override
  Widget build(BuildContext context) {
    int itemCount = posters.length + (isFetchingBottom ? 1 : 0);

    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
     homeData==null? CircularProgressIndicator() :  RefreshIndicator(
            key: refreshKey,
            child: GridView.builder(
                controller: _controller,
                itemCount: posterData.length,
                physics: const AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  if (isFetchingBottom && index == itemCount - 1) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  } else {
                    return buildPoster(posterData[index]);
                  }
                },
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: 4,
                    crossAxisSpacing: 4,
                    childAspectRatio: 3.0 / 4.0)),
            onRefresh: _onRefresh),
        if (widget.shareEnabled &&
            profile != null &&
            profile?.profileUrl == null)
          setProfileInfo(profile!)
      ],
    );
  }

  Widget setProfileInfo(SelfProfile profile) {
    return Row(
      children: [
        Expanded(
            child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text("Set profile pic to enable share"),
        )),
        TextButton(
            onPressed: () {
              // context.pushWidget((context) => ProfileEdit(profile: profile));
            },
            child: Text("Set")),
        TextButton(
            onPressed: () async {
              await getData(refresh: true);
            },
            child: Text("Refresh"))
      ],
    );
  }

  Widget buildPoster(Map<String ,dynamic > posterData ) {
     //print("posterData   $posterData");
    // final profileUrl = profile?.profileUrl;
    // final profileName = profile?.name ?? "";
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Card(
        elevation: 1,
        child: Container(
   
decoration: BoxDecoration(    color: Colors.white,borderRadius: BorderRadius.circular(5),           boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 5,
                    blurRadius: 7,
                    offset: Offset(0, 3), // changes position of shadow
                  ),
                ],
),
          child: Column(
            children: [
              Expanded(
                child: CachedNetworkImage(
                  width: double.maxFinite,
                  imageUrl: posterData['url'],
                  fit: BoxFit.cover,
                ),
              ),
              Divider(color: Colors.grey,),
              Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // if (widget.shareEnabled && profileUrl != null)
                  //   TextButton(
                  //       onPressed: () => {},
                  //       // createPreview(profileUrl, profileName, model),
                  //       child: Text("Your Poster")),
                  if (widget.deleteEnabled)
                    TextButton(
                        onPressed: () async {
                          var posterId =posterData['posterId'] ;
                         //print(posterData['posterId'])  ;
                       var res =  await login.deleteposter(context ,posterId) ;
                        //print(res);
// setState(() {
  
// }); 
if(res['status']!=200) {
   final snackBar = SnackBar(
          content: const Text('Somethinng Went Wrong!'),
      );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}else if (res ["status"] == 403){
  showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return SessionExpiredDialog();
                  },
                );
                          }else{
  Navigator.pop(context);
}
                        },
                        // () => deletePoster(model),
                        child: Text("Delete" ,style: TextStyle(color: Color(0xff4C3AB4) , fontWeight: FontWeight.bold , fontSize: 20),))
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

class _AlertDelete extends StatefulWidget {
  final PosterNetworkModelV2 poster;
  const _AlertDelete({Key? key, required this.poster}) : super(key: key);

  @override
  __AlertDeleteState createState() => __AlertDeleteState();
}

class __AlertDeleteState extends State<_AlertDelete> {
  bool isDeleting = false;
  String? err;

  void deleteIssue() async {
    try {
      setState(() {
        isDeleting = true;
        err = null;
      });
      await FirebaseCloud.instance.deletePoster(widget.poster);
      Navigator.pop(context, true);
    } catch (e) {
      err = e.toError();
    } finally {
      setState(() {
        isDeleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Confirm Delete Poster"),
      content: err != null
          ? Text(
              err!,
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
            )
          : (isDeleting
              ? SizedBox(
                  height: 40,
                  width: 20,
                  child: Center(child: CircularProgressIndicator()))
              : null),
      actions: [
        TextButton(
            onPressed: () => Navigator.pop(context), child: Text("Cancel")),
        TextButton(onPressed: () => deleteIssue(), child: Text("Confirm")),
      ],
    );
  }
}
