// import 'dart:io';

// import 'package:mla_connect/models/Dashboard.dart';
// import 'package:mla_connect/models/poster.dart';
// import 'package:mla_connect/screens/poster/poster_util.dart';
// import 'package:mla_connect/utils/constants.dart';
// import 'package:mla_connect/widgets/progress.dart';
// import 'package:flutter/material.dart';
 

// class PosterForFeed extends StatefulWidget {
//   SelfProfile? profile;
//   PosterNetworkModel? model;

//   PosterForFeed({Key? key, this.profile, this.model}) : super(key: key);

//   @override
//   State<PosterForFeed> createState() => _PosterForFeedState();
// }

// class _PosterForFeedState extends State<PosterForFeed> {
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     Future.delayed(Duration.zero, () async {
//       generatePreviewFromModel();
//     });
//   }

//   // final profileUrl = widget.profile?.profileUrl;
//   // final profileName = widget.profile?.name ?? "";
//   bool generating = true;
//   String? previewPath;
//   String? error;

//   void generatePreviewFromModel() async {
//     try {
//       setState(() {
//         error = null;
//         generating = true;
//       });

//       final preview = await PosterUtils.overlapPosterFromModel(
//           widget.model!,
//           widget.profile?.profileUrl ?? USERIMAGE_LINK,
//           widget.profile?.name ?? "");

//       if (preview != null) {
//         generating = false;
//         previewPath = preview;
//       } else {
//         error = "Error Creating Preview";
//       }
//     } catch (e) {
//       // error = e.toError();
//     } finally {
//       if (mounted) setState(() {});
//     }
//   }

//   @override
//   void dispose() {
//     // TODO: implement dispose
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return
//         // padding: EdgeInsets.all(15),
//         Card(
//             elevation: 1,
//             child: error != null
//                 ? Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Text(error!),
//                   )
//                 : (generating
//                     ? Center(
//                         child: Container(
//                         child: PI1(),
//                         height: 50,
//                         width: 50,
//                       ))
//                     : Image.file(
//                         File(previewPath!),
//                         width: double.maxFinite,
//                         height: MediaQuery.of(context).size.height * 0.5,
//                         fit: BoxFit.cover,
//                       )));
//   }
// }
