import 'dart:io';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/screens/poster/models.dart';
import 'package:mla_connect/screens/poster/poster_util.dart';
import 'package:mla_connect/screens/poster/preview_page.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/widgets/color_pick.dart';
import 'package:flutter/material.dart';

part 'create_part.dart';

class CreatePosterPage extends StatefulWidget {
  final String posterPath;
  const CreatePosterPage({Key? key, required this.posterPath})
      : super(key: key);

  @override
  _CreatePosterPageState createState() => _CreatePosterPageState();
}

class _CreatePosterPageState extends State<CreatePosterPage> {
  final img_key = GlobalKey();

  bool hasInit = false;
  late SelfProfile profile;
  String? initError;

//Image Setting
  Offset pPos = Offset(50, 50);
  double circleSize = 150;

  Size? imgSize;
  late Offset globalOffset;
  late Rect imgRect;
  bool circlePlaceholder = true;

//Text Settings
  PosterTextAlign textAlign = PosterTextAlign.Center;
  PosterTextFont textFont = PosterTextFont.Arial_48;
  Color textColor = Colors.black;
  Color textBg = Colors.white;

  double aspectRatio = 3.0 / 4.0;

  _Control controller = _Control.IMAGE;

  bool isCreatingPoster = false;

  void pickTextColor() async {
    final res = await showDialog(
        context: context,
        builder: (context) => ColorPickerDialog(
              startColor: textColor,
            ));
    if (res is Color)
      setState(() {
        textColor = res;
      });
  }

  void pickBgColor() async {
    final res = await showDialog(
        context: context,
        builder: (context) => ColorPickerDialog(
              startColor: textBg,
            ));
    if (res is Color)
      setState(() {
        textBg = res;
      });
  }

  void setFont(PosterTextFont? font) {
    if (font == null) return;
    setState(() {
      textFont = font;
    });
  }

  void setAlign(PosterTextAlign? align) {
    if (align == null) return;
    setState(() {
      textAlign = align;
    });
  }

  void setControl(_Control control) {
    try {
      getViewportData();
      setState(() {
        aspectRatio = control == _Control.IMAGE ? 3.0 / 4.0 : 1;
        controller = control;
      });
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  void setType(bool isCircle) {
    setState(() {
      circlePlaceholder = isCircle;
    });
  }

  void setSize(double size) {
    setState(() {
      circleSize = size;
    });
  }

  ViewportData getViewportData() {
    //ImgSize may not be initialised
    try {
      final pos_abs = Offset(pPos.dx - globalOffset.dx, pPos.dy);
      return ViewportData(
          posterImagePath: widget.posterPath,
          posterSize: imgSize!,
          overlapImageType: circlePlaceholder ? "circle" : "square",
          overlapImageSize: circleSize,
          overlapPos: pos_abs,
          textAlign: textAlign,
          textBg: textBg,
          textColor: textColor,
          textFont: textFont);
    } catch (e) {
      throw Exception("Adjust placeholder first");
    }
  }

  void showPreview(String profileUrl, String profileName) async {
    try {
      final data = getViewportData();
      // context.pushWidget((context) => PosterPreview(
      //       data: data,
      //       profileUrl: profileUrl,
      //       profileName: profileName,
      //     ));
    } catch (e) {
      context.snackBar(e.toError());
    }
  }

  void createPoster() async {
    try {
      setState(() {
        isCreatingPoster = true;
      });
      final data = getViewportData();
      // final res = await PosterUtils.convertToAbsolute(data);
      // if (res != null) {
      //   Navigator.pop(context, res);
      //   // final posterUrl = await FirebaseCloud.instance
      //   //     .uploadFileToFirebaseStorage(File(widget.posterPath));
      //   // final poster = PosterNetworkModel.fromLocalData(
      //   //     data: res, posterUrl: posterUrl, id: "");
      //   // final newPoster = await FirebaseCloud.instance.createPoster(poster);
      //   // Navigator.pop(context, newPoster);
      // } else {
      //   throw Exception("Error Reading Image");
      // }
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      if (mounted) {
        setState(() {
          isCreatingPoster = false;
        });
      }
    }
  }

  void _calculateSize() {
    if (imgSize == null) {
      final box = img_key.currentContext?.findRenderObject();
      if (box != null && box is RenderBox) {
        imgSize = box.size;
        globalOffset = box.localToGlobal(Offset.zero);
        //top is 0 as stack as TopCenter alignment
        imgRect =
            Rect.fromLTWH(globalOffset.dx, 0, box.size.width, box.size.height);
      }
    }
  }

  void panUpdate(DragUpdateDetails details) {
    _calculateSize();
    final size = imgSize;
    if (size != null) {
      final radius = circleSize / 2;
      final center = Offset(details.localPosition.dx, details.localPosition.dy);
      final movRect = Rect.fromCenter(
          center: center, width: circleSize, height: circleSize);

      double px = details.localPosition.dx;
      double py = details.localPosition.dy;
      if (!imgRect.contains(movRect.centerLeft)) px = imgRect.left + radius;
      if (!imgRect.contains(movRect.centerRight)) px = imgRect.right - radius;
      if (!imgRect.contains(movRect.topCenter)) py = imgRect.top + radius;
      if (!imgRect.contains(movRect.bottomCenter)) py = imgRect.bottom - radius;

      setState(() {
        pPos = Offset(px - radius, py - radius);
      });
    }
  }

  void init() async {
    try {
      setState(() {
        hasInit = false;
        initError = null;
      });
      final dash = await FirebaseCloud.instance.getDashboardData();
      hasInit = true;
      profile = dash.profile;
    } catch (e) {
      initError = e.toError();
    } finally {
      setState(() {});
    }
  }

  Future<bool> _willPopCallback() async {
    if (controller == _Control.TEXT) {
      setControl(_Control.IMAGE);
      return false;
    }
    return true; // return true if the route to be popped
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => init());
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _willPopCallback,
      child: Scaffold(
          appBar: AppBar(
              title: Text("Create Poster"),
              centerTitle: true,
              leading: IconButton(
                onPressed: () {
                  Navigator.pop(context, true);
                },
                icon: Icon(Icons.arrow_back),
                color: Colors.black,
              )),
          body: hasInit
              ? buildBody()
              : Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Center(
                      child: initError != null
                          ? Text(initError!)
                          : CircularProgressIndicator()),
                )),
    );
  }

  Widget buildBody() {
    if (controller == _Control.IMAGE) {
      return Column(
        children: [
          AspectRatio(
              aspectRatio: aspectRatio,
              child: GestureDetector(
                onPanUpdate: (details) => panUpdate(details),
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Image.file(
                      File(widget.posterPath),
                      key: img_key,
                      fit: BoxFit.contain,
                    ),
                    Positioned(
                        left: pPos.dx, top: pPos.dy, child: buildPlaceholder()),
                    // if (isCreatingPoster) creatingPoster() //TODO -->
                  ],
                ),
              )),
          Expanded(child: buildPlaceControls())
        ],
      );
    } else {
      final _imSize = imgSize;
      return Stack(
        fit: StackFit.expand,
        children: [
          Column(
            children: [
              if (_imSize != null)
                Expanded(
                    child: Image.file(
                  File(widget.posterPath),
                  key: img_key,
                  width: _imSize.width,
                  fit: BoxFit.cover,
                )),
              buildTextPlaceholder(),
              buildTextController()
            ],
          ),
          if (isCreatingPoster) AbsorbPointer(child: CreatingLoader())
        ],
      );
    }
  }

  Widget buildPlaceholder({bool dragText = true}) {
    return Container(
      width: circleSize,
      height: circleSize,
      decoration: BoxDecoration(
          shape: circlePlaceholder ? BoxShape.circle : BoxShape.rectangle,
          color: Colors.black54),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (circleSize > 100) ...[
            Text(
              "Placeholder",
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white70, fontSize: 16),
            ),
            SizedBox(
              height: 4,
            ),
            if (dragText)
              Text(
                "Drag Me!",
                style:  TextStyle(fontFamily: 'Gilroy',color: Colors.white54, fontSize: 14),
              )
          ],
        ],
      ),
    );
  }

  Widget buildPlaceControls() {
    return Container(
      color: PRIMARY_COLOR_ORANGE.withOpacity(0.4),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Column(children: [
             
              SizedBox(
                height: 8,
              ),
              ElevatedButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    minimumSize: MaterialStateProperty.all(const Size(100, 45)),
                    backgroundColor: MaterialStateProperty.all(Colors.black),
                  ),
                  onPressed: () => setType(true),
                  child: Text("Circle")),
              ElevatedButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    minimumSize: MaterialStateProperty.all(const Size(100, 45)),
                    backgroundColor: MaterialStateProperty.all(Colors.black),
                  ),
                  onPressed: () => setType(false),
                  child: Text("Square")),
            ]),
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("Size", style:  TextStyle(fontFamily: 'Gilroy',fontSize: 20)),
                Slider(
                  thumbColor: Colors.black,
                  activeColor: Colors.black,
                  inactiveColor: Colors.black45,
                  value: circleSize,
                  onChanged: (value) => setSize(value),
                  min: 50,
                  max: 200,
                ),
                // Expanded(child: Container()),
                Align(
                    alignment: Alignment.centerRight,
                    child:
                        // Center(
                        // child:
                        ElevatedButton(
                            style: ButtonStyle(
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              minimumSize: MaterialStateProperty.all(
                                  const Size(200, 45)),
                              backgroundColor:
                                  MaterialStateProperty.all(Colors.black),
                            ),
                            onPressed: () => setControl(_Control.TEXT),
                            child: Text("Next"))),
              ],
            ))
          ],
        ),
      ),
    );
  }

  Widget buildTextPlaceholder() {
    final _imSize = imgSize;
    if (_imSize == null) return Container();

    TextStyle getTextStyle() {
      return  TextStyle(fontFamily: 'Gilroy',fontSize: textFont.fontSize, color: textColor);
    }

    return Container(
      width: _imSize.width,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      // margin: EdgeInsets.only(left: globalOffset.dx),
      alignment: textAlign.getAlignment(),
      color: textBg,
      child: Text("Name", style: getTextStyle()),
    );
  }

  Widget buildTextController() {
    final profileUrl = profile.profileUrl;

    Widget fontRow(PosterTextFont font) {
      return Row(
        children: [
          Radio<PosterTextFont>(
              value: font,
              groupValue: textFont,
              onChanged: (font) => setFont(font)),
          Expanded(
              child: GestureDetector(
                  onTap: () => setFont(font),
                  child: Text(font.fontSize.toString())))
        ],
      );
    }

    Widget alignRow(PosterTextAlign align) {
      return Row(
        children: [
          Radio<PosterTextAlign>(
              fillColor:
                  MaterialStateColor.resolveWith((states) => Colors.black),
              value: align,
              groupValue: textAlign,
              onChanged: (align) => setAlign(align)),
          Expanded(
              child: GestureDetector(
                  onTap: () => setAlign(align), child: Text(align.alignName)))
        ],
      );
    }

    return Container(
      color: PRIMARY_COLOR_ORANGE.withOpacity(0.4),
      padding: const EdgeInsets.only(top: 8),
      child: Column(
        children: [
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            // Expanded(
            //   child: Column(
            //     mainAxisSize: MainAxisSize.min,
            //     children: [
            //       Text("Size"),
            //       Divider(),
            //       fontRow(PosterTextFont.Arial_14),
            //       fontRow(PosterTextFont.Arial_24),
            //       fontRow(PosterTextFont.Arial_48),
            //     ],
            //   ),
            // ),
            Expanded(
                child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("Alignment", style:  TextStyle(fontFamily: 'Gilroy',fontSize: 20)),
                Divider(),
                alignRow(PosterTextAlign.Right),
                alignRow(PosterTextAlign.Left),
                alignRow(PosterTextAlign.Center)
              ],
            )),
            Expanded(
                child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("Colors", style:  TextStyle(fontFamily: 'Gilroy',fontSize: 20)),
                Divider(),
                Container(height: 20, width: 20, color: textColor),
                TextButton(
                    onPressed: () => pickTextColor(),
                    child: Text(
                      "Text",
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black),
                    )),
                SizedBox(
                  height: 8,
                ),
                Container(height: 20, width: 20, color: textBg),
                TextButton(
                    onPressed: () => pickBgColor(),
                    child: Text(
                      "Background",
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black),
                    ))
              ],
            ))
          ]),
          Divider(),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      minimumSize:
                          MaterialStateProperty.all(const Size(100, 45)),
                      backgroundColor: MaterialStateProperty.all(Colors.black),
                    ),
                    onPressed: () => setControl(_Control.IMAGE),
                    child: Text("Previous")),
                ElevatedButton(
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      minimumSize:
                          MaterialStateProperty.all(const Size(100, 45)),
                      backgroundColor: MaterialStateProperty.all(Colors.black),
                    ),
                    onPressed: profileUrl != null
                        ? () => showPreview(profileUrl, profile.name)
                        : null,
                    child: Text("Preview")),
                ElevatedButton(
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      minimumSize:
                          MaterialStateProperty.all(const Size(100, 45)),
                      backgroundColor: MaterialStateProperty.all(Colors.black),
                    ),
                    onPressed: () => createPoster(),
                    child: Text("Create")),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class CreatingLoader extends StatelessWidget {
  const CreatingLoader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black45,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [CircularProgressIndicator()],
        ),
      ),
    );
  }
}
