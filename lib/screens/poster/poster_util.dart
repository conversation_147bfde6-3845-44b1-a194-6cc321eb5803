import 'dart:io';
import 'dart:isolate';
import 'dart:math';
import 'dart:ui';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image/image.dart' as im;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:mla_connect/models/poster.dart';
import 'package:mla_connect/screens/poster/models.dart';

extension alignExt on PosterTextFont {
  im.BitmapFont get font {
    switch (this) {
      case PosterTextFont.Arial_14:
        return im.arial14;
      case PosterTextFont.Arial_24:
        return im.arial24;
      case PosterTextFont.Arial_48:
        return im.arial48;
      default:
        return im.arial14;
    }
  }
}

class ImageDecodeParam {
  final String posterImagePath;
  final SendPort sendPort;

  ImageDecodeParam({
    required this.posterImagePath,
    required this.sendPort,
  });
}

void imageDecodeIsolate(ImageDecodeParam param) {
  final posterFile = File(param.posterImagePath);
  final posterImg = im.decodeImage(posterFile.readAsBytesSync());
  if (posterImg != null) {
    param.sendPort.send(posterImg);
  }
  param.sendPort.send(null);
}

//--------------------------------------------
class _OverlapParam {
  final String posterImagePath;
  final String overlapImagePath;
  final String overlapImageType;
  final double overlapImageSize;
  final Offset overlapPos;

  final String posterText;
  final PosterTextAlign textAlign;
  final PosterTextFont textFont;
  final Color textColor;
  final Color textBg;

  final SendPort sendPort;

  _OverlapParam(
      {required this.posterImagePath,
      required this.overlapImagePath,
      required this.overlapImageType,
      required this.overlapImageSize,
      required this.overlapPos,
      required this.textAlign,
      required this.textFont,
      required this.textColor,
      required this.textBg,
      required this.sendPort,
      required this.posterText});
}

Tuple2<int, int> _calculateStringSize(im.BitmapFont font, String string) {
  var stringWidth = 0;
  var stringHeight = 0;
  final chars = string.codeUnits;
  for (var c in chars) {
    if (!font.characters.containsKey(c)) {
      continue;
    }
    final ch = font.characters[c]!;
    stringWidth += ch.xAdvance;
    if (ch.height + ch.yOffset > stringHeight) {
      stringHeight = ch.height + ch.yOffset;
    }
  }
  return Tuple2(stringWidth, stringHeight);
}

// int _getImColor(Color c) {
  
//   return im.getRowStride(c.red, c.green, c.blue );
// }

// void imageOverlapIsolate(_OverlapParam param) {
//   final posterImg =
//       im.decodeImage(File(param.posterImagePath).readAsBytesSync());
//   final profileImg =
//       im.decodeImage(File(param.overlapImagePath).readAsBytesSync());
//   if (posterImg != null && profileImg != null) {
//     //resize profile
//     final resizeUnit = param.overlapImageSize.toInt();
//     late im.Image resizeProfile;
//     if (profileImg.height > profileImg.width)
//       resizeProfile = im.copyResize(profileImg, height: -1, width: resizeUnit);
//     else
//       resizeProfile = im.copyResize(profileImg, height: resizeUnit, width: -1);
//     //crop profile - circle or square
//     late im.Image cropped;
//     final center = im.Point(resizeProfile.width / 2, resizeProfile.height / 2);
//     final radius = min(resizeProfile.width, resizeProfile.height) / 2;
//     if (param.overlapImageType == "circle") {
//       cropped = im.copyCropCircle(resizeProfile,
//           radius: radius.toInt(), centerX: center);
//     } else {
//       //square
//       final _x = (center.x - radius).toInt();
//       final _y = (center.y - radius).toInt();
//       cropped = im.copyCrop(resizeProfile, _x, _y, resizeUnit, resizeUnit);
//     }

//     //perform overlap with cropped image
//     final dst_x = param.overlapPos.dx.toInt();
//     final dst_y = param.overlapPos.dy.toInt();
//     final overlap_img =
//         im.drawPolygon(posterImg, cropped, dstX: dst_x, dstY: dst_y);

//     // im.Image()
//     final textSize =
//         _calculateStringSize(param.textFont.font, param.posterText);
//     final margin = 24;
//     final textImgHeight = textSize.value2 + 2 * margin;
//     final textImg = im.Image(overlap_img.width, textImgHeight);
//     final textImg_color = im.fill(textImg, _getImColor(param.textBg));
//     late im.Image textImg_text;
//     final t_y = margin;
//     final stringColor = _getImColor(param.textColor);

//     if (param.textAlign == PosterTextAlign.Left) {
//       final t_x = margin;
//       textImg_text = im.drawString(
//           textImg_color, param.textFont.font, t_x, t_y, param.posterText,
//           color: stringColor);
//     } else if (param.textAlign == PosterTextAlign.Right) {
//       final t_x = textImg_color.width - margin - textSize.value1;
//       textImg_text = im.drawString(
//           textImg_color, param.textFont.font, t_x, t_y, param.posterText,
//           color: stringColor);
//     } else {
//       //center
//       textImg_text = im.drawCircle(
//           textImg_color , param.posterText,
//           color: stringColor);
//           //  textImg_text = im.drawCircle(
//           // textImg_color, param.textFont.font, param.posterText,
//           // color: stringColor);
//     }

//     final result_frame =
//         im.Image(overlap_img.width, overlap_img.height + textImg_text.height);
//     final res_img1 = im.drawImage(result_frame, overlap_img);
//     final res_img2 =
//         im.drawImage(res_img1, textImg_text, dstX: 0, dstY: overlap_img.height);

//     param.sendPort.send(res_img2);
//   }
//   param.sendPort.send(null);
// }

//--------------------

class PosterUtils {
  PosterUtils._();

  static Future<String> getCacheImagePath(String url) async {
    final cache = DefaultCacheManager();
    final file = await cache.getSingleFile(url);
    return file.path;
  }

  static Future<PosterOverlayData?> convertToAbsolute(ViewportData data) async {
    final receivePort = ReceivePort();

    await Isolate.spawn(
        imageDecodeIsolate,
        ImageDecodeParam(
            posterImagePath: data.posterImagePath,
            sendPort: receivePort.sendPort));

    final posterImg = await receivePort.first as im.Image?;
    if (posterImg != null) {
      final double a_v_ratio = posterImg.height / data.posterSize.height;
      final double abs_overlap_size = a_v_ratio * data.overlapImageSize;
      final abs_overlap_pos = Offset(
          data.overlapPos.dx * a_v_ratio, data.overlapPos.dy * a_v_ratio);

      return PosterOverlayData(
          posterImagePath: data.posterImagePath,
          overlapImageType: data.overlapImageType,
          overlapImageSize: abs_overlap_size,
          overlapPos: abs_overlap_pos,
          textAlign: data.textAlign,
          textFont: data.textFont,
          textBg: data.textBg,
          textColor: data.textColor);
    }
    return null;
  }

  static Future<String?> overlapPoster(PosterOverlayData poster,
      String profileImgPath, String profileName) async {
    final receivePort = ReceivePort();

    // await Isolate.spawn(
        // imageOverlapIsolate,
        // _OverlapParam(
        //     posterImagePath: poster.posterImagePath,
        //     overlapImagePath: profileImgPath,
        //     overlapImageType: poster.overlapImageType,
        //     overlapImageSize: poster.overlapImageSize,
        //     overlapPos: poster.overlapPos,
        //     sendPort: receivePort.sendPort,
        //     textAlign: poster.textAlign,
        //     textFont: poster.textFont,
        //     textColor: poster.textColor,
        //     textBg: poster.textBg,
        //     posterText: profileName));

    final mergedImage = await receivePort.first as im.Image?;
    if (mergedImage != null) {
      final tempDir = await getTemporaryDirectory();
      final imageFile = File(tempDir.path + "/" + Uuid().v1() + ".jpg");
      FileImage(imageFile).evict();
      await imageFile.writeAsBytes(im.encodeJpg(mergedImage));
      return imageFile.path;
    }

    return null;
  }

  static Future<String?> overlapPosterFromModel(
      PosterNetworkModel model, String profileUrl, String profileName) async {
    final cache = _PosterCache(
        posterUrl: model.posterUrl,
        profileUrl: profileUrl,
        profileName: profileName);
    final cachedFilePath = await cache.getFilePath();
    if (cachedFilePath != null) return cachedFilePath;

    final posterPath = await getCacheImagePath(model.posterUrl);
    final profilePath = await getCacheImagePath(profileUrl);
    final overlayData = PosterOverlayData(
        posterImagePath: posterPath,
        overlapImageType: model.overlapImageType,
        overlapImageSize: model.overlapImageSize,
        overlapPos: Offset(model.overlapPos.dx, model.overlapPos.dy),
        textAlign: model.textAlign,
        textFont: model.textFont,
        textColor: model.textColor,
        textBg: model.textBg);
    final preview = await overlapPoster(overlayData, profilePath, profileName);
    await cache.saveFile(preview);
    return preview;
  }
  // resizeImagePixes() {
  //   Image? image = imim.decodeImage(posterFile.readAsBytesSync());

  //   // Resize the image to a 120x? thumbnail (maintaining the aspect ratio).
  //   Image thumbnail = copyResize(image, width: 120);

  //   // Save the thumbnail as a PNG.
  //   // new Io.File('out/thumbnail-test.png')
  //   //   ..writeAsBytesSync(encodePng(thumbnail));
  // }
}

class _PosterCache {
  final String posterUrl;
  final String profileUrl;
  final String profileName;
  _PosterCache(
      {required this.posterUrl,
      required this.profileUrl,
      required this.profileName});

  String _last(String url) {
    final split = url.split("/");
    if (split.length > 0) return split[split.length - 1];
    return url;
  }

  String _getKey() {
    return _last(posterUrl) +
        "_" +
        _last(profileUrl) +
        "_" +
        profileName.trim();
  }

  Future<String?> getFilePath() async {
    final key = _getKey();
    final pref = await SharedPreferences.getInstance();
    final path = pref.getString(key);
    if (path != null) {
      if (await File(path).exists()) {
        return path;
      } else {
        await pref.remove(key);
      }
    }
    return null;
  }

  Future saveFile(String? filePath) async {
    if (filePath == null) return;

    final key = _getKey();
    final pref = await SharedPreferences.getInstance();
    await pref.setString(key, filePath);
  }
}
