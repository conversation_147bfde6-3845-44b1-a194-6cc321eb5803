// import 'package:mla_connect/screens/poster/poster_list_widget.dart';
// import 'package:flutter/material.dart';

// class UserPosterPage extends StatefulWidget {
//   final bool appbar;
//   const UserPosterPage({Key? key, this.appbar = true}) : super(key: key);

//   @override
//   _UserPosterPageState createState() => _UserPosterPageState();
// }

// class _UserPosterPageState extends State<UserPosterPage>
//     with AutomaticKeepAliveClientMixin {
//   final refresh_key = GlobalKey<RefreshIndicatorState>();

//   @override
//   Widget build(BuildContext context) {
//     super.build(context);
//     return Scaffold(
//       appBar: widget.appbar
//           ? AppBar(
//               title: Text("Posters"),
//               centerTitle: true,
//             )
//           : null,
//       body: PosterListWidget(
//         refreshKey: refresh_key,
//         shareEnabled: true,
//       ),
//     );
//   }

//   @override
//   bool get wantKeepAlive => true;
// }
