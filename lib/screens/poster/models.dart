import 'package:mla_connect/models/poster.dart';
import 'package:flutter/material.dart';

class ViewportData {
  final String posterImagePath;
  final Size posterSize;
  final String overlapImageType; //circle or square
  final double overlapImageSize; // Relative to viewport
  final Offset overlapPos; // Relative to viewport

  final PosterTextAlign textAlign;
  final PosterTextFont textFont;
  final Color textColor;
  final Color textBg;

  ViewportData({
    required this.posterImagePath,
    required this.posterSize,
    required this.overlapImageType,
    required this.overlapImageSize,
    required this.overlapPos,
    required this.textAlign,
    required this.textFont,
    required this.textColor,
    required this.textBg,
  });
}

class PosterOverlayData {
  final String posterImagePath;
  final String overlapImageType;
  final double overlapImageSize; //absolute size
  final Offset overlapPos; // absolute position

  final PosterTextAlign textAlign;
  final PosterTextFont textFont;
  final Color textColor;
  final Color textBg;

  PosterOverlayData({
    required this.posterImagePath,
    required this.overlapImageType,
    required this.overlapImageSize,
    required this.overlapPos,
    required this.textAlign,
    required this.textFont,
    required this.textColor,
    required this.textBg,
  });
}
