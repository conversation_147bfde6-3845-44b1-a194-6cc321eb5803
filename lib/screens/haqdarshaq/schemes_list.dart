import 'package:mla_connect/screens/haqdarshaq/eligibility_form.dart';
import 'package:flutter/material.dart';

import 'package:mla_connect/services/firebase_cloud.dart';

class SchemesList extends StatefulWidget {
  const SchemesList({Key? key}) : super(key: key);

  @override
  State<SchemesList> createState() => _SchemesListState();
}

class _SchemesListState extends State<SchemesList> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          iconTheme: IconThemeData(
            color: Colors.black, //change your color here
          ),
          title: Text('Schemes'),
        ),
        body: SingleChildScrollView(
            child: Column(
          children: [
            SizedBox(height: 20),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard(),
            _schemeCard()
          ],
        )));
  }

  Widget _schemeCard() => Container(
      width: double.infinity,
      child: Card(
          margin: EdgeInsets.all(10),
          elevation: 3,
          child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "Title of scheme",
                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 22),
                  ),
                  SizedBox(height: 10),
                  Text("Description of scheme"),
                  SizedBox(height: 10),
                  ElevatedButton(
                    onPressed: () {
                      context.pushWidget((context) => EligibilityForm());
                    },
                    child: Text("Info"),
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      minimumSize:
                          MaterialStateProperty.all(const Size(300, 45)),
                      backgroundColor: MaterialStateProperty.all(Colors.black),
                    ),
                  )
                ],
              ))));
}
