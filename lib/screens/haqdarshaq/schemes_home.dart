import 'package:mla_connect/screens/haqdarshaq/eligibility_form.dart';
import 'package:mla_connect/screens/haqdarshaq/schemes_category.dart';
import 'package:mla_connect/screens/haqdarshaq/schemes_list.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class SchemesHome extends StatefulWidget {
  const SchemesHome({Key? key}) : super(key: key);

  @override
  State<SchemesHome> createState() => _SchemesHomeState();
}

class _SchemesHomeState extends State<SchemesHome> {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            title: Text('Schemes'),
            bottom: TabBar(
              unselectedLabelColor: Colors.black,
              labelColor: Colors.white,
              indicator: BoxDecoration(
                  // borderRadius: BorderRadius.circular(50),
                  color: PRIMARY_COLOR_ORANGE.withOpacity(1)),
              tabs: [
                Tab(text: 'Schemes', icon: Icon(Icons.search)),
                Tab(text: 'Your Eligibility', icon: Icon(Icons.person)),
              ],
            ),
          ),
          //   TabBar(
          //     unselectedLabelColor: Colors.orange,
          //     tabs: [
          //       Tab(
          //           child: Container(
          //         height: double.infinity,
          //         width: double.infinity,
          //         color: PRIMARY_COLOR_ORANGE,
          //         child: Center(child: Text("Schemes")),
          //       )),
          //       Tab(
          //           child: Container(
          //         height: double.infinity,
          //         width: double.infinity,
          //         color: PRIMARY_COLOR_ORANGE,
          //         child: Center(child: Text("Schemes")),
          //       )),
          //     ],
          //   ),
          // ),
          body: TabBarView(
            children: [
              // SchemesList(),
              SchemesCategory(),
              EligibilityForm(),
            ],
          ),
        ));
  }
}
