import 'package:mla_connect/screens/haqdarshaq/proof_upload.dart';
import 'package:flutter/material.dart';
import 'package:mla_connect/services/firebase_cloud.dart';

class EligibilityForm extends StatefulWidget {
  const EligibilityForm({Key? key}) : super(key: key);

  @override
  State<EligibilityForm> createState() => _EligibilityFormState();
}

class _EligibilityFormState extends State<EligibilityForm> {
  final _formKey = GlobalKey<FormState>();

  // Initialize your field variables
  String name = '';
  String gender = '';
  int age = 0;
  String residence = '';
  String caste = '';
  String state = '';
  bool differentlyAbled = false;
  bool minority = false;
  bool student = false;
  bool employed = false;
  String occupation = '';
  bool bpl = false;
  double annualIncome = 0.0;
  String category = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: <Widget>[
                // Name field
                TextFormField(
                  decoration: InputDecoration(labelText: 'Name'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter some text';
                    }
                    return null;
                  },
                  onSaved: (value) => name = value ?? '',
                ),
                // Gender field
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(labelText: 'Gender'),
                  items:
                      <String>['Male', 'Female', 'Other'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a gender';
                    }
                    return null;
                  },
                  onSaved: (value) => gender = value ?? '',
                  onChanged: (value) {},
                ),
                // Age field
                TextFormField(
                  decoration: InputDecoration(labelText: 'Age'),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your age';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                  onSaved: (value) => age = int.parse(value ?? '0'),
                ),
                // Residence field
                TextFormField(
                  decoration: InputDecoration(labelText: 'Residence'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your residence';
                    }
                    return null;
                  },
                  onSaved: (value) => residence = value ?? '',
                ),
                // Caste field
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(labelText: 'Caste'),
                  onChanged: (value) {},
                  items: <String>['Caste1', 'Caste2', 'Caste3']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select your caste';
                    }
                    return null;
                  },
                  onSaved: (value) => caste = value ?? '',
                ),
                // State field
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(labelText: 'State'),
                  onChanged: (value) {},
                  items: <String>['State1', 'State2', 'State3']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select your state';
                    }
                    return null;
                  },
                  onSaved: (value) => state = value ?? '',
                ),
                // Differently abled field
                SwitchListTile(
                  title: Text('Differently abled'),
                  value: differentlyAbled,
                  onChanged: (bool value) {
                    setState(() {
                      differentlyAbled = value;
                    });
                  },
                ),
                // Minority field
                SwitchListTile(
                  title: Text('Minority'),
                  value: minority,
                  onChanged: (bool value) {
                    setState(() {
                      minority = value;
                    });
                  },
                ),
                // Student field
                SwitchListTile(
                  title: Text('Student'),
                  value: student,
                  onChanged: (bool value) {
                    setState(() {
                      student = value;
                    });
                  },
                ),
                // Employed field
                SwitchListTile(
                  title: Text('Employed'),
                  value: employed,
                  onChanged: (bool value) {
                    setState(() {
                      employed = value;
                    });
                  },
                ),
                // Occupation field
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(labelText: 'Occupation'),
                  onChanged: (value) {},
                  items: <String>['Occupation1', 'Occupation2', 'Occupation3']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select your occupation';
                    }
                    return null;
                  },
                  onSaved: (value) => occupation = value ?? '',
                ),
                // BPL field
                SwitchListTile(
                  title: Text('Below Poverty Line'),
                  value: bpl,
                  onChanged: (bool value) {
                    setState(() {
                      bpl = value;
                    });
                  },
                ),
                // Annual Income field
                TextFormField(
                  decoration: InputDecoration(labelText: 'Annual Income'),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your annual income';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                  onSaved: (value) => annualIncome = double.parse(value ?? '0'),
                ),
                // Category field
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(labelText: 'Category'),
                  onChanged: (value) {},
                  items: <String>['Category1', 'Category2', 'Category3']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a category';
                    }
                    return null;
                  },
                  onSaved: (value) => category = value ?? '',
                ),
                // Submit button
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: ElevatedButton(
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      minimumSize:
                          MaterialStateProperty.all(const Size(300, 45)),
                      backgroundColor: MaterialStateProperty.all(Colors.black),
                    ),
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // If the form is valid, save the fields
                        _formKey.currentState!.save();

                        // Then show a snackbar with the inputs
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Processing Data...'),
                            duration: Duration(milliseconds: 500),
                          ),
                        );

                        context.pushWidget((context) => DocumentsUpload());
                      }
                    },
                    child: Text('Submit'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
