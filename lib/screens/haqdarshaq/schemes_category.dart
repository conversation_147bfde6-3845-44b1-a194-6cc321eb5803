import 'package:mla_connect/screens/haqdarshaq/schemes_list.dart';
import 'package:flutter/material.dart';
import 'package:mla_connect/services/firebase_cloud.dart';

class SchemesCategory extends StatefulWidget {
  const SchemesCategory({Key? key}) : super(key: key);

  @override
  State<SchemesCategory> createState() => _SchemesCategoryState();
}

class _SchemesCategoryState extends State<SchemesCategory> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // Expanded(
            //   child:
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildSquareButton(Icons.home, 'Cat 1'),
                _buildSquareButton(Icons.search, 'Cat 2'),
                _buildSquareButton(Icons.settings, 'Cat 3'),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildSquareButton(Icons.message, 'Cat 4'),
                _buildSquareButton(Icons.person, 'Cat 5'),
                _buildSquareButton(Icons.help, 'Cat 6'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSquareButton(IconData iconData, String text) {
    double size = MediaQuery.of(context).size.width *
        0.23; // Change this to your desired size

    return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          height: size,
          width: size,
          color: Colors.white,
          // decoration: BoxDecoration(borderRadius: BorderRadius.circular(30)),
          child: Card(
            elevation: 5.0,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(30))),
            child: ElevatedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
                backgroundColor: MaterialStateProperty.all<Color>(Colors.white),
                // backgroundColor:
                //     MaterialStateProperty.all<Color>(Colors.transparent),
                padding:
                    MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(0)),
              ),
              onPressed: () {
                context.pushWidget((context) => SchemesList());
              },
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Icon(
                    iconData,
                    size: 30.0,
                    color: Colors.black,
                  ),
                  SizedBox(height: 10),
                  Text(
                    text,
                    style:  TextStyle(fontFamily: 'Gilroy',fontSize: 14.0, color: Colors.black),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
