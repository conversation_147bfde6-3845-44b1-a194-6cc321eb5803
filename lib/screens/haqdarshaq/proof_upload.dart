import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class DocumentsUpload extends StatefulWidget {
  const DocumentsUpload({Key? key}) : super(key: key);

  @override
  State<DocumentsUpload> createState() => _DocumentsUploadState();
}

class _DocumentsUploadState extends State<DocumentsUpload> {
  late Map<String, String> _paths;

  void _openFileExplorer(String documentName) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      setState(() => _paths[documentName] = result.files.single.path ?? '');
    } else {
      // User canceled the picker
    }
  }

  @override
  void initState() {
    super.initState();
    _paths = {'Adhar Card': '', '12th Certificate': '', 'PAN Card': ''};
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        iconTheme: IconThemeData(
          color: Colors.black, //change your color here
        ),
        title: Text('Upload Documents'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          // crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ..._paths.keys.map((documentName) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () => _openFileExplorer(documentName),
                    child: Text('Upload $documentName'),
                  ),
                  if (_paths[documentName]!.isNotEmpty)
                    Text('Selected file: ${_paths[documentName]}'),
                ],
              );
            }).toList(),
            SizedBox(height: 100),
            // Adding the ElevatedButton at the end of the Column
            Center(
                child: ElevatedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                minimumSize: MaterialStateProperty.all(const Size(300, 45)),
                backgroundColor: MaterialStateProperty.all(Colors.black),
              ),
              onPressed: () {},
              child: Text('Submit'),
            )),
          ],
        ),
      ),
    );
  }
}
