import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class AddMemberForm extends StatefulWidget {
  const AddMemberForm({Key? key}) : super(key: key);

  @override
  _AddMemberFormState createState() => _AddMemberFormState();
}

class _AddMemberFormState extends State<AddMemberForm> {
  late TextEditingController designationController,
      boothNoController,
      roleIdController,
      nameController,
      ageController,
      mobileNoController;

  DashboardResponse? response;
  bool isAdmin = false;
  late bool memberAdmin;
  @override
  void initState() {
    memberAdmin = false;
    designationController = new TextEditingController(text: "");
    boothNoController = new TextEditingController(text: "");
    roleIdController = new TextEditingController(text: "");
    nameController = new TextEditingController(text: "");
    ageController = new TextEditingController(text: "");
    mobileNoController = new TextEditingController(text: "");
    getData();
    super.initState();
  }

  final _formKey = GlobalKey<FormState>();
  List<String> rolesName = [];

  String? selectionDesignation, selectionNode, roleLevel, roleId;
  var dropdownMenuOptionsRoles;
  var dropdownMenuOptionsNodes;
  Map<String, int> rolemap = {};
  Map<String, List<Node>> nodes = {};

  bool isAdding = false;

  void addData() async {
    try {
      FocusScope.of(context).unfocus();
      setState(() {
        isAdding = true;
      });
      final value = await FirebaseCloud.instance.createUser(
        name: nameController.text,
        phone: mobileNoController.text,
        roleId: roleId!,
        isAdmin: memberAdmin,
        roleLevel: rolemap[selectionDesignation],
      );
      if (value == "true") {
        openAlertBox();
      } else {
        context.snackBar(value.toString());
      }
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      setState(() {
        isAdding = false;
      });
    }
  }

  getData() async {
    try {
      final value = await FirebaseCloud.instance.getDashboardData();
      isAdmin = value.isAdmin;
      response = value;
      value.roles.forEach((element) {
        nodes[element.alias] = element.nodes;

        rolesName.add(element.alias);
        rolemap[element.alias] = element.roleLevel;
      });

      selectionDesignation = rolesName.first;
      selectionNode = nodes[selectionDesignation]![0].value;
      roleId = nodes[selectionDesignation]![0].id;

      dropdownMenuOptionsRoles = rolesName
          .map((String item) =>
              new DropdownMenuItem<String>(value: item, child: new Text(item)))
          .toList();
      dropdownMenuOptionsNodes = nodes[selectionDesignation]!
          .map((Node item) => new DropdownMenuItem<String>(
              value: item.value, child: new Text(item.value)))
          .toList();

      setState(() {
        response = value;
      });
    } catch (e) {}

    // .then((value) => response = value);
  }

  openAlertBox() {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, StateSetter setState) {
            return AlertDialog(
                title: Center(child: Text("Member Successfully added")),
                actions: [
                  ElevatedButton(
                      onPressed: () => {
                            Share.share(
                                "You can now login with phone number : ${mobileNoController.text} \n ${SHARE_URL}")
                          },
                      child: Text("Share")),
                  TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text("Dismiss"))
                ],
                contentPadding: EdgeInsets.only(top: 10.0));
            // content: Container(
            //     height: 200.0,
            //     width: 400.0,
            //     child: Center(child: Text("Member added"))));
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    if (response == null) {
      return Scaffold(body: PI1());
    } else {
      return Scaffold(
        appBar: AppBar(
          iconTheme: IconThemeData(
            color: Colors.white,
          ),
          title: Text(" Add Member"),
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 20.0,
              ),
              Padding(
                  padding: EdgeInsets.all(15),
                  child: Card(
                      child: Padding(
                          padding: EdgeInsets.all(15),
                          child: Form(
                              key: _formKey,
                              child: Column(
                                children: [
                                  DropdownButton<String>(
                                      isExpanded: true,
                                      value: selectionDesignation,
                                      items: dropdownMenuOptionsRoles,
                                      onChanged: isAdding
                                          ? null
                                          : (s) {
                                              if (s != null) {
                                                setState(() {
                                                  selectionDesignation = s;

                                                  selectionNode = nodes[
                                                          selectionDesignation]![0]
                                                      .value;

                                                  dropdownMenuOptionsNodes = nodes[
                                                          selectionDesignation]!
                                                      .map((Node item) =>
                                                          new DropdownMenuItem<
                                                                  String>(
                                                              value: item.value,
                                                              child: new Text(
                                                                  item.value)))
                                                      .toList();
                                                  roleId = nodes[
                                                          selectionDesignation]![0]
                                                      .id;
                                                });
                                              }
                                            }),
                                  DropdownButton<String>(
                                      isExpanded: true,
                                      value: selectionNode,
                                      items: dropdownMenuOptionsNodes,
                                      onChanged: isAdding
                                          ? null
                                          : (s) {
                                              nodes[selectionDesignation]!
                                                  .forEach((element) {
                                                 //print(s);
                                                if (element.value == s) {
                                                  roleId = element.id;

                                                   //print(element.id);
                                                } else {
                                                   //print("SOMETHING WENT WRONG");
                                                }
                                              });
                                               //print(roleId);
                                               //print(s);
                                              if (s != null)
                                                setState(() {
                                                  selectionNode = s;
                                                });
                                            }),
                                  TextFormField(
                                    controller: nameController,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter some text';
                                      }
                                      return null;
                                    },
                                    decoration:
                                        InputDecoration(labelText: 'Name'),
                                  ),
                                  TextFormField(
                                    controller: mobileNoController,
                                    validator: (value) {
                                      if (value == null ||
                                          value.isEmpty ||
                                          value.length != 10) {
                                        return 'Please enter valid phone number';
                                      }
                                      return null;
                                    },
                                    decoration: InputDecoration(
                                        labelText: 'Mobile Number'),
                                    keyboardType: TextInputType.number,
                                  ),
                                  isAdmin
                                      ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Checkbox(
                                                value: memberAdmin,
                                                onChanged: isAdding
                                                    ? null
                                                    : (value) {
                                                        setState(() {
                                                          memberAdmin =
                                                              value ?? false;
                                                        });
                                                      }),
                                            Text(" Admin")
                                          ],
                                        )
                                      : Container(),
                                  SizedBox(
                                    height: 30,
                                  ),
                                  if (isAdding)
                                    SizedBox(
                                      height: 24,
                                      width: 24,
                                      child: CircularProgressIndicator(),
                                    ),
                                  ElevatedButton(
                                      onPressed: isAdding
                                          ? null
                                          : () {
                                              if (_formKey.currentState!
                                                  .validate()) {
                                                addData();
                                              }
                                            },
                                      child: Text("Save"))
                                ],
                              )))))
            ],
          ),
        ),
      );
    }
  }
}
