import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/screens/cadre/add_member.dart';
import 'package:mla_connect/screens/cadre/my_team.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/profile_pic.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';

class Cadre extends StatefulWidget {
  const Cadre({Key? key}) : super(key: key);

  @override
  _CadreState createState() => _CadreState();
}

class _CadreState extends State<Cadre> {
  DashboardResponse? data;

  void fetchData() async {
    try {
      data = await FirebaseCloud.instance.getDashboardData();
      setState(() {});
    } catch (e) {}
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => fetchData());
  }

  @override
  Widget build(BuildContext context) {
    final dashboardData = data;
    if (dashboardData == null) {
      return Scaffold(
        body: PI1(),
      );
    }
    return Scaffold(
        floatingActionButton: dashboardData.canAddUser
            ? FloatingActionButton(
                backgroundColor: Colors.black,
                child: Icon(Icons.add),
                onPressed: () {
                  context.pushWidget((context) => AddMemberForm());
                },
              )
            : null,
        appBar: AppBar(
          bottom: PreferredSize(
              child: Row(
                children: [
                  Padding(
                      padding: EdgeInsets.all(25),
                      child: ProfilePicRounded(
                          url: dashboardData.profile.profileUrl, size: 80)),
                  Expanded(
                    child: Padding(
                        padding: EdgeInsets.only(left: 2),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Hi, ${dashboardData.profile.name}",
                              style:  TextStyle(fontFamily: 'Gilroy',fontSize: 18),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            if (dashboardData.profile.roleLevel != null)
                              Text(
                                dashboardData.profile.roleAlias,
                                style:  TextStyle(fontFamily: 'Gilroy',fontSize: 14),
                              )
                          ],
                        )),
                  ),
                ],
              ),
              preferredSize: Size(
                  double.infinity, MediaQuery.of(context).size.height * 0.15)),
        ),
        body: ListView(
            padding: const EdgeInsets.only(top: 50),
            shrinkWrap: true,
            children: List.generate(dashboardData.roles.length, (index) {
              return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: ElevatedButton(
                      onPressed: () {
                        context.pushWidget(
                            (context) => MyTeam(dashboardData.roles[index]));
                      },
                      child: Text(dashboardData.roles[index].alias)));
            })));
  }
}
