import 'dart:io';

import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/TeamMemberProfile.dart';
import 'package:mla_connect/models/TeamMemberSearch.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/profile_pic.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class MyTeam extends StatefulWidget {
  const MyTeam(this.role, {Key? key}) : super(key: key);
  final Role role;
  @override
  _MyTeamState createState() => _MyTeamState();
}

class _MyTeamState extends State<MyTeam> {
  ScrollController _controller = ScrollController();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  TeamMemberSearch? _last;
  late String roleNameFirst;
  bool isFetchingBottom = false;

  List<TeamMemberProfile> profiles = [];

  final Map<String, Node> nodeMap = {};

  final List<Node> filterOptions = [];
  Node? filterNode;

  @override
  void initState() {
    super.initState();
    final rr = widget.role.alias.split(" ");
    roleNameFirst = rr.isNotEmpty ? rr.first : "";

    widget.role.nodes.forEach((n) {
      nodeMap[n.id] = n;
    });
    filterOptions.addAll(widget.role.nodes);
    _controller.addListener(_scrollListener);
    Future.microtask(() => _refreshIndicatorKey.currentState?.show());
  }

  _scrollListener() {
    if (_controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && _last?.pageEnd != true) {
        _startDataFetch();
      }
    }
  }

  Future _startDataFetch({bool refresh = false}) async {
    try {
      if (refresh) {
        _last = null;
      } else {
        setState(() {
          isFetchingBottom = true;
        });
      }
      final res =
          await FirebaseCloud.instance.fetchMembers(widget.role, last: _last);
      _last = res;
      if (refresh) {
        profiles.clear();
      }
      profiles.addAll(res.profiles);
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      setState(() {
        isFetchingBottom = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    if (mounted) {
      await _startDataFetch(refresh: true);
    }
  }

  openwhatsapp(String number) async {
    var whatsappURl_android = "whatsapp://send?phone=" + number + "&text=";
    var whatappURL_ios = "https://wa.me/$number?text=${Uri.parse("hello")}";
    if (Platform.isIOS) {
      // for iOS phone only
      if (await canLaunch(whatappURL_ios)) {
        await launch(whatappURL_ios, forceSafariVC: false);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: new Text("whatsapp not installed")));
      }
    } else {
      // android , web
      if (await canLaunch(whatsappURl_android)) {
        await launch(whatsappURl_android);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: new Text("whatsapp not installed")));
      }
    }
  }

  openPhone(phoneNumber) async {
    final url = "tel://$phoneNumber";
    if (await canLaunch(url)) {
      await launch(url);
    }
  }

  Widget card(TeamMemberProfile profile) {
    final name = "$roleNameFirst : ${nodeMap[profile.roleId]?.value}";

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Stack(
          children: [
            Row(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ProfilePicRounded(
                    url: profile.profileUrl,
                    size: 50,
                  ),
                ),
                Expanded(
                    child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(profile.name,
                          ),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            name,
                            style:
                                 TextStyle(fontFamily: 'Gilroy',color: Colors.black54, fontSize: 14),
                          )
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                            icon: Icon(Icons.call_rounded),
                            onPressed: () => openPhone(profile.phoneNumber)),
                        IconButton(
                            icon: Image.asset(
                              "assets/icons/whatsapp.png",
                              height: 24,
                              width: 24,
                            ),
                            onPressed: () => openwhatsapp(profile.phoneNumber))
                      ],
                    )
                  ],
                ))
              ],
            ),
            if (profile.adminUser)
              Align(
                  alignment: Alignment.topRight,
                  child: Text(
                    "Admin",
                    style:  TextStyle(fontFamily: 'Gilroy',
                      fontSize: 11,
                      color: Colors.black45,
                    ),
                  ))
          ],
        ),
      ),
    );
  }

  Widget buildFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(color: Colors.white, boxShadow: [
        BoxShadow(
          color: Colors.black26,
          blurRadius: 2,
        )
      ]),
      child: Row(
        children: [
          Icon(Icons.filter_list),
          SizedBox(
            width: 16,
          ),
          Text(roleNameFirst),
          SizedBox(
            width: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: DropdownButton<Node>(
                value: filterNode,
                onChanged: (value) {
                  setState(() {
                    filterNode = value;
                  });
                },
                items: filterOptions.map((n) {
                  return DropdownMenuItem<Node>(value: n, child: Text(n.value));
                }).toList()),
          ),
          Expanded(child: Container()),
          if (filterNode != null)
            TextButton(
                onPressed: () {
                  setState(() {
                    filterNode = null;
                  });
                },
                child: Text("Clear"))
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final fl = filterNode;
    final tmp = fl == null
        ? profiles
        : profiles.where((p) => p.roleId == fl.id).toList();
    int itemCount = tmp.length + (isFetchingBottom ? 1 : 0);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(widget.role.alias),
        centerTitle: true,
      ),
      body: Column(
        children: [
          buildFilter(),
          Expanded(
            child: RefreshIndicator(
              key: _refreshIndicatorKey,
              onRefresh: _onRefresh,
              child: ListView.builder(
                  controller: _controller,
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    if (isFetchingBottom && index == itemCount - 1) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    } else {
                      return card(tmp[index]);
                    }
                  }),
            ),
          ),
        ],
      ),
    );
  }
}
