// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voter_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$SearchStateCWProxy {
  SearchState field(SearchField field);

  SearchState filter(SearchFilter? filter);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchState(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchState call({
    SearchField? field,
    SearchFilter? filter,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSearchState.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfSearchState.copyWith.fieldName(...)`
class _$SearchStateCWProxyImpl implements _$SearchStateCWProxy {
  const _$SearchStateCWProxyImpl(this._value);

  final SearchState _value;

  @override
  SearchState field(SearchField field) => this(field: field);

  @override
  SearchState filter(SearchFilter? filter) => this(filter: filter);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchState(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchState call({
    Object? field = const $CopyWithPlaceholder(),
    Object? filter = const $CopyWithPlaceholder(),
  }) {
    return SearchState(
      field: field == const $CopyWithPlaceholder() || field == null
          // ignore: unnecessary_non_null_assertion
          ? _value.field!
          // ignore: cast_nullable_to_non_nullable
          : field as SearchField,
      filter: filter == const $CopyWithPlaceholder()
          ? _value.filter
          // ignore: cast_nullable_to_non_nullable
          : filter as SearchFilter?,
    );
  }
}

extension $SearchStateCopyWith on SearchState {
  /// Returns a callable class that can be used as follows: `instanceOfSearchState.copyWith(...)` or like so:`instanceOfSearchState.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$SearchStateCWProxy get copyWith => _$SearchStateCWProxyImpl(this);
}

abstract class _$SearchFieldCWProxy {
  SearchField name(bool name);

  SearchField booth(bool booth);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchField(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchField(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchField call({
    bool? name,
    bool? booth,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSearchField.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfSearchField.copyWith.fieldName(...)`
class _$SearchFieldCWProxyImpl implements _$SearchFieldCWProxy {
  const _$SearchFieldCWProxyImpl(this._value);

  final SearchField _value;

  @override
  SearchField name(bool name) => this(name: name);

  @override
  SearchField booth(bool booth) => this(booth: booth);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchField(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchField(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchField call({
    Object? name = const $CopyWithPlaceholder(),
    Object? booth = const $CopyWithPlaceholder(),
  }) {
    return SearchField(
      name: name == const $CopyWithPlaceholder() || name == null
          // ignore: unnecessary_non_null_assertion
          ? _value.name!
          // ignore: cast_nullable_to_non_nullable
          : name as bool,
      booth: booth == const $CopyWithPlaceholder() || booth == null
          // ignore: unnecessary_non_null_assertion
          ? _value.booth!
          // ignore: cast_nullable_to_non_nullable
          : booth as bool,
    );
  }
}

extension $SearchFieldCopyWith on SearchField {
  /// Returns a callable class that can be used as follows: `instanceOfSearchField.copyWith(...)` or like so:`instanceOfSearchField.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$SearchFieldCWProxy get copyWith => _$SearchFieldCWProxyImpl(this);
}

abstract class _$SearchFilterCWProxy {
  SearchFilter gender(SearchGender? gender);

  SearchFilter part_no(int? part_no);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchFilter(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchFilter(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchFilter call({
    SearchGender? gender,
    int? part_no,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSearchFilter.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfSearchFilter.copyWith.fieldName(...)`
class _$SearchFilterCWProxyImpl implements _$SearchFilterCWProxy {
  const _$SearchFilterCWProxyImpl(this._value);

  final SearchFilter _value;

  @override
  SearchFilter gender(SearchGender? gender) => this(gender: gender);

  @override
  SearchFilter part_no(int? part_no) => this(part_no: part_no);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SearchFilter(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchFilter(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchFilter call({
    Object? gender = const $CopyWithPlaceholder(),
    Object? part_no = const $CopyWithPlaceholder(),
  }) {
    return SearchFilter(
      gender: gender == const $CopyWithPlaceholder()
          ? _value.gender
          // ignore: cast_nullable_to_non_nullable
          : gender as SearchGender?,
      part_no: part_no == const $CopyWithPlaceholder()
          ? _value.part_no
          // ignore: cast_nullable_to_non_nullable
          : part_no as int?,
    );
  }
}

extension $SearchFilterCopyWith on SearchFilter {
  /// Returns a callable class that can be used as follows: `instanceOfSearchFilter.copyWith(...)` or like so:`instanceOfSearchFilter.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$SearchFilterCWProxy get copyWith => _$SearchFilterCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)` or `SearchFilter(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SearchFilter(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  SearchFilter copyWithNull({
    bool gender = false,
    bool part_no = false,
  }) {
    return SearchFilter(
      gender: gender == true ? null : this.gender,
      part_no: part_no == true ? null : this.part_no,
    );
  }
}
