import 'dart:io';

import 'package:flutter_social_share_plugin/file_type.dart';
import 'package:flutter_social_share_plugin/flutter_social_share.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/constants.dart';
import 'package:mla_connect/utils/voter_slip.dart';
import 'package:mla_connect/widgets/pdf_view.dart';
import 'package:flutter/material.dart';
 
import 'package:share_plus/share_plus.dart';

class VoterSlipPage extends StatefulWidget {
  // final VoterProfile voter;
Map<String ,dynamic> voterDetails ;
    VoterSlipPage({Key? key, 
  required this.voterDetails
  }) : super(key: key);

   static void lauchSheet(BuildContext context, Map<String, dynamic> voter) async {
    showModalBottomSheet(
        context: context,
        isDismissible: false,
        enableDrag: false,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        builder: (context) {
          return FractionallySizedBox(
              heightFactor: 0.5, child: VoterSlipPage(voterDetails: voter, ));
        });
  }

  @override
  _VoterSlipPageState createState() => _VoterSlipPageState();
}

class _VoterSlipPageState extends State<VoterSlipPage> {
  bool _generating = true;
  String? error;
  File? pdfFile;

  void _generate() async {
    try {
      setState(() {
        _generating = true;
        error = null;
      });
      // pdfFile = await VoterSlipUtil.generate(widget.voterDetails);
    } catch (e) {
      error = e.toError();
    } finally {
      if (mounted)
        setState(() {
          _generating = false;
        });
    }
  }

  void viewFile(File file) {
    context.pushWidget((context) => PdfFileViewer(file));
  }

  // void sharePdfFile(File file) {
  //   Share.shareXFiles([file.path], mimeTypes: ["application/pdf"]);
  // }

  void shareWhatsapp(File file) {
    FlutterSocialShare().shareToWhatsApp(
        msg: "Voter Slip \n ${SHARE_URL}",
        imagePath: file.path,
        fileType: FileType.image);
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => _generate());
  }

  @override
  Widget build(BuildContext context) {
    final pdf = pdfFile;

    return Container(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  "Voter Slip",
                 
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text("Close")),
                )
              ],
            ),
          ),
          if (_generating)
            Container(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (pdf == null)
            Container(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ElevatedButton(
                        onPressed: () => _generate(), child: Text("Retry")),
                    SizedBox(
                      height: 8,
                    ),
                    Text(
                      error ?? "",
                      style:  TextStyle(fontFamily: 'Gilroy',color: Colors.red),
                    )
                  ],
                ),
              ),
            )
          else
            Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                          onPressed: () => viewFile(pdf),
                          iconSize: 40,
                          color: Colors.redAccent,
                          icon: Icon(Icons.picture_as_pdf)),
                      SizedBox(
                        height: 4,
                      ),
                      Text("View")
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                          iconSize: 40,
                          color: Colors.green,
                          onPressed: () {
                            // => sharePdfFile(pdf)
                            },
                          icon: Icon(Icons.share)),
                      SizedBox(
                        height: 4,
                      ),
                      Text("Share")
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                          onPressed: () => shareWhatsapp(pdf),
                          icon: Image.asset(
                            "assets/icons/whatsapp.png",
                            height: 30,
                            width: 30,
                          )),
                      SizedBox(
                        height: 4,
                      ),
                      Text("Whatsapp")
                    ],
                  ),
                ],
              ),
            )
        ],
      ),
    );
  }
}
