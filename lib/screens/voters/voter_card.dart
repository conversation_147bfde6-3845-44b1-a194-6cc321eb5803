import 'package:mla_connect/models/Canditate.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/screens/voters/voter_details.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:flutter/material.dart';

class VoterCard extends StatelessWidget {
  // final Candidate profile;
  Map<String ,dynamic> voterDetails ;
 
    VoterCard({Key? key,
   required this.voterDetails  
   }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    
     //print(" voterdetails ${voterDetails}");
bool isCompleted = false; 
   
         List  metaTag=  voterDetails["metaTags"]??["ii"]; // Declaring metaTags as nullable
  
             

  // Check if metaTags is not null and contains "BENEFICIARY"
  if (metaTag != null && metaTag.contains("COMPLETED")) {
     
      isCompleted = true;
    
     // Set isTrue to true if "BENEFICIARY" is present
  }
    return GestureDetector(
      onTap: () {  
        
          context.pushWidget((context) => VoterDetails(voterDetails: voterDetails, ));},
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 18 ,vertical: 5),
        padding: EdgeInsets.all(10),
         decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.0),
                      border: Border.all(color:Color(0xffD7D9E4)),
                    ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Text(
                    voterDetails['name']+" "+voterDetails['lastName'],
                    style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontSize: 16 , fontWeight: FontWeight.bold),
                      
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Text(
                          "Gender:  "   ,
                          style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontSize: 14 , fontWeight: FontWeight.bold),
                        ),
                        Text(
                           voterDetails['gender'] ,
                          style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontSize: 14 , fontWeight: FontWeight.bold , color: Color(0xff666666)),
                        ),
                        SizedBox(
                          width: 30,
                        ),
                        Text("Age:  " ,                          style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontSize: 14 , fontWeight: FontWeight.bold  ),
  ),
                         Text( voterDetails['age'].toString(),                          style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontSize: 14 , fontWeight: FontWeight.bold , color: Color(0xff666666)),
  )
                      ],
                    ),
                  ),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(vertical: 4),
                  //   child: Text(
                  //     profile.voting_pref?.text ?? '',
                  //     style: tm.bodyText2?.copyWith(color: Colors.black38),
                  //   ),
                  // ),
                ],
              ),
            ),
           isCompleted == true ? Text( "Approved" ,style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.green ,fontSize: 16 ,fontWeight: FontWeight.bold), ) :
            Text( "Pending ", style:  TextStyle(fontFamily: 'Gilroy-Bold',color: Colors.red ,fontSize: 16 ,fontWeight: FontWeight.bold), ) 
          ],
        ),
      ),
    );
  }
}
