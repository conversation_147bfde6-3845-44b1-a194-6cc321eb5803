import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/models/Canditate.dart';
import 'package:mla_connect/models/Dashboard.dart';
import 'package:mla_connect/models/TeamMemberProfile.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/new_ui/onboarding/customtextfield.dart';
import 'package:mla_connect/screens/voters/voter_edit.dart';
import 'package:mla_connect/screens/voters/voter_slip.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:mla_connect/widgets/custom_textfield.dart';

class VoterDetails extends StatefulWidget {
  // final Candidate? profile;
  // final String? epicNo;
  Map<String ,dynamic> voterDetails ;
    VoterDetails({Key? key, 
  required this.voterDetails
  // this.profile, this.epicNo
  }) : super(key: key);

  @override
  _VoterDetailsState createState() => _VoterDetailsState();
}

class _VoterDetailsState extends State<VoterDetails> {
  VoterFetchResult? result;
  bool fetching = false;
  DashboardResponse? res;

  // void fetchData() async {
  //   try {
  //     setState(() {
  //       fetching = true;
  //     });
  //     res = await FirebaseCloud.instance.getDashboardData();
  //     if (widget.profile != null) {
  //       result = await FirebaseCloud.instance
  //           .getVoterDetailsById(widget.profile!.id);
  //     } else if (widget.epicNo != null) {
  //       result =
  //           await FirebaseCloud.instance.getVoterDetailsByEpic(widget.epicNo!);
  //     } else {
  //       context.snackBar("Voter Details or EPIC not found");
  //     }
  //   } catch (e) {
  //     context.snackBar(e.toError());
  //   } finally {
  //     setState(() {
  //       fetching = false;
  //     });
  //   }
  // }

  @override
  void initState() {
    super.initState();
    // Future.microtask(() => fetchData());
  }

  Widget gap() => SizedBox(
        height: 24,
      );

  void startEditing(  ) async {
    // final edit = await
    context.pushWidget((context) => VoterEdit(
         voter: widget.voterDetails, 
        ));
    // if (edit is VoterProfile) {
    //   setState(() {
    //     this.result = result.copyWith(profile: edit);
    //   });
    // }
  }

  Widget buildFull(  ) {
     List  metaTag= widget.voterDetails["metaTags"]??["ii"]; 
      //print(metaTag);// Declaring metaTags as nullable
  bool iNFLUENCER = false; 
    bool beneficiary  = false;
        bool  lastVoted  = false;
         bool  dead  = false;
          bool  currently_present  = false;
          bool  responsibility  = false;
          bool  last_voted_selected  = false;
             

  // Check if metaTags is not null and contains "BENEFICIARY"
  if (metaTag != null && metaTag.contains("BENEFICIARY")) {
    beneficiary = true; // Set isTrue to true if "BENEFICIARY" is present
  }
  if (metaTag != null && metaTag.contains("INFLUENCER")) {
    iNFLUENCER = true; // Set isTrue to true if "BENEFICIARY" is present
  }
if (metaTag != null && metaTag.contains("LAST_VOTED")) {
    lastVoted = true; // Set isTrue to true if "BENEFICIARY" is present
  }

  if (metaTag != null && metaTag.contains("DEAD")) {
    dead = true; // Set isTrue to true if "BENEFICIARY" is present
  }
  if (metaTag != null && metaTag.contains("PRESENT")) {
    currently_present = true; // Set isTrue to true if "BENEFICIARY" is present
  }
  if (metaTag != null && metaTag.contains("RESPONSIBLE")) {
    responsibility = true; // Set isTrue to true if "BENEFICIARY" is present
  }
//  List  metaTags = widget.voterDetails["metaTags"] ? widget.voterDetails["metaTags"] :[];
//     bool iNFLUENCER =metaTags.contains('INFLUENCER') ? metaTags.contains('INFLUENCER') :false;
//      //print(" here infulaserr $iNFLUENCER");
//     bool beneficiary = metaTags.contains('BENEFICIARY')?metaTags.contains('BENEFICIARY'):false;
//     bool  lastVoted = metaTags.contains('LAST_VOTED')? metaTags.contains('LAST_VOTED') :false;
//       bool  dead = metaTags.contains('DEAD')?metaTags.contains('DEAD') :false;
//          bool  currently_present = metaTags.contains('PRESENT')? metaTags.contains('PRESENT'):false;
    
    // final profile = result.profile;
    // final tm = profile.resp_member;
     final TextEditingController NAMEcontroller = TextEditingController(text:widget.voterDetails["epicNumber"]  );
       final TextEditingController FIRSTNAME = TextEditingController(text:widget.voterDetails["name"] );
         final TextEditingController lastname = TextEditingController(text:widget.voterDetails["lastName"] );
           final TextEditingController age = TextEditingController(text: widget.voterDetails["age"].toString()  );
             final TextEditingController gender = TextEditingController(text : widget.voterDetails["gender"]  );
               final TextEditingController phone = TextEditingController(text:widget.voterDetails["number"]  );
                 final TextEditingController relative = TextEditingController(text:widget.voterDetails["relativeName"]  );
                   final TextEditingController relativetype = TextEditingController(text:widget.voterDetails["epicNumber"]  );
                     final TextEditingController address = TextEditingController(text:widget.voterDetails["address"]  );
                       final TextEditingController house = TextEditingController(text:widget.voterDetails["houseNumber"]  );
                         final TextEditingController boothno = TextEditingController(text:widget.voterDetails["boothNumber"].toString()  );
                           final TextEditingController booth = TextEditingController(text:widget.voterDetails["booth"].toString()  );
                             final TextEditingController village = TextEditingController(text:widget.voterDetails["village"] ??'-' );
                               final TextEditingController familycount = TextEditingController(text: widget.voterDetails["familyCount"].toString() );
                                 final TextEditingController beneficiarycon = TextEditingController(text:beneficiary == true && beneficiary!=null? 'Yes' : 'No'  );
                                   final TextEditingController votingprefrence = TextEditingController(text:widget.voterDetails["votingFor"]?? '-'  );
                                     final TextEditingController votedlast = TextEditingController(text:lastVoted == true && lastVoted != null ? 'Yes' : 'No' );
                                     final TextEditingController currentlypresent = TextEditingController(text:currently_present  == true&& currently_present !=null ? 'Yes' : 'No'  );
                                     final TextEditingController responsiblefor = TextEditingController(text: responsibility  == true&& last_voted_selected !=null ? 'Yes' : 'No' );
    return Scaffold( 
      
        appBar: AppBar(
           backgroundColor: Color(0xff4C3AB4),
          title: Text("Voter Details"),
          iconTheme: IconThemeData(
            color: PRIMARY_COLOR_ORANGE, //change your color here
          ),
          // elevation: 0,
          
          leading:IconButton(onPressed: ( ){ }, icon:  Icon(Icons.arrow_back)),
          centerTitle: true,
          actions: [
            // IconButton(
            //     onPressed: 
            //    () => VoterSlipPage.lauchSheet(context, widget.voterDetails ),
            //     icon: Icon(
            //       Icons.document_scanner,
            //       color: Colors.white,
            //     )),
            IconButton(
                onPressed: 
                  () => startEditing( ),
                icon: Icon(
                  Icons.edit,
                  color: Colors.white,
                ))
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Card(
                elevation: 1,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Column(
                    children: [
                      Icon(
                        Icons.account_circle,
                        size: 90,
                        color: Colors.black38,
                      ),
                      gap(),
                       READCustomTextFormField(label: "EPIC NUMBER", hint: '', controller: NAMEcontroller,),
                      // _Ele('Epic No.',widget.voterDetails["epicNumber"]),
                      gap(),
                       READCustomTextFormField(label: "FIRST NAME", hint: '', controller: FIRSTNAME,),
                      // _Ele('Name', widget.voterDetails["name"]),
                      gap(),
                       READCustomTextFormField(label: "LAST NAME", hint: '', controller: lastname,),
                      // _Ele('Last Name',  widget.voterDetails["lastName"]),
                      gap(),
                       READCustomTextFormField(label: "AGE", hint: '', controller: age,),
                      // _Ele('Age',  widget.voterDetails["age"].toString() ),
                      gap(),
                       READCustomTextFormField(label: "GENDER", hint: '', controller: gender,),
                      // _Ele('Gender', widget.voterDetails["gender"]  ),
                      gap(),
                       READCustomTextFormField(label: "MOBLIE NO.", hint: '', controller: NAMEcontroller,),
                      // _Ele('Mobile No.', widget.voterDetails["number"].toString()?? "null"),
                      gap(),
                       READCustomTextFormField(label: "RELATIVE NAME", hint: '', controller: relative,),
                      // _Ele('Relative Name',
                      //       widget.voterDetails["relativeName"]),
                      gap(),
                       READCustomTextFormField(label: "RELATIVE TYPE", hint: '', controller: relativetype,),
                      // _Ele("Relative Type",  widget.voterDetails["relativeType"]),
                      gap(),
                       READCustomTextFormField(label: "ADDRESS", hint: '', controller: address,),
                      // _Ele('Address',widget.voterDetails["address"]),
                      gap(),
                       READCustomTextFormField(label: "HOUSE NO.", hint: '', controller: house,),
                      // _Ele('House No.', widget.voterDetails["houseNumber"].toString()),
                      gap(),
                       READCustomTextFormField(label: "BOOTH NO.", hint: '', controller: boothno,),
                      // _Ele('Booth No.', widget.voterDetails["boothNumber"].toString()),
                      gap(),
                       READCustomTextFormField(label: "BOOTH", hint: '', controller: booth,),
                      // _Ele('Booth', widget.voterDetails["booth"]),
                      gap(),
                       READCustomTextFormField(label: "VILLAGE", hint: '', controller: village,),
                      // _Ele('Village',  widget.voterDetails["village"]??" "),
                      gap(),
                       READCustomTextFormField(label: "FAMILY COUNT", hint: '', controller: familycount,),
                      // _Ele('Family Count',
                      //     widget.voterDetails["familyCount"].toString() ?? ''),
                      gap(),
                       READCustomTextFormField(label: "FAMILY EPIC NO.", hint: '', controller: NAMEcontroller,),
                      // Container(
                      //   child: Row(
                      //     children: [
                      //       Expanded(
                      //         child: Text('Family Epic No.',
                      //             style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black54)),
                      //         flex: 1,
                      //       ),
                             
                      //     ],
                      //   ),
                      // ),
                      gap(),
                     
                      // _Ele('Influencer',
                      //     iNFLUENCER== true && iNFLUENCER!=null ? 'Yes' : 'No'),
                      // gap(),
                       READCustomTextFormField(label: "BENEFICIARY", hint: '', controller: beneficiarycon,),
                      // _Ele('Beneficiary',
                      //    beneficiary == true && beneficiary!=null? 'Yes' : 'No'),
                      // gap(),
                      // _Ele('Beneficiary Type',widget.voterDetails["beneficiaryType"]??''),
                      // gap(),
                      // _Ele('Dead',dead && dead!=null ? 'Yes' : 'No'),
                      gap(),
                       READCustomTextFormField(label: "VOTING PREFERNCE", hint: '', controller: votingprefrence ,),
                      // _Ele(
                      //     'Voting Preference',widget.voterDetails["votingFor"]?? ''),
                      // gap(),
                      // _Ele('Caste', widget.voterDetails["caste"] ?? ''),
                      gap(),
                       READCustomTextFormField(label: "VOTED LAST", hint: '', controller: votedlast,),
                      // _Ele('Voted Last',
                      //    lastVoted == true && lastVoted != null ? 'Yes' : 'No'),
                      gap(),
                       READCustomTextFormField(label: "CURRENTLY PRESENT", hint: '', controller: currentlypresent,),
                      // _Ele('Currently Present',
                      //    currently_present  == true&& currently_present !=null ? 'Yes' : 'No'),
                         gap(),
                          READCustomTextFormField(label: "TAKE RESPONSIBILITY", hint: '', controller: responsiblefor,),
                      // _Ele('take responsibility of this voter',
                      //    last_voted_selected  == true&& last_voted_selected !=null ? 'Yes' : 'No'),
                    ],
                  ),
                ),
              ),
               
                // Card(
                //   elevation: 1,
                //   child: Container(
                //     child: Row(
                //       children: [
                //         "tm.profileUrl "!= null
                //             ? ClipOval(
                //                 child: CachedNetworkImage(
                //                     imageUrl:" tm.profileUrl"!,
                //                     height: 60,
                //                     width: 60,
                //                     fit: BoxFit.cover))
                //             : ClipOval(
                //                 child: Icon(Icons.account_circle_outlined,
                //                     size: 60)),
                //         Expanded(
                //           child: Column(
                //             crossAxisAlignment: CrossAxisAlignment.start,
                //             children: [Text("tm.name"), Text("tm.roleAlias")],
                //           ),
                //         )
                //       ],
                //     ),
                //   ),
                // )
            ],
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    // final res = result;
    // if (res != null) 
   return buildFull(  );
    // final profile = widget.profile;

    // return Scaffold(
    //   appBar: AppBar(
    //     title: Text("Voter Details"),
    //     centerTitle: true,
    //   ),
    //   body: SingleChildScrollView(
    //     child: Card(
    //       elevation: 1,
    //       child: Container(
    //         padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    //         child: Column(
    //           children: [
    //             Icon(
    //               Icons.account_circle,
    //               size: 90,
    //               color: Colors.black38,
    //             ),
    //             // if (profile != null) ...[
                  // gap(),
    //               _Ele('Name', widget.voterDetails["name"]  ?? ""),
                  // gap(),
    //               _Ele('Age',  widget.voterDetails["age"].toString()),
                  // gap(),
    //               _Ele('Gender', widget.voterDetails["gender"] ?? ""),
                  // gap(),
    //               _Ele('Booth No', widget.voterDetails["boothNumber"].toString()),
    //             // ],
    //             if (fetching)
    //               Padding(
    //                 padding: const EdgeInsets.only(top: 60),
    //                 child: CircularProgressIndicator(),
    //               )
    //           ],
    //         ),
    //       ),
    //     ),
    //   ),
    // );
  }
}

class _Ele extends StatelessWidget {
  final String f;
  final String s;
  const _Ele(this.f, this.s, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          Expanded(
            child: Text(
              f,
              style:  TextStyle(fontFamily: 'Gilroy',color: Colors.black54),
            ),
            flex: 1,
          ),
          Expanded(
            child: Text(s),
            flex: 2,
          )
        ],
      ),
    );
  }
}
