import 'package:intl/intl.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/models/TeamMemberProfile.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/new_ui/home/<USER>';
import 'package:mla_connect/new_ui/karyakarta/KaryakartaHome.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/colorConstants.dart';
import 'package:mla_connect/widgets/custom_textfield.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VoterEdit extends StatefulWidget {
  Map<String, dynamic> voter;
  VoterEdit({Key? key, required this.voter}) : super(key: key);

  @override
  State<VoterEdit> createState() => _VoterEditState();
}

class _VoterEditState extends State<VoterEdit> {
  LoginService loginService = LoginService();
  final _fbForm = GlobalKey<FormState>();

  TextEditingController family_tc = TextEditingController();
  bool? influencer = false;
  bool? beneficiary = false;
  TextEditingController beneficiary_type = TextEditingController();
  List<String>? epic_list;
  bool dead = false;
  VotingPref? voting_pref;
  TeamMemberProfile? member;
  CastePref? caste;
  bool voted_last = false;
  bool currently_present = false;
  bool responsibility = false;
  List<VotingPref> pref_list = [];
  List<CastePref> castes = [];

  bool isLoading = false;

  bool moreExpanded = false;
  bool saved = false;
  LoginService login = LoginService();
  var config;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    fetchHomeData();
  }

  List<dynamic> listOfDynamic = ['bjo', 'congress'];

  List<dynamic> partyName = [];
  String selectedValue = 'Select Value';
  String selectedValueLastVoted = '';
  var number;
  fetchHomeData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    number = prefs.getString('number');

    config = await login.fetchConfig(context);
    partyName = config['partyList'];
    partyName.add("Select Option");
     //print(config['partyList']);
    setState(() {
      selectedValue = partyName.isNotEmpty ? partyName.last.toString() : 'bjp';
      selectedValueLastVoted =
          partyName.isNotEmpty ? partyName.last.toString() : 'bjp';
    });
     //print(selectedValue);
  }

  TextEditingController phone_tc = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final TextEditingController epic =
        TextEditingController(text: widget.voter['epicNumber']);
     //print(partyName);
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Color(0xff4C3AB4),
          iconTheme: IconThemeData(
            color: Colors.white, //change your color here
          ),
          elevation: 0,
          title: Text("Edit Details"),
          centerTitle: true,
          //           actions: [
          //             IconButton(
          //                 onPressed:() async {

          //                var res =    await loginService.voterEditionalDataPost(context,selectedValue ,int.parse(family_tc.text) ,influencer!,beneficiary!,beneficiary_type.text,phone_tc.text ,dead,voted_last ,currently_present ,widget.voter['epicNumber'] ,responsibility,selectedValueLastVoted) ;
          //                  //print(res) ;
          //                if(res['status']== 200){
          //                 Navigator.push(context, MaterialPageRoute(builder:  (context) {
          //                   return KaryakartaHome();
          //                 },));
          //                }else if (res["status"] == 403){
          // showDialog(
          //               context: context,
          //               builder: (BuildContext context) {
          //                 return SessionExpiredDialog();
          //               },
          //             );
          //                       }
          //                else{
          //                 context.snackBar("Something went wrong!");
          //                }

          //                 },
          //                 //  isLoading ? null : () => saveDetails(),
          //                 icon: Icon(
          //                   Icons.check,
          //                   color: Colors.white,
          //                 ))
          //           ],
        ),
        body: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _fbForm,
              child: Column(
                children: [
                  if (isLoading)
                    Container(
                      padding: const EdgeInsets.all(24),
                      child: CircularProgressIndicator(),
                    ),
                  READCustomTextFormField(
                    label: "EPIC NUMBER",
                    hint: '',
                    controller: epic,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  CustomTextFormField(
                    hint: 'e.g. 8264849728',
                    label: 'ENTER MOBILE NO.',
                    h: 0,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a mobile no.';
                      }
                      if (value.length != 10) {
                        return 'Enter a 10 digit number';
                      }
                      // Validate if the input is a valid number using a regular expression
                      if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                        return 'Please enter a valid number.';
                      }
                      return null;
                    },
                    controller: phone_tc,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Stack(
                    children: [
                      Container(
                        // margin: EdgeInsets.only(left: 20 ,right: 20 , top: 20),
                        width: MediaQuery.of(context).size.width,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(top: 10),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Color.fromARGB(
                                255, 228, 225, 225), // Border color
                            width: 1.0, // Border width
                          ),
                          borderRadius: BorderRadius.circular(
                              5), // Optional: border radius
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 10,
                            ),
                            SizedBox(
                                width: MediaQuery.of(context).size.width * 0.81,
                                child: DropdownButton<String>(
                                  isExpanded: true,
                                  value: selectedValue,
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      selectedValue = newValue!;
                                    });
                                  },
                                  items: partyName
                                      .map<DropdownMenuItem<String>>((value) {
                                    return DropdownMenuItem<String>(
                                      value: value.toString(),
                                      child: Text(value.toString()),
                                    );
                                  }).toList(),
                                )),
                          ],
                        ),
                      ),
                      Positioned(
                        left: MediaQuery.of(context).size.height * 0.01,
                        top: MediaQuery.of(context).size.height * -0,
                        child: Container(
                          padding: EdgeInsets.only(left: 5, right: 5),
                          color: Colors.white,
                          child: Text(
                            "VOTING PREFRENCE",
                            style: TextStyle(
                                fontFamily: 'Gilroy-Bold',
                                fontWeight: FontWeight.bold,
                                fontSize: 14),
                          ),
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  //                     Row(
                  //                       children: [
                  //                         Expanded(
                  //                           child: Text("Voting Preference"),
                  //                           flex: 1,
                  //                         ),
                  //                         Expanded(
                  //                             flex: 2,
                  //                             child: Padding(
                  //                               padding:
                  //                                   const EdgeInsets.symmetric(horizontal: 8.0),
                  //                               child:
                  // DropdownButton<String>(
                  //   isExpanded: true,
                  //   value: selectedValue,
                  //   onChanged: (String? newValue) {
                  //     setState(() {
                  //       selectedValue = newValue!;
                  //     });
                  //   },
                  //   items: partyName.map<DropdownMenuItem<String>>((value) {
                  //     return DropdownMenuItem<String>(
                  //       value: value.toString(),
                  //       child: Text(value.toString()),
                  //     );
                  //   }).toList(),
                  // )
                  //                             ))
                  //                       ],
                  //                     ),
                  if (castes.isNotEmpty)
                    Row(
                      children: [
                        Expanded(
                          child: Text("Caste"),
                          flex: 1,
                        ),
                        Expanded(
                            flex: 2,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8.0),
                              child: DropdownButton<String>(
                                  value: caste?.id,
                                  isExpanded: true,
                                  onChanged: (value) {
                                    setState(() {
                                      caste = castes
                                          .firstWhere((e) => e.id == value);
                                    });
                                  },
                                  items: castes.map((pref) {
                                    return DropdownMenuItem<String>(
                                        value: pref.id, child: Text(pref.text));
                                  }).toList()),
                            ))
                      ],
                    ),
                  SizedBox(
                    height: 10,
                  ),
                  // if (moreExpanded)
                  CustomTextFormField(
                    hint: '5',
                    label: 'FAMILY COUNT',
                    h: 0,
                    validator: (value) {
                      if (value == null || value.isEmpty    ) {
                        return 'Please enter a number.';
                      }
                      if (   double.parse(value) <= 0  ) {
                        return 'Please enter a number.';
                      }

                      return null;
                    },
                    controller: family_tc,
                  ),
                  //           Row(
                  //   children: [
                  //     Expanded(
                  //       child: Text("Family Count"),
                  //       flex: 1,
                  //     ),
                  //     Expanded(
                  //       child: TextField(
                  //         controller: family_tc,
                  //         decoration: InputDecoration(
                  //           hintText: '',
                  //           counterText: "",
                  //         ),
                  //       ),
                  //       flex: 2,
                  //     )
                  //   ],
                  // ),
                  // Row(
                  //   crossAxisAlignment: CrossAxisAlignment.start,
                  //   children: [
                  //     Expanded(
                  //       child: Padding(
                  //         padding: const EdgeInsets.only(top: 16.0),
                  //         child: Text("Family Epic List"),
                  //       ),
                  //       flex: 1,
                  //     ),
                  //     Expanded(
                  //       child: Container(
                  //         child: Column(
                  //             crossAxisAlignment: CrossAxisAlignment.start,
                  //             mainAxisSize: MainAxisSize.min,
                  //             children: [
                  //               if (epic_list != null)
                  //                 ...epic_list!.map((e) {
                  //                   return Container(
                  //                     child: Row(
                  //                       children: [
                  //                         Container(
                  //                           padding: const EdgeInsets.all(8),
                  //                           child: Text(e),
                  //                         ),
                  //                         TextButton(
                  //                             onPressed: () => removeEpic(e),
                  //                             child: Text("remove"))
                  //                       ],
                  //                     ),
                  //                   );
                  //                 }).toList(),
                  //               // TextButton(
                  //               //     onPressed: () => addNewEpic(), child: Text('Add New'))
                  //             ]),
                  //       ),
                  //       flex: 2,
                  //     )
                  //   ],
                  // ),
                  // Row(
                  //   children: [
                  //     Expanded(
                  //       child: Text("Influencer"),
                  //       flex: 1,
                  //     ),
                  //     Expanded(
                  //         flex: 2,
                  //         child: Row(
                  //           children: [
                  //             Text('Yes'),
                  //             Radio<bool>(
                  //                 value: true,
                  //                 groupValue: influencer,
                  //                 onChanged: (value) {
                  //                   setState(() {
                  //                     influencer = value;
                  //                   });
                  //                 }),
                  //             SizedBox(
                  //               width: 24,
                  //             ),
                  //             Text('No'),
                  //             Radio<bool>(
                  //                 value: false,
                  //                 groupValue: influencer,
                  //                 onChanged: (value) {
                  //                   setState(() {
                  //                     influencer = value;
                  //                   });
                  //                 }),
                  //           ],
                  //         ))
                  //   ],
                  // ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Beneficiary",
                          style: TextStyle(
                              fontFamily: "Gilroy-SemiBold",
                              fontSize: 16,
                              fontWeight: FontWeight.w400),
                        ),
                        flex: 2,
                      ),
                      Expanded(
                          flex: 2,
                          child: Row(
                            children: [
                              Text('Yes'),
                              Radio<bool>(
                                  value: true,
                                  groupValue: beneficiary,
                                  onChanged: (value) {
                                    setState(() {
                                      beneficiary = value;
                                    });
                                  }),
                              SizedBox(
                                width: 24,
                              ),
                              Text('No'),
                              Radio<bool>(
                                  value: false,
                                  groupValue: beneficiary,
                                  onChanged: (value) {
                                    setState(() {
                                      beneficiary = value;
                                    });
                                  }),
                            ],
                          ))
                    ],
                  ),
                  // Row(
                  //   children: [
                  //     Expanded(
                  //       child: Text("Beneficiary Type"),
                  //       flex: 1,
                  //     ),
                  //     Expanded(
                  //       child: TextField(
                  //         controller: beneficiary_type,
                  //         maxLength: 30,
                  //         // maxLengthEnforcement: MaxLengthEnforcement.enforced,
                  //         decoration: InputDecoration(
                  //           hintText: '',
                  //           counterText: "",
                  //         ),
                  //       ),
                  //       flex: 2,
                  //     )
                  //   ],
                  // ),
                  // SizedBox(
                  //   height: 16,
                  // ),
                  // Row(
                  //   children: [
                  //   Expanded(
                  //     child: Text("Dead"),
                  //     flex: 1,
                  //   ),
                  //   Expanded(
                  //       flex: 2,
                  //       child: Row(
                  //         children: [
                  //           Text('Yes'),
                  //           Radio<bool>(
                  //               value: true,
                  //               groupValue: dead,
                  //               onChanged: (value) {
                  //                 if (value != null)
                  //                   setState(() {
                  //                     dead = value;
                  //                   });
                  //               }),
                  //           SizedBox(
                  //             width: 24,
                  //           ),
                  //           Text('No'),
                  //           Radio<bool>(
                  //               value: false,
                  //               groupValue: dead,
                  //               onChanged: (value) {
                  //                 if (value != null)
                  //                   setState(() {
                  //                     dead = value;
                  //                   });
                  //               }),
                  //         ],
                  //       )),
                  // ]),
                  Row(children: [
                    Expanded(
                      child: Text(
                        "Voted Last",
                        style: TextStyle(
                            fontFamily: "Gilroy-SemiBold",
                            fontSize: 16,
                            fontWeight: FontWeight.w400),
                      ),
                      flex: 2,
                    ),
                    Expanded(
                        flex: 2,
                        child: Row(
                          children: [
                            Text('Yes'),
                            Radio<bool>(
                                value: true,
                                groupValue: voted_last,
                                onChanged: (value) {
                                  if (value != null)
                                    setState(() {
                                      voted_last = value;
                                    });
                                }),
                            SizedBox(
                              width: 24,
                            ),
                            Text('No'),
                            Radio<bool>(
                                value: false,
                                groupValue: voted_last,
                                onChanged: (value) {
                                  if (value != null)
                                    setState(() {
                                      voted_last = value;
                                    });
                                }),
                          ],
                        )),
                  ]),
                  voted_last == true
                      ? Row(
                          children: [
                            Expanded(
                              child: Text(" "),
                              flex: 2,
                            ),
                            Expanded(
                                flex: 2,
                                child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: DropdownButton<String>(
                                      isExpanded: true,
                                      value: selectedValueLastVoted,
                                      onChanged: (String? newValue) {
                                        setState(() {
                                          selectedValueLastVoted = newValue!;
                                        });
                                      },
                                      items: partyName
                                          .map<DropdownMenuItem<String>>(
                                              (value) {
                                        return DropdownMenuItem<String>(
                                          value: value.toString(),
                                          child: Text(value.toString()),
                                        );
                                      }).toList(),
                                    )))
                          ],
                        )
                      : SizedBox(),
                  Row(children: [
                    Expanded(
                      child: Text(
                        "Currently Present",
                        style: TextStyle(
                            fontFamily: "Gilroy-SemiBold",
                            fontSize: 16,
                            fontWeight: FontWeight.w400),
                      ),
                      flex: 2,
                    ),
                    Expanded(
                        flex: 2,
                        child: Row(
                          children: [
                            Text('Yes'),
                            Radio<bool>(
                                value: true,
                                groupValue: currently_present,
                                onChanged: (value) {
                                  if (value != null)
                                    setState(() {
                                      currently_present = value;
                                    });
                                }),
                            SizedBox(
                              width: 24,
                            ),
                            Text('No'),
                            Radio<bool>(
                                value: false,
                                groupValue: currently_present,
                                onChanged: (value) {
                                  if (value != null)
                                    setState(() {
                                      currently_present = value;
                                    });
                                }),
                          ],
                        )),
                  ]),
                  Row(children: [
                    Expanded(
                      child: Text(
                        "Do you take responsibility   of this voter",
                        style: TextStyle(
                            fontFamily: "Gilroy-SemiBold",
                            fontSize: 16,
                            fontWeight: FontWeight.w400),
                      ),
                      flex: 2,
                    ),
                    Expanded(
                        flex: 2,
                        child: Row(
                          children: [
                            Text('Yes'),
                            Radio<bool>(
                                value: true,
                                groupValue: responsibility,
                                onChanged: (value) {
                                  if (value != null)
                                    setState(() {
                                      responsibility = value;
                                    });
                                }),
                            SizedBox(
                              width: 24,
                            ),
                            Text('No'),
                            Radio<bool>(
                                value: false,
                                groupValue: responsibility,
                                onChanged: (value) {
                                  if (value != null)
                                    setState(() {
                                      responsibility = value;
                                    });
                                }),
                          ],
                        )),
                  ]),
                  SizedBox(
                    height: 30,
                  ),
                  ElevatedButton(
                    style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all<Color>(
                      Color(0xff4C3AB4),
                    )),
                    onPressed: () async {
                      String numberValue =
                          phone_tc.text.replaceAll(RegExp(r"\s"), "");
                          if(selectedValue=="Select Option"){
  final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('Select Voting Prefrence'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                          }
                          
 if(voted_last==true) if(selectedValueLastVoted=="Select Option"){
  final snackBar = SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
            content: const Text('Select Voting Prefrence'),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                          }


                      if (_fbForm.currentState!.validate()) {
 
                        var res = await loginService.voterEditionalDataPost(
                            context,
                            selectedValue,
                            int.parse(family_tc.text),
                            influencer!,
                            beneficiary!,
                            beneficiary_type.text,
                            numberValue,
                            dead,
                            voted_last,
                            currently_present,
                            widget.voter['epicNumber'],
                            responsibility,
                            selectedValueLastVoted);

                        if (res['status'] == 200) {
                          // Navigator.of(context, rootNavigator: true)
                          //     .pushAndRemoveUntil(
                          //   MaterialPageRoute(
                          //     builder: (BuildContext context) {
                          //       return KaryakartaHome();
                          //     },
                          //   ),
                          //   (_) => false,
                          // );
                           
                          context.snackBar("Voter Data Added");
                       
                       
                           Navigator.pushReplacement(context,
                                    MaterialPageRoute(
                                        builder: (BuildContext context) {
                                  return KaryakartaHome();
                                }));
                          // Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder:  (context) {
                          //   return KaryakartaHome();
                          // },));
                        } else if (res["status"] == 403) {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return SessionExpiredDialog();
                            },
                          );
                        } else {
                          context.snackBar("Something went wrong!");
                        }
                       }
                    },
                    child: Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(vertical: 15),
                      child: !isLoading
                          ? Text(
                              "SAVE",
                              style: TextStyle(
                                fontFamily: 'Gilroy-Bold',
                                fontSize: 18.0,
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.all(10),
                              child: CircularProgressIndicator(),
                            ),
                    ),
                  ),
                  // else
                ],
              ),
            ),
          ),
        ));
    ;
  }

  void removeEpic(epic) {
    setState(() {
      epic_list?.remove(epic);
    });
  }

  void addNewEpic() async {
    final epic = await showDialog(
        context: context,
        builder: (context) {
          final tc = TextEditingController();

          return Dialog(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Add EPIC No.'),
                  TextField(
                    controller: tc,
                    textCapitalization: TextCapitalization.characters,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text('Cancel')),
                      TextButton(
                          onPressed: () => Navigator.pop(context, tc.text),
                          child: Text('Add'))
                    ],
                  )
                ],
              ),
            ),
          );
        });
    if (epic is String) {
      setState(() {
        if (epic_list == null) epic_list = [];
        epic_list?.add(epic);
      });
    }
  }
}
