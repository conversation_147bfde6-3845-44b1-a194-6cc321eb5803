 

import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/service/login.dart';
import 'package:mla_connect/models/Canditate.dart';
import 'package:mla_connect/models/election.dart';
import 'package:mla_connect/new_ui/analytics/analytics.dart';
import 'package:mla_connect/screens/voters/voter_card.dart';
import 'package:mla_connect/screens/voters/voter_model.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/utils/candidatesDb.dart';
import 'package:mla_connect/widgets/session_expired.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VoterSearchPage extends StatefulWidget {
  const VoterSearchPage({Key? key}) : super(key: key);

  @override
  VoterSearchPageState createState() => VoterSearchPageState();
}

class VoterSearchPageState extends State<VoterSearchPage> {
  final _controller = ScrollController();
  bool isFetchingBottom = false;
  // List<ElectoralProfile> profiles = [];
  List<Candidate> profiles = [];
  SearchResult? lastResult;
  SearchState searchState = SearchState.deafult();

  final textEditingController = TextEditingController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  _scrollListener() {
    final last = lastResult;
    if (last != null &&
        _controller.offset >= (_controller.position.maxScrollExtent - 50) &&
        !_controller.position.outOfRange) {
      if (!isFetchingBottom && !last.pageEnd) {
        _startDataFetch();
      }
    }
  }

  Future _startDataFetch({bool refresh = false}) async {
    if (!refresh) {
      if (mounted)
        setState(() {
          isFetchingBottom = true;
        });
    }
    if (refresh) {
      lastResult = null;
    }
    try {
      final text = textEditingController.text;
      //  SearchResult(nextOffset: );
      final cl = await CandidateDB.instance.loadFirst500Rows();
      SearchResult res = SearchResult(
          profiles: cl, pageEnd: false, nextOffset: cl.last.epic_no);
      await FirebaseCloud.instance.search(text,
          startOffset: lastResult?.nextOffset,
          state: searchState); // profile return
      if (mounted)
        setState(() {
          if (refresh) profiles.clear();
          profiles.addAll(res.profiles);
          lastResult = res;
          isFetchingBottom = false;
        });
    } catch (e) {
      context.snackBar(e.toError());
    } finally {
      if (mounted) {
        setState(() {
          isFetchingBottom = false;
        });
      }
    }
  }

  Future<void> _onRefresh() async {
    await _startDataFetch(refresh: true);
  }

  @override
  void dispose() {
    textEditingController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    //temp
    // testData();
    //--
    _controller.addListener(_scrollListener);
    Future.microtask(() => _refreshIndicatorKey.currentState?.show());
  }

  // testData() async {
  //   DateTime now = DateTime.now();

  //    //print(now.hour.toString() +
  //       ":" +
  //       now.minute.toString() +
  //       ":" +
  //       now.second.toString());

  //   var list = await CandidateDB.instance.searchByName("abdal");
  //   now = DateTime.now();

  //    //print(now.hour.toString() +
  //       ":" +
  //       now.minute.toString() +
  //       ":" +
  //       now.second.toString());

  //    //print("lala");
  // }

  @override
  Widget build(BuildContext context) {
    int itemCount = profiles.length + (isFetchingBottom ? 1 : 0);

    return Scaffold(
      // appBar: AppBar(
      //   centerTitle: true,
      //   title: Text("Electoral Role"),
      //   iconTheme: IconThemeData(
      //     color: Colors.black, //change your color here
      //   ),
      //   elevation: 0,
      // ),
      body: Column(
        children: [
          buildHeader(),
          Expanded(
              child: RefreshIndicator(
                  key: _refreshIndicatorKey,
                  child: ListView.builder(
                    controller: _controller,
                    physics: const AlwaysScrollableScrollPhysics(),
                    itemCount: itemCount,
                    itemBuilder: (context, index) {
                      if (isFetchingBottom && index == itemCount - 1) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      } 
                        else {
                          return Text("VOTER CARD");
                        // return VoterCard(
                        //   profile: profiles[index],
                        // );
                     }
                    },
                  ),
                  onRefresh: _onRefresh))
        ],
      ),
    );
  }

  Widget buildHeader() {
    void search() {
      _refreshIndicatorKey.currentState?.show();
    }

    void showFilter() async {
      final newState = await showModalBottomSheet(
          isScrollControlled: true,
          context: context,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          builder: (context) {
            return Padding(
              padding: MediaQuery.of(context).viewInsets,
              child: _FilterScreen(current: searchState),
            );
          });
      if (newState is SearchState) {
        setState(() {
          searchState = newState;
          search();
        });
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextField(
                  controller: textEditingController,
                  style:  TextStyle(fontFamily: 'Gilroy',
                    fontSize: 18.0,
                    // color: Colors.blueAccent,
                  ),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                    hintText: "Search",
                    border: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: Colors.blueAccent, width: 32.0),
                        borderRadius: BorderRadius.circular(25.0)),
                  )),
            ),
          ),
          IconButton(
            icon: Icon(Icons.filter_list_alt),
            onPressed: () => showFilter(),
          ),
          IconButton(
              onPressed: () => search(), icon: Icon(Icons.search_outlined))
        ],
      ),
    );
  }
}

class _FilterScreen extends StatefulWidget {
  final SearchState current;
  const _FilterScreen({Key? key, required this.current}) : super(key: key);

  @override
  __FilterScreenState createState() => __FilterScreenState();
}

class __FilterScreenState extends State<_FilterScreen> {
  late SearchState state;
  late TextEditingController boothController;
  String? fieldError;

  @override
  void dispose() {
    boothController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    state = widget.current;
    boothController =
        TextEditingController(text: state.filter?.part_no?.toString());
  }

  void applyFilter() {
    final booth = int.tryParse(boothController.text);
    if (booth != null) {
      state = state.copyWith(
          filter: state.filter?.copyWith(part_no: booth) ??
              SearchFilter(part_no: booth));
    }
    Navigator.pop(context, state);
  }

  void checkFieldError() {
    if (state.field.name == false && state.field.booth == false)
      fieldError = "Atleast 1 required";
    else
      fieldError = null;
  }

  @override
  Widget build(BuildContext context) {
     

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Text(
          //   'Search By',
          //   
          // ),
          // if (fieldError != null)
          //   Text(
          //     fieldError!,
          //     style: tt.subtitle2?.copyWith(color: Colors.red),
          //   ),
          // Row(
          //   children: [
          //     Text('Name: ', style: tt.subtitle1),
          //     SizedBox(
          //       width: 16,
          //     ),
          //     Checkbox(
          //         value: state.field.name,
          //         onChanged: (value) {
          //           if (value != null) {
          //             setState(() {
          //               state = state.copyWith(
          //                   field: state.field.copyWith(name: value));
          //               checkFieldError();
          //             });
          //           }
          //         }),
          //   ],
          // ),
          // Row(
          //   children: [
          //     Text('Booth: ', style: tt.subtitle1),
          //     SizedBox(
          //       width: 16,
          //     ),
          //     Checkbox(
          //         value: state.field.booth,
          //         onChanged: (value) {
          //           if (value != null) {
          //             setState(() {
          //               state = state.copyWith(
          //                   field: state.field.copyWith(booth: value));
          //               checkFieldError();
          //             });
          //           }
          //         }),
          //   ],
          // ),
          // Divider(),
          Text(
            'Filters',
           
          ),
          Row(
            children: [
              Text('Gender: ' ),
              SizedBox(
                width: 16,
              ),
              DropdownButton<SearchGender>(
                  value: state.filter?.gender,
                  onChanged: (gender) {
                    setState(() {
                      state = state.copyWith(
                          filter: state.filter?.copyWith(gender: gender) ??
                              SearchFilter(gender: gender));
                    });
                  },
                  items: [
                    DropdownMenuItem(
                        child: Text("Male"), value: SearchGender.Male),
                    DropdownMenuItem(
                        child: Text("Female"), value: SearchGender.Female)
                  ]),
              if (state.filter?.gender != null)
                Container(
                  margin: const EdgeInsets.only(left: 24),
                  child: TextButton(
                      onPressed: () {
                        setState(() {
                          final filter = state.filter
                              ?.copyWith(gender: state.filter?.gender);
                          state =
                              state.copyWith(filter: filter ?? SearchFilter());
                        });
                      },
                      child: Text('clear')),
                )
            ],
          ),
          Row(
            children: [
              Text('Booth No: ',  ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                child: TextField(
                    controller: boothController,
                    maxLength: 6,
                    maxLengthEnforcement: MaxLengthEnforcement.enforced,
                    keyboardType: TextInputType.number),
              )
            ],
          ),
          Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Cancel')),
              TextButton(
                  onPressed: fieldError == null ? () => applyFilter() : null,
                  child: Text('Apply')),
            ],
          )
        ],
      ),
    );
  }
}
class KaryakartaTeam extends StatefulWidget {
  const KaryakartaTeam({Key? key}) : super(key: key);

  @override
  State<KaryakartaTeam> createState() => _KaryakartaTeamState();
}

class _KaryakartaTeamState extends State<KaryakartaTeam> { 
  LoginService login = LoginService() ;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fetchkaryakarta ();
  }
 var kk ;
 String? role ;
 String boothId='' ;
   var filteredUsers = [];
  fetchkaryakarta() async{
   
     var ress = await login.karyakartaList(context);
      kk =ress['res'] ;
      print(kk);
 if(ress['status'] ==403) {
 showDialog(
                context: context,
                builder: (BuildContext context) {
                  return SessionExpiredDialog();
                },
              );
 }
      //print(kk);
       SharedPreferences prefs = await SharedPreferences.getInstance();
    
     role = prefs.getString('role');
       boothId =prefs.getString('boothId')!;
        //print(kk);
       filteredUsers =[] ;
     int kkf=kk['users'].length ;
    //  print(kk['users'].length);
    //   //print("length");
    //   //print(kkf);
    //   //print(boothId);
    for(int i=0 ;i <=  kkf - 1 ; i++){
       //print(i);
       //print(kk['users'][i]);
      if(boothId==kk['users'][i]['boothId'].toString()){
        filteredUsers.add(kk['users'][i]);
      }
    }
     print("filteredUsers");
       print(filteredUsers);
     
  //  //print("filteredUsers  $filteredUsers");
     setState(() {
       
     }); 
      
  }

  
  var boothData ={};
  @override
  Widget build(BuildContext context) {
        //  fetchkaryakarta();
    return   Column(
        children: [
          SizedBox(height: 10,),
        kk==null ? Center(child:CircularProgressIndicator()): SizedBox(
          height: MediaQuery.of(context).size.height*0.71,
            child:
             role== "KARYAKARTA" ? ListView.builder(
              itemCount:filteredUsers.length,
              itemBuilder: (context, index) {
             
                  //print(filteredUsers[index]['users']);
                //  //print('Name: ${kk['users'][index]['name'].toString()}');
                return ListTile(
                  title: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                         
                      border: Border.all(
                        color: Color(0xffD7D7D7), // Border color
                        width: 1.0, // Border width
                      ),
                      borderRadius:
                          BorderRadius.circular(5), // Optional: border radius
                    ),
                    // color:Colors.purple ,
                    padding: EdgeInsets.all(10),
                    child: Column(
                      children: [
                        
                        Row(
                          children: [
                            Container(
                  width:  MediaQuery.of(context).size.width*0.2   ,
                  height:MediaQuery.of(context).size.width*0.2  ,
                  decoration: BoxDecoration(
                   
                    shape: BoxShape.circle,
                    border: Border.all(width: 2, color: Color(0xff4C3AB4)),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(filteredUsers[index]['profileImgUrl']  ),
                      fit: BoxFit.cover,
                    ),
                  )) ,
                            // CachedNetworkImage(
                            //   imageUrl: filteredUsers[index]['profileImgUrl'],
                            //   // imageBuilder: (context, imageProvider) => Container(child: ,),
                            //   // progressIndicatorBuilder: (context, url, downloadProgress) =>
                            //   //     CircularProgressIndicator(value: downloadProgress.progress),
                            //   errorWidget: (context, url, error) =>
                            //       Icon(Icons.error),
                            //   width: 50,
                            //   fit: BoxFit.fill,
                            // ),
                            SizedBox(
                              width: 30,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text('Name: ', style: TextStyle(fontFamily: "Gilroy-Bold" , fontWeight: FontWeight.bold),),
                                    Text('${filteredUsers[index]['name'].toString()}'),
                                  ],
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  children: [
                                    Text('Number: ', style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontWeight: FontWeight.bold)),
                                    Text('${filteredUsers[index]['number']}'),
                                  ],
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  children: [
                                    Text('Booth No : ', style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontWeight: FontWeight.bold)),
                                     Text('${'${filteredUsers[index]['blockId']}'}'),
                                  ],
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  children: [
                                     Text('Role :  ', style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontWeight: FontWeight.bold)),
                                    Text('${filteredUsers[index]['role']}'),
                                  ],
                                ),
                                Divider(),
                                 
                              ],
                            ),
                          ],
                        ),
                      ],
                    
                    ) ));})
                  : 
                  ListView.builder(
                                itemCount:kk['users'].length,
                                itemBuilder: (context, index) {
                  
                               print(kk['users'].length);
                                  return ListTile(
                  title:
                   Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                       color: Colors.white,
                      border: Border.all(
                        color: Colors.grey, // Border color
                        width: 1.0, // Border width
                      ),
                      borderRadius:
                          BorderRadius.circular(5), // Optional: border radius
                    ),
                    padding: EdgeInsets.all(20),
                    child: Column(
                      children: [
                  
                        Column(
                          children: [
                        Row(
                          children: [
                                        Container(
                  width:  MediaQuery.of(context).size.width*0.2   ,
                  height:MediaQuery.of(context).size.width*0.2  ,
                  decoration: BoxDecoration(
                  
                    shape: BoxShape.circle,
                    border: Border.all(width: 2, color: Color(0xff4C3AB4)),
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(kk['users'][index]['profileImgUrl'] ),
                    ),
                  )) ,
                            
                            SizedBox(
                              width: 30,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Text('Name: ${kk['users'][index]['name'].toString()}'),
                                // SizedBox(
                                //   height: 3,
                                // ),
                                // Text('Number: ${kk['users'][index]['number']}'),
                                // SizedBox(
                                //   height: 3,
                                // ),
                                // Text('Booth No : ${kk['users'][index]['boothId']}'),
                                // SizedBox(
                                //   height: 3,
                                // ),
                                // Text('Role : ${kk['users'][index]['role']}'),
                        
                             //
                              Row(
                                  children: [
                                    Text('Name: ', style: TextStyle(fontFamily: "Gilroy-Bold" , fontWeight: FontWeight.bold),),
                                    Text('${kk['users'][index]['name'].toString()}'),
                                  ],
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                // Row(
                                //   children: [
                                //     Text('Number: ', style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontWeight: FontWeight.bold)),
                                //     Text('${filteredUsers[index]['number']}'),
                                //   ],
                                // ),
                                SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  children: [
                                    Text('Booth No : ', style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontWeight: FontWeight.bold)),
                                     Text('${kk['users'][index]['boothId']}'),
                                  ],
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  children: [
                                     Text('Role :  ', style: TextStyle(fontFamily: "Gilroy-SemiBold" , fontWeight: FontWeight.bold)),
                                    Text('${kk['users'][index]['role']}'),
                                  ],
                                ),
                                        ],),
                                
                              ],
                            ),
                          ],
                        ),
                           Divider(),
                            
                        Row(
                                  children: [
                                     SizedBox(
                                      height: MediaQuery.of(context).size.width*0.12,
                        width: MediaQuery.of(context).size.width*0.37,
                                       child: ElevatedButton(
                                         style: ButtonStyle(
                                             backgroundColor:
                                                 MaterialStateColor.resolveWith(
                                                     (states) => Color(0xffAE3120))),
                                         onPressed: () async {
                                          showDialog(
                                             context: context,
                                             builder: (BuildContext context) {
                                               return Dialog(
                                                   shape: RoundedRectangleBorder(
                                                     borderRadius:
                                                         BorderRadius.circular(10.0),
                                                   ),
                                                   child: Container( 
                                                     padding: EdgeInsets.all(20),
                                                     height: MediaQuery.of(context).size.height*0.2,
                                                     color: Colors.white,
                                                     child: Column(
                                                       children: [
                                                         Text(
                                                             "are you sure. you want to delete ${kk['users'][index]['name'].toString()} karyakarta. " ,style:  TextStyle(fontFamily: 'Gilroy',fontWeight: FontWeight.bold),),
                                                         SizedBox(
                                                           height: 20,
                                                         ),
                                                         Row(
                                                           mainAxisAlignment:
                                                               MainAxisAlignment
                                                                   .spaceEvenly,
                                                           children: [
                                                             ElevatedButton(
                                                               onPressed: () async {
                                                                 var karyakartadelete =
                                                                     await login
                                                                         .deleteKaryaKarta(
                                                                             context,
                                                                             kk['users'][index]
                                                                                 [
                                                                                 'userId']);
                                                                                  //print(karyakartadelete);
                   if(karyakartadelete['status']==200){
                    Navigator.pop(context);
                   }else if (karyakartadelete["status"] == 403){
                    showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                  return SessionExpiredDialog();
                                  },
                                );
                        }
                   else{
                           final snackBar = SnackBar(
                            behavior: SnackBarBehavior.floating,
                            margin: EdgeInsets.fromLTRB(15, 5, 15, 30),
                            content: const Text('Something went wrong!'),
                          );
                          ScaffoldMessenger.of(context).showSnackBar(snackBar);
                   }
                                                                 // Navigator.push(context, MaterialPageRoute(builder:  (context) {
                                                                 //   return CreatePoster();
                                                                 // },));
                                                               },
                                                               style: ElevatedButton
                                                                   .styleFrom(
                                                                 backgroundColor:
                                                                     Colors.green,
                                                               ),
                                                               child: Text('Yes'),
                                                             ),
                                                             ElevatedButton(
                                                               onPressed: () async {
                                                                 Navigator.pop(context);
                                                               },
                                                               style: ElevatedButton
                                                                   .styleFrom(
                                                                 backgroundColor: Color(0xffAE3120),
                                                               ),
                                                               child: Text('No'),
                                                             ),
                                                           ],
                                                         ),
                                                       ],
                                                     ),
                                                   ));
                                             },
                                           );
                  
                                           //
                  
                                           //  //print(karyakartaListData);
                                         },
                                         child: Text('Delete karyakarta' , style: TextStyle(color: Colors.white),),
                                       ),
                                     ),
                                           Spacer(),
                                    SizedBox(
                                      height: MediaQuery.of(context).size.width*0.12,
                        width: MediaQuery.of(context).size.width*0.37,
                                      child: ElevatedButton(
                                        style: ButtonStyle(
                                            backgroundColor:
                                                MaterialStateColor.resolveWith(
                                                    (states) => Colors.green)),
                                        onPressed: () async {
                                          Navigator.push(context, MaterialPageRoute(builder:  (context) {
                        return PBChart(boothNumber:  kk['users'][index]['boothId']!) ;
                      },));
                  
                                          //
                  
                                          //  //print(karyakartaListData);
                                        },
                                        child: Text('Work analytics', style: TextStyle(color: Colors.white),),
                                      ),
                                    ),
                         
                                   
                                  ],
                                ),
                               role!="MANAGER"  ? SizedBox(
                                  width: double.infinity,
                                   child: ElevatedButton(
                                     style: ButtonStyle(
                                         backgroundColor:
                                             MaterialStateColor.resolveWith(
                                                 (states) => Color(0xff4C3AB4))),
                                     onPressed: () async {
                                        var isSucess =
                                await login.AssignkaryakartaToManager(
                                    context,
                                    kk['users'][index]['number'],
                                    kk['users'][index]['boothId'],
                                    kk['users'][index]['blockId'],
                                    kk['users'][index]['userId'] ,
                                    "MANAGER");
                            if (isSucess['status'] == 200) {
                               //print("assigned");
                            }else if (isSucess["status"] == 403){
                    showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                  return SessionExpiredDialog();
                                  },
                                );
                        } else {
                             ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Something went worng'),
                          ),
                          
                        );
                            }
                            } ,
                                     child: Text('Assign as a manager', style: TextStyle(color: Colors.white),),
                                   ),
                                 ) :SizedBox(),
                      ],
                    ),
                  ),
                                  );
                                },
                              ),
          )
        ],
      );
  }
}