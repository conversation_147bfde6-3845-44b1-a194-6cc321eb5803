import 'package:copy_with_extension/copy_with_extension.dart';
part 'voter_model.g.dart';

@CopyWith()
class SearchState {
  final SearchField field;
  final SearchFilter? filter;

  SearchState({required this.field, required this.filter});

  factory SearchState.deafult() {
    return SearchState(
        field: SearchField(name: true, booth: false), filter: null);
  }

  void writeToJson(Map<String, dynamic> json) {
    final fld = Map<String, dynamic>();
    fld['name'] = this.field.name;
    fld['booth'] = this.field.booth;

    final fil = Map<String, dynamic>();
    if (this.filter?.gender != null) fil['gender'] = this.filter?.gender?.value;
    if (this.filter?.part_no != null) fil['part_no'] = this.filter?.part_no;

    if (fld.isNotEmpty) {
      json['search_fields'] = fld;
    }
    if (fil.isNotEmpty) {
      json['search_filters'] = fil;
    }
  }
}

@CopyWith()
class SearchField {
  final bool name;
  final bool booth;

  SearchField({required this.name, required this.booth});
}

@CopyWith(copyWithNull: true)
class SearchFilter {
  final SearchGender? gender;
  final int? part_no;

  SearchFilter({this.gender, this.part_no});
}

enum SearchGender { Male, Female }

extension _Stringify on SearchGender {
  static const values = ['M', 'F'];
  String get value => values[this.index];
}
