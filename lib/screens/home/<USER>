import 'package:cached_network_image/cached_network_image.dart';
import 'package:mla_connect/controller/reward_controller.dart';
import 'package:mla_connect/models/LeaderboardResult.dart';
import 'package:mla_connect/services/firebase_cloud.dart';
import 'package:mla_connect/widgets/progress.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get/instance_manager.dart';

class LeaderBoardBox extends StatefulWidget {
  const LeaderBoardBox({Key? key}) : super(key: key);

  @override
  State<LeaderBoardBox> createState() => _LeaderBoardBoxState();
}

class _LeaderBoardBoxState extends State<LeaderBoardBox> {
  LeaderboardResult? _leaderboardResult;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getleaderBoardData();
  }

  bool loading = true;
  RewardController? _rewardController;
  getleaderBoardData() async {
    _leaderboardResult = await FirebaseCloud.instance.getLeaderboard();
    _rewardController = Get.isRegistered<RewardController>()
        ? Get.find<RewardController>()
        : Get.put(RewardController());

    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return loading
        ? PI1()
        : Container(
            height: 250,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              boxShadow: kElevationToShadow[2],
              image: DecorationImage(
                image: AssetImage('assets/background/leaderboard_bk.jpeg'),
                fit: BoxFit.fill,
              ),
            ),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Container(
                      margin: EdgeInsets.only(top: 35),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Stack(
                            children: [
                              Image.asset(
                                'assets/background/star_background.png',
                                height: 120,
                                // width: 50,
                              ),
                              Positioned.fill(
                                  child: Align(
                                      alignment: Alignment.center,
                                      child: CircleAvatar(
                                          radius: 50,
                                          // backgroundColor: Colors.white,
                                          backgroundImage:
                                              CachedNetworkImageProvider(
                                                  _leaderboardResult!
                                                          .leaderBoard![1]
                                                          .profileUrl ??
                                                      "https://enphamedbiotech.com/wp-content/uploads/2021/02/article_51_7-18-20181-15-10PM.jpg")))),
                              Positioned.fill(
                                child: Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Image.asset(
                                      "assets/icons/2.png",
                                      height: 40,
                                    )),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          Text(_leaderboardResult!.leaderBoard![1].name),
                          Center(
                              child: Text(
                            _leaderboardResult!.leaderBoard![1].points
                                    .toString() +
                                " Points",
                            style:  TextStyle(fontFamily: 'Gilroy',
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          ))
                        ],
                      )),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Stack(
                        children: [
                          Image.asset(
                            'assets/background/star_background.png',
                            height: 120,
                            // width: 50,
                          ),
                          Positioned.fill(
                              child: Align(
                                  alignment: Alignment.center,
                                  child: CircleAvatar(
                                      radius: 50,
                                      // backgroundColor: Colors.white,
                                      backgroundImage: CachedNetworkImageProvider(
                                          _leaderboardResult!
                                                  .leaderBoard![0].profileUrl ??
                                              "https://enphamedbiotech.com/wp-content/uploads/2021/02/article_51_7-18-20181-15-10PM.jpg")))),
                          Positioned.fill(
                            child: Align(
                                alignment: Alignment.bottomCenter,
                                child: Image.asset(
                                  "assets/icons/1.png",
                                  height: 40,
                                )),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      Text(_leaderboardResult!.leaderBoard![0].name),
                      Center(
                          child: Text(
                        _leaderboardResult!.leaderBoard![0].points.toString() +
                            " Points",
                        style:  TextStyle(fontFamily: 'Gilroy',
                            color: Colors.white, fontWeight: FontWeight.bold),
                      ))
                    ],
                  ),
                  Container(
                      margin: EdgeInsets.only(top: 35),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Stack(
                            children: [
                              Image.asset(
                                'assets/background/star_background.png',
                                height: 120,
                                // width: 50,
                              ),
                              Positioned.fill(
                                  child: Align(
                                      alignment: Alignment.center,
                                      child: CircleAvatar(
                                          radius: 50,
                                          // backgroundColor: Colors.white,
                                          backgroundImage:
                                              CachedNetworkImageProvider(
                                                  _leaderboardResult
                                                          ?.leaderBoard![2]
                                                          .profileUrl ??
                                                      "https://enphamedbiotech.com/wp-content/uploads/2021/02/article_51_7-18-20181-15-10PM.jpg")))),
                              Positioned.fill(
                                child: Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Image.asset(
                                      "assets/icons/3.png",
                                      height: 40,
                                    )),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          Text(_leaderboardResult!.leaderBoard![2].name),
                          Center(
                              child: Text(
                            _leaderboardResult!.leaderBoard![2].points
                                    .toString() +
                                " Points",
                            style:  TextStyle(fontFamily: 'Gilroy',
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          ))
                        ],
                      ))
                ]),
          );
  }
}
