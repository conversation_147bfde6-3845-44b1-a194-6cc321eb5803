 
import 'package:mla_connect/screens/home/<USER>';
import 'package:mla_connect/screens/poster/home_poster_carousel.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Column(
      children: [
        HomePosterCarousel(),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 10,
        ),
        LeaderBoardBox(),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 10,
        ),
        // Feedpage()
      ],
    ));
  }
}
