name: mla_connect
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  dio: 
  social_share_plugin: ^0.4.1+2
  dartz: ^0.10.0
  sembast: ^3.1.0+2
  path_provider: ^2.0.2
  sqflite: ^2.4.0
  path: ^1.8.3
  fluttertoast: ^8.0.8
  firebase_core: ^3.6.0
  url_launcher: ^6.0.9
  lottie: ^3.1.3 
  firebase_auth:  
  flutter_social_share_plugin: ^2.0.2
  # flutter_share_me: ^1.4.0     
  firebase_storage: ^12.3.3
  pin_code_fields: ^8.0.1
  cloud_firestore: ^5.4.4
  flutter_linkify: ^6.0.0
  cloud_functions: ^5.1.3
  copy_with_extension: ^5.0.0
  share_plus: ^10.1.0
  persistent_bottom_nav_bar: ^6.2.1
  image_picker: ^1.1.2
  cached_network_image: ^3.1.0
  flutter_cache_manager: ^3.1.2
  file_picker: ^8.1.2
  syncfusion_flutter_core: ^27.1.53
  syncfusion_flutter_pdfviewer: ^27.1.53
  syncfusion_flutter_pdf: ^27.1.53
  uuid: ^4.5.1
  flutter_lints: 
  json_annotation: ^4.8.0 
  image: ^4.3.0
  photo_view: ^0.15.0
  shared_preferences: ^2.0.12
  flutter_colorpicker: ^1.0.3
  tuple: ^2.0.0
  equatable: ^2.0.3
  flutter_contacts: ^1.1.2
  provider: ^6.0.2
  device_info_plus:  
  pdf: ^3.8.4
  printing:  
  intl: ^0.19.0
  infinite_carousel: ^1.0.2
  flutter_phoenix: ^1.1.1
  restart_app: ^1.1.0+1
  get: ^4.6.5
  dotted_border:
  firebase_analytics:
  confetti: ^0.8.0
  video_player: ^2.6.0
  im_stepper: ^1.0.1+1
  indexed: ^0.0.8
  html: ^0.15.4
  # easy_upi_payment: 
  sign_in_button: ^3.2.0
  http: 
  # upi_india: ^3.0.1
  firebase_app_check:  
  facebook_app_events: ^0.19.0
  syncfusion_flutter_charts:
  google_sign_in: ^6.2.1
  # cached_video_player: ^2.0.4
  # added git profile because release build was failing
  # cached_video_player:
  #   git:
  #     url: https://github.com/vikram25897/flutter_cached_video_player
  #     ref: feature/gradle_version_bump
  # webview_flutter: ^4.0.6 chal to ra hai lekin height ni dera chodu
  # twitter_oembed_api: ^0.1.0 information to dera hai lekin karu kya gaand me bharu ?
  # tweet_ui: 3.0.0 install ni hua coz of photo ki kuch bakchodi
  # webview_flutter_plus: ^0.3.0+2
  flutter_inappwebview: ^6.1.5
  photo_gallery: 
   git:
      url: https://github.com/DeuMiAn/photo_gallery.git
      ref: master
  permission_handler: ^11.3.1
  transparent_image: ^2.0.1
  # flutter_social_content_share: ^1.0.2
  visibility_detector: ^0.4.0+2
  image_cropper: ^8.0.2
  firebase_messaging: ^15.1.3
  razorpay_flutter: ^1.3.5
  flutter_svg:  
  # custom_linear_progress_indicator:  
   
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.3.3
  json_serializable: ^6.6.1
  flutter_toggle_tab: ^1.2.0
  copy_with_extension_gen: ^5.0.0
  
  

scripts:
  build: flutter pub run build_runner build --delete-conflicting-outputs
  watch: flutter pub run build_runner watch --delete-conflicting-outputs

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/
    - assets/icons/
    - assets/background/
    - assets/icons/bottombar/
    - assets/animations/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Gilroy
      fonts:
        - asset: assets/fonts/Gilroy-Light.ttf
          weight: 200
        - asset: assets/fonts/Gilroy-Medium.ttf
          weight: 400
        - asset: assets/fonts/Gilroy-Regular.ttf
          weight: 600
        - asset: assets/fonts/Gilroy-SemiBold.ttf
          weight: 800
        - asset: assets/fonts/Gilroy-Bold.ttf
          weight: 900