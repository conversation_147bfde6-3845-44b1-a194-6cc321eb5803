# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# FVM Version Cache
.fvm/

# Firebase Configuration Files (SENSITIVE - DO NOT COMMIT)
android/app/google-services.json
ios/Runner/GoogleService-Info.plist
lib/firebase_options.dart

# Environment and Configuration Files
.env
.env.local
.env.production
.env.staging
android/local.properties
android/key.properties

# API Keys and Secrets
**/secrets/
**/config/secrets.dart
**/config/api_keys.dart

# Build outputs
build/
.dart_tool/
.packages

# IDE
.vscode/
.idea/

# macOS
.DS_Store

# Temporary files
*.tmp
*.temp